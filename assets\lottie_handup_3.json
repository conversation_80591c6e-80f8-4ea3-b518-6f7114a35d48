{"v": "5.6.10", "fr": 30, "ip": 85, "op": 90, "w": 174, "h": 98, "nm": "摇手", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 3, "ty": 4, "nm": "“图层 7”轮廓 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [89.6, 127.3, 0], "to": [0, -10.083, 0], "ti": [0, 10.083, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 4, "s": [89.6, 66.8, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 86, "s": [89.6, 66.8, 0], "to": [0, 10.083, 0], "ti": [0, -10.083, 0]}, {"t": 90, "s": [89.6, 127.3, 0]}], "ix": 2}, "a": {"a": 0, "k": [14.6, 14.6, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7, 17.801], [2.599, 27.4], [12.2, 17.801], [2.599, 8.2]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7.75, 17.451], [1.849, 27.05], [11.45, 17.451], [1.849, 7.85]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7, 17.801], [2.599, 27.4], [12.2, 17.801], [2.599, 8.2]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-6.125, 17.802], [3.474, 27.401], [13.075, 17.802], [3.474, 8.201]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 22, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7, 17.801], [2.599, 27.4], [12.2, 17.801], [2.599, 8.2]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7.75, 17.451], [1.849, 27.05], [11.45, 17.451], [1.849, 7.85]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7, 17.801], [2.599, 27.4], [12.2, 17.801], [2.599, 8.2]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 31, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-6.125, 17.802], [3.474, 27.401], [13.075, 17.802], [3.474, 8.201]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 34, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7, 17.801], [2.599, 27.4], [12.2, 17.801], [2.599, 8.2]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 37, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7.75, 17.451], [1.849, 27.05], [11.45, 17.451], [1.849, 7.85]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7, 17.801], [2.599, 27.4], [12.2, 17.801], [2.599, 8.2]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 43, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-6.125, 17.802], [3.474, 27.401], [13.075, 17.802], [3.474, 8.201]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 46, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7, 17.801], [2.599, 27.4], [12.2, 17.801], [2.599, 8.2]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7.75, 17.451], [1.849, 27.05], [11.45, 17.451], [1.849, 7.85]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7, 17.801], [2.599, 27.4], [12.2, 17.801], [2.599, 8.2]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-6.125, 17.802], [3.474, 27.401], [13.075, 17.802], [3.474, 8.201]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 58, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7, 17.801], [2.599, 27.4], [12.2, 17.801], [2.599, 8.2]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7.75, 17.451], [1.849, 27.05], [11.45, 17.451], [1.849, 7.85]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7, 17.801], [2.599, 27.4], [12.2, 17.801], [2.599, 8.2]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 67, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-6.125, 17.802], [3.474, 27.401], [13.075, 17.802], [3.474, 8.201]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 70, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7, 17.801], [2.599, 27.4], [12.2, 17.801], [2.599, 8.2]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 73, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7.75, 17.451], [1.849, 27.05], [11.45, 17.451], [1.849, 7.85]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7, 17.801], [2.599, 27.4], [12.2, 17.801], [2.599, 8.2]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 79, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-6.125, 17.802], [3.474, 27.401], [13.075, 17.802], [3.474, 8.201]], "c": true}]}, {"t": 82, "s": [{"i": [[0, -5.295], [-5.293, 0], [0, 5.293], [5.293, 0]], "o": [[0, 5.292], [5.293, 0], [0, -5.292], [-5.292, 0]], "v": [[-7, 17.801], [2.599, 27.4], [12.2, 17.801], [2.599, 8.2]], "c": true}]}], "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.956862747669, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12, -3.2], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "“图层 8”轮廓 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [87.2, 128.5, 0], "to": [0, -10.083, 0], "ti": [0, 10.083, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 4, "s": [87.2, 68, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 86, "s": [87.2, 68, 0], "to": [0, 10.083, 0], "ti": [0, -10.083, 0]}, {"t": 90, "s": [87.2, 128.5, 0]}], "ix": 2}, "a": {"a": 0, "k": [24.2, 35, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [{"i": [[0, -2.781], [0, 0], [0, 0], [-1.405, -2.792], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0, 7.081], [0, 0], [0, 0], [2.846, 0]], "o": [[0, 0], [0, 0], [0, 3.111], [0.659, 1.309], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [0, -2.781], [-2.844, 0]], "v": [[-19, -8.454], [-19, 3.63], [-19, 15.685], [-16.851, 24.703], [-15.842, 28.937], [-15.842, 45.801], [-15.128, 46.5], [9.079, 46.5], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.51, 45.801], [10.51, 46.5], [18.684, 46.5], [19.401, 45.801], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-8.681, 15.684], [-8.681, 3.255], [-8.681, -8.454], [-13.84, -13.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[-1.125, -2.921], [0, 0], [0, 0], [-1.352, -3], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [2.931, 6.816], [0, 0], [0, 0], [3.318, -0.223]], "o": [[0, 0], [0, 0], [1.5, 3.753], [0.602, 1.336], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [-1.569, -3.359], [-2.785, 0.188]], "v": [[-27.562, -3.516], [-24.25, 5.641], [-20.75, 15.685], [-16.788, 24.687], [-15.85, 28.937], [-15.824, 45.801], [-15.11, 46.5], [9.105, 46.489], [9.079, 36.718], [9.794, 36.018], [10.51, 36.718], [10.51, 39.516], [10.536, 45.79], [10.536, 46.489], [18.71, 46.489], [19.427, 45.79], [19.401, 33.968], [11.577, 26.603], [0.993, 26.603], [-10.181, 15.434], [-14.987, 3.641], [-18.243, -4.516], [-24.84, -9.562]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[0, -2.781], [0, 0], [0, 0], [-1.405, -2.792], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0, 7.081], [0, 0], [0, 0], [2.846, 0]], "o": [[0, 0], [0, 0], [0, 3.111], [0.659, 1.309], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [0, -2.781], [-2.844, 0]], "v": [[-19, -8.454], [-19, 3.63], [-19, 15.685], [-16.851, 24.703], [-15.842, 28.937], [-15.842, 45.801], [-15.128, 46.5], [9.079, 46.5], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.51, 45.801], [10.51, 46.5], [18.684, 46.5], [19.401, 45.801], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-8.681, 15.684], [-8.681, 3.255], [-8.681, -8.454], [-13.84, -13.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [{"i": [[1.812, -2.329], [0.737, -3.689], [-0.004, -3.307], [-1.024, -2.891], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0.306, 7.566], [-0.118, 3.255], [0, 0], [3.215, -0.125]], "o": [[-1.812, 2.329], [-0.759, 3.799], [0.004, 3.012], [0.489, 1.381], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0.118, -3.255], [1.806, -2.921], [-2.842, 0.11]], "v": [[-10.438, -4.895], [-16.421, 6.689], [-17.184, 15.619], [-16.898, 24.641], [-15.815, 28.937], [-15.756, 45.77], [-15.042, 46.469], [9.138, 46.469], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.569, 45.77], [10.569, 46.469], [18.743, 46.469], [19.46, 45.77], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-7.681, 15.993], [-7.849, 9.314], [0.444, -6.02], [-2.715, -11.628]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 22, "s": [{"i": [[0, -2.781], [0, 0], [0, 0], [-1.405, -2.792], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0, 7.081], [0, 0], [0, 0], [2.846, 0]], "o": [[0, 0], [0, 0], [0, 3.111], [0.659, 1.309], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [0, -2.781], [-2.844, 0]], "v": [[-19, -8.454], [-19, 3.63], [-19, 15.685], [-16.851, 24.703], [-15.842, 28.937], [-15.842, 45.801], [-15.128, 46.5], [9.079, 46.5], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.51, 45.801], [10.51, 46.5], [18.684, 46.5], [19.401, 45.801], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-8.681, 15.684], [-8.681, 3.255], [-8.681, -8.454], [-13.84, -13.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[-1.125, -2.921], [0, 0], [0, 0], [-1.352, -3], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [2.931, 6.816], [0, 0], [0, 0], [3.318, -0.223]], "o": [[0, 0], [0, 0], [1.5, 3.753], [0.602, 1.336], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [-1.569, -3.359], [-2.785, 0.188]], "v": [[-27.562, -3.516], [-24.25, 5.641], [-20.75, 15.685], [-16.788, 24.687], [-15.85, 28.937], [-15.824, 45.801], [-15.11, 46.5], [9.105, 46.489], [9.079, 36.718], [9.794, 36.018], [10.51, 36.718], [10.51, 39.516], [10.536, 45.79], [10.536, 46.489], [18.71, 46.489], [19.427, 45.79], [19.401, 33.968], [11.577, 26.603], [0.993, 26.603], [-10.181, 15.434], [-14.987, 3.641], [-18.243, -4.516], [-24.84, -9.562]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [{"i": [[0, -2.781], [0, 0], [0, 0], [-1.405, -2.792], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0, 7.081], [0, 0], [0, 0], [2.846, 0]], "o": [[0, 0], [0, 0], [0, 3.111], [0.659, 1.309], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [0, -2.781], [-2.844, 0]], "v": [[-19, -8.454], [-19, 3.63], [-19, 15.685], [-16.851, 24.703], [-15.842, 28.937], [-15.842, 45.801], [-15.128, 46.5], [9.079, 46.5], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.51, 45.801], [10.51, 46.5], [18.684, 46.5], [19.401, 45.801], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-8.681, 15.684], [-8.681, 3.255], [-8.681, -8.454], [-13.84, -13.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 31, "s": [{"i": [[1.812, -2.329], [0.737, -3.689], [-0.004, -3.307], [-1.024, -2.891], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0.306, 7.566], [-0.118, 3.255], [0, 0], [3.215, -0.125]], "o": [[-1.812, 2.329], [-0.759, 3.799], [0.004, 3.012], [0.489, 1.381], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0.118, -3.255], [1.806, -2.921], [-2.842, 0.11]], "v": [[-10.438, -4.895], [-16.421, 6.689], [-17.184, 15.619], [-16.898, 24.641], [-15.815, 28.937], [-15.756, 45.77], [-15.042, 46.469], [9.138, 46.469], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.569, 45.77], [10.569, 46.469], [18.743, 46.469], [19.46, 45.77], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-7.681, 15.993], [-7.849, 9.314], [0.444, -6.02], [-2.715, -11.628]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 34, "s": [{"i": [[0, -2.781], [0, 0], [0, 0], [-1.405, -2.792], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0, 7.081], [0, 0], [0, 0], [2.846, 0]], "o": [[0, 0], [0, 0], [0, 3.111], [0.659, 1.309], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [0, -2.781], [-2.844, 0]], "v": [[-19, -8.454], [-19, 3.63], [-19, 15.685], [-16.851, 24.703], [-15.842, 28.937], [-15.842, 45.801], [-15.128, 46.5], [9.079, 46.5], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.51, 45.801], [10.51, 46.5], [18.684, 46.5], [19.401, 45.801], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-8.681, 15.684], [-8.681, 3.255], [-8.681, -8.454], [-13.84, -13.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 37, "s": [{"i": [[-1.125, -2.921], [0, 0], [0, 0], [-1.352, -3], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [2.931, 6.816], [0, 0], [0, 0], [3.318, -0.223]], "o": [[0, 0], [0, 0], [1.5, 3.753], [0.602, 1.336], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [-1.569, -3.359], [-2.785, 0.188]], "v": [[-27.562, -3.516], [-24.25, 5.641], [-20.75, 15.685], [-16.788, 24.687], [-15.85, 28.937], [-15.824, 45.801], [-15.11, 46.5], [9.105, 46.489], [9.079, 36.718], [9.794, 36.018], [10.51, 36.718], [10.51, 39.516], [10.536, 45.79], [10.536, 46.489], [18.71, 46.489], [19.427, 45.79], [19.401, 33.968], [11.577, 26.603], [0.993, 26.603], [-10.181, 15.434], [-14.987, 3.641], [-18.243, -4.516], [-24.84, -9.562]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [{"i": [[0, -2.781], [0, 0], [0, 0], [-1.405, -2.792], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0, 7.081], [0, 0], [0, 0], [2.846, 0]], "o": [[0, 0], [0, 0], [0, 3.111], [0.659, 1.309], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [0, -2.781], [-2.844, 0]], "v": [[-19, -8.454], [-19, 3.63], [-19, 15.685], [-16.851, 24.703], [-15.842, 28.937], [-15.842, 45.801], [-15.128, 46.5], [9.079, 46.5], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.51, 45.801], [10.51, 46.5], [18.684, 46.5], [19.401, 45.801], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-8.681, 15.684], [-8.681, 3.255], [-8.681, -8.454], [-13.84, -13.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 43, "s": [{"i": [[1.812, -2.329], [0.737, -3.689], [-0.004, -3.307], [-1.024, -2.891], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0.306, 7.566], [-0.118, 3.255], [0, 0], [3.215, -0.125]], "o": [[-1.812, 2.329], [-0.759, 3.799], [0.004, 3.012], [0.489, 1.381], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0.118, -3.255], [1.806, -2.921], [-2.842, 0.11]], "v": [[-10.438, -4.895], [-16.421, 6.689], [-17.184, 15.619], [-16.898, 24.641], [-15.815, 28.937], [-15.756, 45.77], [-15.042, 46.469], [9.138, 46.469], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.569, 45.77], [10.569, 46.469], [18.743, 46.469], [19.46, 45.77], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-7.681, 15.993], [-7.849, 9.314], [0.444, -6.02], [-2.715, -11.628]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 46, "s": [{"i": [[0, -2.781], [0, 0], [0, 0], [-1.405, -2.792], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0, 7.081], [0, 0], [0, 0], [2.846, 0]], "o": [[0, 0], [0, 0], [0, 3.111], [0.659, 1.309], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [0, -2.781], [-2.844, 0]], "v": [[-19, -8.454], [-19, 3.63], [-19, 15.685], [-16.851, 24.703], [-15.842, 28.937], [-15.842, 45.801], [-15.128, 46.5], [9.079, 46.5], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.51, 45.801], [10.51, 46.5], [18.684, 46.5], [19.401, 45.801], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-8.681, 15.684], [-8.681, 3.255], [-8.681, -8.454], [-13.84, -13.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [{"i": [[-1.125, -2.921], [0, 0], [0, 0], [-1.352, -3], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [2.931, 6.816], [0, 0], [0, 0], [3.318, -0.223]], "o": [[0, 0], [0, 0], [1.5, 3.753], [0.602, 1.336], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [-1.569, -3.359], [-2.785, 0.188]], "v": [[-27.562, -3.516], [-24.25, 5.641], [-20.75, 15.685], [-16.788, 24.687], [-15.85, 28.937], [-15.824, 45.801], [-15.11, 46.5], [9.105, 46.489], [9.079, 36.718], [9.794, 36.018], [10.51, 36.718], [10.51, 39.516], [10.536, 45.79], [10.536, 46.489], [18.71, 46.489], [19.427, 45.79], [19.401, 33.968], [11.577, 26.603], [0.993, 26.603], [-10.181, 15.434], [-14.987, 3.641], [-18.243, -4.516], [-24.84, -9.562]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52, "s": [{"i": [[0, -2.781], [0, 0], [0, 0], [-1.405, -2.792], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0, 7.081], [0, 0], [0, 0], [2.846, 0]], "o": [[0, 0], [0, 0], [0, 3.111], [0.659, 1.309], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [0, -2.781], [-2.844, 0]], "v": [[-19, -8.454], [-19, 3.63], [-19, 15.685], [-16.851, 24.703], [-15.842, 28.937], [-15.842, 45.801], [-15.128, 46.5], [9.079, 46.5], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.51, 45.801], [10.51, 46.5], [18.684, 46.5], [19.401, 45.801], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-8.681, 15.684], [-8.681, 3.255], [-8.681, -8.454], [-13.84, -13.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 55, "s": [{"i": [[1.812, -2.329], [0.737, -3.689], [-0.004, -3.307], [-1.024, -2.891], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0.306, 7.566], [-0.118, 3.255], [0, 0], [3.215, -0.125]], "o": [[-1.812, 2.329], [-0.759, 3.799], [0.004, 3.012], [0.489, 1.381], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0.118, -3.255], [1.806, -2.921], [-2.842, 0.11]], "v": [[-10.438, -4.895], [-16.421, 6.689], [-17.184, 15.619], [-16.898, 24.641], [-15.815, 28.937], [-15.756, 45.77], [-15.042, 46.469], [9.138, 46.469], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.569, 45.77], [10.569, 46.469], [18.743, 46.469], [19.46, 45.77], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-7.681, 15.993], [-7.849, 9.314], [0.444, -6.02], [-2.715, -11.628]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 58, "s": [{"i": [[0, -2.781], [0, 0], [0, 0], [-1.405, -2.792], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0, 7.081], [0, 0], [0, 0], [2.846, 0]], "o": [[0, 0], [0, 0], [0, 3.111], [0.659, 1.309], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [0, -2.781], [-2.844, 0]], "v": [[-19, -8.454], [-19, 3.63], [-19, 15.685], [-16.851, 24.703], [-15.842, 28.937], [-15.842, 45.801], [-15.128, 46.5], [9.079, 46.5], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.51, 45.801], [10.51, 46.5], [18.684, 46.5], [19.401, 45.801], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-8.681, 15.684], [-8.681, 3.255], [-8.681, -8.454], [-13.84, -13.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61, "s": [{"i": [[-1.125, -2.921], [0, 0], [0, 0], [-1.352, -3], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [2.931, 6.816], [0, 0], [0, 0], [3.318, -0.223]], "o": [[0, 0], [0, 0], [1.5, 3.753], [0.602, 1.336], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [-1.569, -3.359], [-2.785, 0.188]], "v": [[-27.562, -3.516], [-24.25, 5.641], [-20.75, 15.685], [-16.788, 24.687], [-15.85, 28.937], [-15.824, 45.801], [-15.11, 46.5], [9.105, 46.489], [9.079, 36.718], [9.794, 36.018], [10.51, 36.718], [10.51, 39.516], [10.536, 45.79], [10.536, 46.489], [18.71, 46.489], [19.427, 45.79], [19.401, 33.968], [11.577, 26.603], [0.993, 26.603], [-10.181, 15.434], [-14.987, 3.641], [-18.243, -4.516], [-24.84, -9.562]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [{"i": [[0, -2.781], [0, 0], [0, 0], [-1.405, -2.792], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0, 7.081], [0, 0], [0, 0], [2.846, 0]], "o": [[0, 0], [0, 0], [0, 3.111], [0.659, 1.309], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [0, -2.781], [-2.844, 0]], "v": [[-19, -8.454], [-19, 3.63], [-19, 15.685], [-16.851, 24.703], [-15.842, 28.937], [-15.842, 45.801], [-15.128, 46.5], [9.079, 46.5], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.51, 45.801], [10.51, 46.5], [18.684, 46.5], [19.401, 45.801], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-8.681, 15.684], [-8.681, 3.255], [-8.681, -8.454], [-13.84, -13.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 67, "s": [{"i": [[1.812, -2.329], [0.737, -3.689], [-0.004, -3.307], [-1.024, -2.891], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0.306, 7.566], [-0.118, 3.255], [0, 0], [3.215, -0.125]], "o": [[-1.812, 2.329], [-0.759, 3.799], [0.004, 3.012], [0.489, 1.381], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0.118, -3.255], [1.806, -2.921], [-2.842, 0.11]], "v": [[-10.438, -4.895], [-16.421, 6.689], [-17.184, 15.619], [-16.898, 24.641], [-15.815, 28.937], [-15.756, 45.77], [-15.042, 46.469], [9.138, 46.469], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.569, 45.77], [10.569, 46.469], [18.743, 46.469], [19.46, 45.77], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-7.681, 15.993], [-7.849, 9.314], [0.444, -6.02], [-2.715, -11.628]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 70, "s": [{"i": [[0, -2.781], [0, 0], [0, 0], [-1.405, -2.792], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0, 7.081], [0, 0], [0, 0], [2.846, 0]], "o": [[0, 0], [0, 0], [0, 3.111], [0.659, 1.309], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [0, -2.781], [-2.844, 0]], "v": [[-19, -8.454], [-19, 3.63], [-19, 15.685], [-16.851, 24.703], [-15.842, 28.937], [-15.842, 45.801], [-15.128, 46.5], [9.079, 46.5], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.51, 45.801], [10.51, 46.5], [18.684, 46.5], [19.401, 45.801], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-8.681, 15.684], [-8.681, 3.255], [-8.681, -8.454], [-13.84, -13.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 73, "s": [{"i": [[-1.125, -2.921], [0, 0], [0, 0], [-1.352, -3], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [2.931, 6.816], [0, 0], [0, 0], [3.318, -0.223]], "o": [[0, 0], [0, 0], [1.5, 3.753], [0.602, 1.336], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [-1.569, -3.359], [-2.785, 0.188]], "v": [[-27.562, -3.516], [-24.25, 5.641], [-20.75, 15.685], [-16.788, 24.687], [-15.85, 28.937], [-15.824, 45.801], [-15.11, 46.5], [9.105, 46.489], [9.079, 36.718], [9.794, 36.018], [10.51, 36.718], [10.51, 39.516], [10.536, 45.79], [10.536, 46.489], [18.71, 46.489], [19.427, 45.79], [19.401, 33.968], [11.577, 26.603], [0.993, 26.603], [-10.181, 15.434], [-14.987, 3.641], [-18.243, -4.516], [-24.84, -9.562]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [{"i": [[0, -2.781], [0, 0], [0, 0], [-1.405, -2.792], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0, 7.081], [0, 0], [0, 0], [2.846, 0]], "o": [[0, 0], [0, 0], [0, 3.111], [0.659, 1.309], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [0, -2.781], [-2.844, 0]], "v": [[-19, -8.454], [-19, 3.63], [-19, 15.685], [-16.851, 24.703], [-15.842, 28.937], [-15.842, 45.801], [-15.128, 46.5], [9.079, 46.5], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.51, 45.801], [10.51, 46.5], [18.684, 46.5], [19.401, 45.801], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-8.681, 15.684], [-8.681, 3.255], [-8.681, -8.454], [-13.84, -13.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 79, "s": [{"i": [[1.812, -2.329], [0.737, -3.689], [-0.004, -3.307], [-1.024, -2.891], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0.306, 7.566], [-0.118, 3.255], [0, 0], [3.215, -0.125]], "o": [[-1.812, 2.329], [-0.759, 3.799], [0.004, 3.012], [0.489, 1.381], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0.118, -3.255], [1.806, -2.921], [-2.842, 0.11]], "v": [[-10.438, -4.895], [-16.421, 6.689], [-17.184, 15.619], [-16.898, 24.641], [-15.815, 28.937], [-15.756, 45.77], [-15.042, 46.469], [9.138, 46.469], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.569, 45.77], [10.569, 46.469], [18.743, 46.469], [19.46, 45.77], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-7.681, 15.993], [-7.849, 9.314], [0.444, -6.02], [-2.715, -11.628]], "c": true}]}, {"t": 82, "s": [{"i": [[0, -2.781], [0, 0], [0, 0], [-1.405, -2.792], [0, -1.462], [0, 0], [-0.396, 0], [0, 0], [0, 0], [-0.391, 0], [0, -0.388], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.388], [0, 0], [4.491, 0], [0, 0], [0, 7.081], [0, 0], [0, 0], [2.846, 0]], "o": [[0, 0], [0, 0], [0, 3.111], [0.659, 1.309], [0, 0], [0, 0.388], [0, 0], [0, 0], [0, -0.388], [0.395, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.397, 0], [0, 0], [-0.003, -4.396], [0, 0], [-5.819, 0], [0, 0], [0, 0], [0, -2.781], [-2.844, 0]], "v": [[-19, -8.454], [-19, 3.63], [-19, 15.685], [-16.851, 24.703], [-15.842, 28.937], [-15.842, 45.801], [-15.128, 46.5], [9.079, 46.5], [9.079, 36.729], [9.794, 36.029], [10.51, 36.729], [10.51, 39.527], [10.51, 45.801], [10.51, 46.5], [18.684, 46.5], [19.401, 45.801], [19.401, 33.979], [11.577, 26.614], [2.243, 26.614], [-8.681, 15.684], [-8.681, 3.255], [-8.681, -8.454], [-13.84, -13.5]], "c": true}]}], "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.956862747669, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [24, 18.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}], "markers": []}