//全局通用样式或样式参数
.position100(){
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
}
.positionAll(){
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}
.show,
.block{
    display: block !important;
}
.hide,
.none{
    display: none;
}
.pull-left{
    float: left;
}
.pull-right{
    float: right !important;
}
.clearafter(){
    &:after{
        content: '';
        display:block;
        clear: both;
        height: 0px;
        overflow: hidden;
    }
}
.clearbefore(){
    &:before{
        content: '';
        display:block;
        clear: both;
        height: 0px;
        overflow: hidden;
    }
}
.clearfix{
    .clearbefore();
    .clearafter();
}

.row{
    margin-left: -@base-margin / 2;
    margin-right: -@base-margin / 2;
    .clearafter();
}
.col(){
    padding-left: @base-margin / 2;
    padding-right: @base-margin / 2;
}
.col-20{
    float: left;
    width: 20%;
    .col();
}
.col-25{
    float: left;
    width: 25%;
    .col();
}
.col-40{
    float: left;
    width: 40%;
    .col();
}
.col-50{
    float: left;
    width: 50%;
    .col();
}
.col-100{
    float: left;
    width: 100%;
    .col();
}