<?xml version="1.0" encoding="utf-8"?>
<selector
  xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true" android:color="?colorOnContainer" android:alpha="@dimen/m3_comp_fab_primary_pressed_state_layer_opacity" />
    <item android:state_focused="true" android:color="?colorOnContainer" android:alpha="@dimen/m3_comp_fab_primary_focus_state_layer_opacity" />
    <item android:color="?colorOnContainer" android:alpha="@dimen/m3_comp_fab_primary_hover_state_layer_opacity" android:state_hovered="true" />
    <item android:color="?colorOnContainer" android:alpha="@dimen/m3_ripple_default_alpha" />
</selector>
