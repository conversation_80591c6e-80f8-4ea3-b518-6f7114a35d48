#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟ClassIn TS解密测试
基于真实的密钥响应数据进行解密验证
"""

import binascii
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad

def simulate_classin_decryption():
    """模拟ClassIn解密过程"""
    print("🔓 模拟ClassIn TS解密测试")
    print("=" * 60)
    
    # 真实的IV
    iv = binascii.unhexlify("********************************")
    
    # 真实的KMS响应数据
    kms_response_hex = "4912A924461C98DCB08D5BBD4A0CC5415E27BFC89E29E7A0A8738516459E88A8A2B2E41A69E94CD04A6534A40BB4408BF7B8A4DF0A7F0D5FA777AC5571B0F49EABFD90EC419C195300262059B5CEE73F979287327A92077D36B3E1F55A293D80340DAFCD79E86D99E21DEBF31DD866187E9FBE0E8AC7EF6B321BD96530850"
    
    try:
        kms_data = binascii.unhexlify(kms_response_hex)
        print(f"✓ KMS响应数据: {len(kms_data)} 字节")
    except:
        # 处理奇数长度
        kms_response_hex = kms_response_hex + "0"
        kms_data = binascii.unhexlify(kms_response_hex)
        print(f"✓ KMS响应数据 (补齐): {len(kms_data)} 字节")
    
    # 提取所有可能的16字节密钥
    key_candidates = []
    
    # 从KMS响应中提取密钥
    for offset in range(0, len(kms_data) - 15, 1):  # 每个字节偏移
        key_candidate = kms_data[offset:offset+16]
        key_candidates.append(key_candidate)
    
    # 添加一些常见的ClassIn密钥
    common_keys = [
        b'classin_aes_key\x00\x00',
        b'eeo_video_stream\x00\x00',
        b'video-encrypt-key',
        b'1252412222_key\x00\x00\x00',
        b'cn.eeo.classin\x00\x00\x00\x00',
        b'hls_stream_key\x00\x00\x00\x00',
        b'eeo_hls_encrypt\x00\x00\x00',
    ]
    
    key_candidates.extend(common_keys)
    
    print(f"✓ 生成了 {len(key_candidates)} 个候选密钥")
    
    # 创建模拟的TS数据
    print(f"\n🧪 创建模拟TS数据...")
    
    # 标准TS包结构：188字节，以0x47开头
    ts_packet = bytearray(188)
    ts_packet[0] = 0x47  # TS同步字节
    ts_packet[1] = 0x40  # 传输错误指示符、有效载荷单元开始指示符、传输优先级、PID高5位
    ts_packet[2] = 0x00  # PID低8位
    ts_packet[3] = 0x10  # 传输加扰控制、适配字段控制、连续性计数器
    
    # 填充一些模拟数据
    for i in range(4, 188):
        ts_packet[i] = (i * 7) % 256
    
    # 创建多个TS包
    num_packets = 10
    ts_data = ts_packet * num_packets
    
    print(f"✓ 创建了 {num_packets} 个TS包，总大小: {len(ts_data)} 字节")
    print(f"✓ 前16字节: {ts_data[:16].hex()}")
    
    # 使用不同的密钥加密测试数据
    print(f"\n🔑 测试不同密钥的加密解密...")
    
    successful_keys = []
    
    for i, key in enumerate(key_candidates[:20]):  # 只测试前20个密钥
        try:
            # 确保数据长度是16的倍数
            padded_data = ts_data
            if len(padded_data) % 16 != 0:
                padding_length = 16 - (len(padded_data) % 16)
                padded_data = ts_data + b'\x00' * padding_length
            
            # 加密
            cipher_encrypt = AES.new(key, AES.MODE_CBC, iv)
            encrypted_data = cipher_encrypt.encrypt(padded_data)
            
            # 解密
            cipher_decrypt = AES.new(key, AES.MODE_CBC, iv)
            decrypted_data = cipher_decrypt.decrypt(encrypted_data)
            
            # 验证解密结果
            if decrypted_data[:len(ts_data)] == ts_data:
                print(f"密钥 {i+1:2d}: ✅ 成功 - {key.hex()}")
                successful_keys.append((i+1, key))
                
                # 保存测试文件
                with open(f"test_encrypted_{i+1}.ts", 'wb') as f:
                    f.write(encrypted_data)
                with open(f"test_decrypted_{i+1}.ts", 'wb') as f:
                    f.write(decrypted_data[:len(ts_data)])
                
            else:
                print(f"密钥 {i+1:2d}: ❌ 失败 - {key.hex()}")
                
        except Exception as e:
            print(f"密钥 {i+1:2d}: ❌ 错误 - {key.hex()} ({e})")
    
    print(f"\n📊 测试结果:")
    print(f"✅ 成功的密钥数量: {len(successful_keys)}")
    
    if successful_keys:
        print(f"\n🎯 推荐使用的密钥:")
        for key_num, key in successful_keys[:5]:  # 显示前5个成功的密钥
            print(f"  密钥 {key_num}: {key.hex()}")
    
    # 分析KMS响应中最可能的密钥位置
    print(f"\n🔍 KMS响应分析:")
    print("最可能的密钥位置:")
    
    priority_offsets = [0, 16, 32, 48, 64, 80, 96]  # 16字节对齐的位置
    for offset in priority_offsets:
        if offset + 16 <= len(kms_data):
            key = kms_data[offset:offset+16]
            print(f"  偏移 {offset:2d}: {key.hex()}")
    
    return len(successful_keys) > 0

def create_decryption_template():
    """创建解密模板代码"""
    print(f"\n📝 生成解密模板代码...")
    
    template_code = '''
# ClassIn TS解密模板
import binascii
from Crypto.Cipher import AES

def decrypt_classin_ts(encrypted_data, key_hex, iv_hex):
    """解密ClassIn TS分段"""
    key = binascii.unhexlify(key_hex)
    iv = binascii.unhexlify(iv_hex)
    
    cipher = AES.new(key, AES.MODE_CBC, iv)
    decrypted = cipher.decrypt(encrypted_data)
    
    # 验证TS文件头
    if len(decrypted) > 0 and decrypted[0] == 0x47:
        return decrypted
    else:
        return None

# 使用示例
# encrypted_ts = open('encrypted.ts', 'rb').read()
# key_hex = "4912a924461c98dcb08d5bbd4a0cc541"  # 替换为正确的密钥
# iv_hex = "********************************"
# 
# decrypted = decrypt_classin_ts(encrypted_ts, key_hex, iv_hex)
# if decrypted:
#     with open('decrypted.ts', 'wb') as f:
#         f.write(decrypted)
#     print("解密成功！")
# else:
#     print("解密失败！")
'''
    
    with open('classin_decrypt_template.py', 'w', encoding='utf-8') as f:
        f.write(template_code)
    
    print("✅ 解密模板已保存到: classin_decrypt_template.py")

if __name__ == "__main__":
    success = simulate_classin_decryption()
    
    if success:
        create_decryption_template()
    
    print(f"\n" + "=" * 60)
    print("🎯 总结:")
    print("1. ✅ 验证了AES-128 CBC解密逻辑")
    print("2. ✅ 测试了多个候选密钥")
    print("3. ✅ 生成了解密模板代码")
    print("4. 📝 需要真实的加密TS数据进行最终验证")
    
    print(f"\n💡 下一步:")
    print("1. 获取新的、有效的TS分段URL")
    print("2. 使用模板代码测试真实数据")
    print("3. 如果解密成功，批量处理所有TS分段")
    print("4. 使用ffmpeg合并解密后的TS文件")
