<?xml version="1.0" encoding="utf-8"?>
<vector android:height="20.0dip" android:width="79.0dip" android:viewportWidth="79.0" android:viewportHeight="20.0"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillColor="#ff009cde" android:pathData="M74.8208,4.5144L73.058,15.6643C73.0238,15.8802 73.1917,16.0756 73.4116,16.0756L75.1845,16.0756C75.4783,16.0756 75.7283,15.8631 75.774,15.5747L77.5121,4.6251C77.5465,4.409 77.3785,4.2136 77.1585,4.2136L75.1744,4.2136C74.9982,4.2136 74.8482,4.3413 74.8208,4.5144" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ff003087" android:pathData="M38.8961,12.1437C38.6974,13.3121 37.7647,14.0963 36.5748,14.0963C35.9781,14.0963 35.5007,13.9055 35.1936,13.5443C34.8894,13.1864 34.7749,12.6762 34.8714,12.1082C35.0566,10.9501 36.0044,10.1406 37.1763,10.1406C37.7603,10.1406 38.2345,10.3331 38.5475,10.6977C38.8625,11.0649 38.9862,11.5785 38.8961,12.1437M41.7623,8.1636L39.7057,8.1636C39.5294,8.1636 39.3794,8.2911 39.352,8.4642L39.2617,9.0359L39.1179,8.8288C38.6724,8.1861 37.6798,7.9713 36.6885,7.9713C34.4165,7.9713 32.4753,9.6833 32.0975,12.0842C31.901,13.2819 32.1801,14.4267 32.8631,15.2257C33.4908,15.9598 34.3865,16.2653 35.4535,16.2653C37.285,16.2653 38.3008,15.0954 38.3008,15.0954L38.2091,15.6638C38.1745,15.88 38.3424,16.0755 38.5627,16.0755L40.4147,16.0755C40.7083,16.0755 40.9583,15.8631 41.0042,15.5748L42.1159,8.5751C42.1503,8.359 41.9824,8.1636 41.7623,8.1636" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ff009cde" android:pathData="M60.0547,8.2127C59.82,9.7456 58.6424,9.7456 57.5035,9.7456L56.8557,9.7456L57.3101,6.8837C57.3377,6.7108 57.4877,6.5833 57.6637,6.5833L57.9608,6.5833C58.7361,6.5833 59.4684,6.5833 59.8458,7.0224C60.0717,7.2852 60.14,7.6746 60.0547,8.2127M59.5592,4.2137L55.2639,4.2137C54.9703,4.2137 54.7201,4.4262 54.6745,4.7146L52.9376,15.6642C52.9032,15.8801 53.0714,16.0755 53.2912,16.0755L55.495,16.0755C55.7005,16.0755 55.8756,15.9267 55.9077,15.7248L56.4001,12.6207C56.4459,12.3323 56.6958,12.1198 56.9896,12.1198L58.3488,12.1198C61.1778,12.1198 62.8107,10.7585 63.2373,8.0605C63.4294,6.8806 63.245,5.9536 62.6895,5.3046C62.0785,4.591 60.9959,4.2137 59.5592,4.2137" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ff003087" android:pathData="M52.7164,8.1637L50.6491,8.1637C50.4515,8.1637 50.2665,8.2612 50.1555,8.4239L47.3038,12.5993L46.0954,8.5869C46.0197,8.3358 45.7873,8.1637 45.5236,8.1637L43.4915,8.1637C43.2461,8.1637 43.0736,8.4038 43.1527,8.6347L45.429,15.2774L43.2878,18.2807C43.1199,18.5164 43.2894,18.8425 43.5801,18.8425L45.6452,18.8425C45.8409,18.8425 46.0243,18.7471 46.1357,18.5872L53.0107,8.7226C53.1752,8.4865 53.0051,8.1637 52.7164,8.1637" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ff009cde" android:pathData="M69.5304,12.1437C69.3318,13.3121 68.3988,14.0963 67.2091,14.0963C66.6125,14.0963 66.1351,13.9055 65.828,13.5443C65.5237,13.1864 65.4093,12.6762 65.5058,12.1082C65.6909,10.9501 66.6387,10.1406 67.8106,10.1406C68.3946,10.1406 68.8689,10.3331 69.1818,10.6977C69.4968,11.0649 69.6206,11.5785 69.5304,12.1437M72.3966,8.1636L70.34,8.1636C70.1638,8.1636 70.0138,8.2911 69.9864,8.4642L69.8958,9.0359L69.7522,8.8288C69.3067,8.1861 68.3141,7.9713 67.3228,7.9713C65.0508,7.9713 63.1096,9.6833 62.7318,12.0842C62.5353,13.2819 62.8143,14.4267 63.4974,15.2257C64.1252,15.9598 65.0207,16.2653 66.0878,16.2653C67.9193,16.2653 68.9351,15.0954 68.9351,15.0954L68.8432,15.6638C68.8088,15.88 68.9768,16.0755 69.1968,16.0755L71.049,16.0755C71.3427,16.0755 71.5926,15.8631 71.6386,15.5748L72.7503,8.5751C72.7846,8.359 72.6167,8.1636 72.3966,8.1636" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ff003087" android:pathData="M29.4204,8.2127C29.1856,9.7456 28.0081,9.7456 26.8694,9.7456L26.2214,9.7456L26.6758,6.8837C26.7032,6.7108 26.8534,6.5833 27.0294,6.5833L27.3265,6.5833C28.1017,6.5833 28.8341,6.5833 29.2115,7.0224C29.4374,7.2852 29.5057,7.6746 29.4204,8.2127M28.9247,4.2137L24.6296,4.2137C24.336,4.2137 24.0858,4.4262 24.04,4.7146L22.3033,15.6642C22.2691,15.8801 22.4371,16.0755 22.6569,16.0755L24.7078,16.0755C25.0014,16.0755 25.2514,15.863 25.2971,15.5747L25.7658,12.6207C25.8115,12.3323 26.0615,12.1198 26.3553,12.1198L27.7145,12.1198C30.5435,12.1198 32.1764,10.7585 32.603,8.0605C32.7951,6.8806 32.6107,5.9536 32.0551,5.3046C31.4442,4.591 30.3616,4.2137 28.9247,4.2137" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ff009cde" android:pathData="M14.4742,4.7872C14.7054,3.3215 14.4726,2.3241 13.6752,1.4207C12.7973,0.426 11.2111,-0.0001 9.1819,-0.0001L3.2915,-0.0001C2.8769,-0.0001 2.5236,0.2999 2.4591,0.7074L0.0062,16.1707C-0.042,16.4758 0.195,16.7518 0.5058,16.7518L4.1422,16.7518L3.8911,18.3342C3.8488,18.6011 4.0563,18.8426 4.3281,18.8426L7.3932,18.8426C7.7561,18.8426 8.0649,18.58 8.1215,18.2236L8.1514,18.0688L8.729,14.4284L8.7663,14.2273C8.8226,13.8711 9.1315,13.6085 9.4944,13.6085L9.9529,13.6085C12.9223,13.6085 15.2475,12.4092 15.9268,8.9405C16.2107,7.4911 16.0638,6.2813 15.3131,5.4307C15.0861,5.1733 14.8035,4.9607 14.4742,4.7872" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ff012169" android:pathData="M14.4742,4.7872C14.7054,3.3215 14.4726,2.3241 13.6752,1.4207C12.7973,0.426 11.2111,-0.0001 9.1819,-0.0001L3.2915,-0.0001C2.8769,-0.0001 2.5236,0.2999 2.4591,0.7074L0.0062,16.1707C-0.042,16.4758 0.195,16.7518 0.5058,16.7518L4.1422,16.7518L5.0557,10.9929L5.0273,11.1735C5.092,10.7662 5.4421,10.4663 5.8568,10.4663L7.5851,10.4663C10.9794,10.4663 13.6374,9.0952 14.4139,5.1301C14.4369,5.0127 14.4566,4.8989 14.4742,4.7872" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ff003087" android:pathData="M6.0369,4.8066C6.0757,4.5616 6.234,4.361 6.4469,4.2597C6.5437,4.2134 6.6518,4.1879 6.7653,4.1879L11.3825,4.1879C11.9294,4.1879 12.4394,4.2234 12.9058,4.298C13.039,4.3194 13.1687,4.3439 13.2946,4.3717C13.4206,4.3996 13.5428,4.4307 13.6613,4.465C13.7204,4.4821 13.7787,4.5003 13.8358,4.5191C14.0648,4.5948 14.2781,4.6838 14.4742,4.7873C14.7055,3.3214 14.4725,2.324 13.6752,1.4206C12.7973,0.4261 11.2111,0 9.1819,0L3.2916,0C2.8767,0 2.5237,0.3 2.459,0.7072L0.0063,16.1707C-0.0422,16.4759 0.195,16.7519 0.5058,16.7519L4.1423,16.7519L5.0557,10.993L6.0369,4.8066Z" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
</vector>
