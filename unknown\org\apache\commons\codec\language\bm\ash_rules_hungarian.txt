/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// ASHKENAZIC

// CONSONANTS
"sz" "" "" "s"
"zs" "" "" "Z"
"cs" "" "" "tS"
           
"ay" "" "" "(oj|aj)"
"ai" "" "" "(oj|aj)"
"aj" "" "" "(oj|aj)"
    
"ei" "" "" "aj" // German element
"ey" "" "" "aj" // German element
    
"y" "[áo]" "" "j"
"i" "[áo]" "" "j"
"ee" "" "" "(aj|e)" // actually ej
"ely" "" "" "(aj|eli)" // actually ej
"ly" "" "" "(j|li)"
"gy" "" "[aeouáéóúüöőű]" "dj"
"gy" "" "" "(d|gi)"
"ny" "" "[aeouáéóúüöőű]" "nj"
"ny" "" "" "(n|ni)"
"ty" "" "[aeouáéóúüöőű]" "tj"
"ty" "" "" "(t|ti)"
    
"qu" "" "" "(ku|kv)"
"h" "" "$" ""
                  
// VOWELS
"á" "" "" "a"
"é" "" "" "e"
"í" "" "" "i"
"ó" "" "" "o"
"ö" "" "" "Y"
"ő" "" "" "Y" 
"ú" "" "" "u"
"ü" "" "" "Q"
"ű" "" "" "Q"
                       
// LATIN ALPHABET
"a" "" "" "a"
"b" "" "" "b"
"c" "" "" "ts" 
"d" "" "" "d"
"e" "" "" "E"
"f" "" "" "f"
"g" "" "" "g" 
"h" "" "" "h"
"i" "" "" "I"
"j" "" "" "j"
"k" "" "" "k"
"l" "" "" "l"
"m" "" "" "m"
"n" "" "" "n"
"o" "" "" "o"
"p" "" "" "p"
"q" "" "" "k"
"r" "" "" "r"
"s" "" "" "(S|s)" 
"t" "" "" "t"
"u" "" "" "u"
"v" "" "" "v" 
"w" "" "" "v" 
"x" "" "" "ks"
"y" "" "" "i" 
"z" "" "" "z"
