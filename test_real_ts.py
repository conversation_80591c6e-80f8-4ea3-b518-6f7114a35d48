#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的ClassIn TS分段解密
"""

import binascii
import requests
from Crypto.Cipher import AES

def test_real_ts_decryption():
    """测试真实的TS分段解密"""
    print("🔓 测试真实ClassIn TS分段解密")
    print("=" * 60)
    
    # 从M3U8中提取的第一个TS分段URL
    ts_url = "https://t0s-cdn.eeo.cn/0712bc453c98649bbc39b4f2117eef9f-0.ts?q-sign-algorithm=sha1&q-ak=AKIDwJEwaFA0uN1NDrQSdiAqe_4CXFKKppNmcnuNXWdRospKy5Zp0w6I0x8SBtgoG0i0&q-sign-time=1755868193%3B1755911393&q-key-time=1755868193%3B1755911393&q-header-list=&q-url-param-list=ci-process%3Bindex%3Bplaylist&q-signature=5fd4d6d1c197a4f8b1fbeeabd58ba7f6ad8ee067&x-cos-security-token=KUYkilSNOCkfuY0wCFulEK0aH8RJXX0aaac67cab96563563b19033318d2b2f8fOWWgifH-OvBp-dJbjGRRc5iqHuJWIdQhgh9fvCgn9_UL9GssIB-5c1-ohypyhE-l0_iz2HLb2IqTjyy4DbaPfdywm18pKkJr79Za1d19YPrgOKVHH2tHIjdHOLuyUm-ZwpLc15q9sRl074VNoN8lp5XAQrLN4Q2-lGyyAfOGFf3s9WyCSHPRayRnO2E3tEQKoairKK8nIASuGnNvjRzIopXY_SysCs1eScIHJOy6rBy9Y9zQNR3hSCSrdmIlf1uQc7mmOzAGil-MXeY5yDYk_KZqCvg2yeISZZigvGerNMmnQSj4dnqsLvaua5RMpDimrPhusqGvu9U23AbLGSuiAJ37q7CGb-8gkxP5GTAiR0wMaHK0fAM6LQCTZxZwFjuN0TuaiDZYNbHXbWwG4y6zf8G_zmOvRoEJIzwsiMrIEKpje7gcWFSkswpEM9zLVH2gyBn9SaTFefrx0KNnAfPL22UGNmKeQfWo_6JUvo7fxjWGygHXb69eGzZIWiRgP_b_x_DOnbN68yrrS7PNG4QOOQ&ci-process=livetranscode&index=0&playlist=ZmlsZXMvcG0zdTgvdm9kLzA3LzEyLzMzOTEzLzA3MTJiYzQ1M2M5ODY0OWJiYzM5YjRmMjExN2VlZjlmLm0zdTg%3D"
    
    # 固定的IV
    iv = binascii.unhexlify("1fae8424a28fed9c2f629c2568d6598f")
    
    # 候选密钥列表
    key_candidates = [
        # 从KMS响应中提取的候选密钥
        binascii.unhexlify("4912a924461c98dcb08d5bbd4a0cc541"),  # 前16字节
        binascii.unhexlify("5e27bfc89e29e7a0a8738516459e88a8"),  # 16-32字节
        
        # 基于KMS密文的可能密钥
        binascii.unhexlify("9c70bfeca9c6cc76402f55296554378608a31b2496f893d3f26e8a4253cf4732")[:16],
        
        # 常见的ClassIn密钥
        b'classin_aes_key\x00\x00',  # 填充到16字节
        b'eeo_video_stream\x00\x00',  # 填充到16字节
        b'video-encrypt-key',
        b'1252412222_key\x00\x00\x00',  # 基于AppId
        
        # 其他可能的密钥
        b'cn.eeo.classin\x00\x00\x00\x00',
        b'hls_stream_key\x00\x00\x00\x00',
        b'eeo_hls_encrypt\x00\x00\x00',
        
        # 基于KMS响应的更多候选密钥
        binascii.unhexlify("a2b2e41a69e94cd04a6534a40bb4408b"),  # 32-48字节
        binascii.unhexlify("f7b8a4df0a7f0d5fa777ac5571b0f49e"),  # 48-64字节
        binascii.unhexlify("abfd90ec419c195300262059b5cee73f"),  # 64-80字节
    ]
    
    print(f"✓ 准备测试 {len(key_candidates)} 个候选密钥")
    print(f"✓ IV: {iv.hex()}")
    print(f"✓ TS URL: {ts_url[:100]}...")
    
    # 下载TS分段
    print(f"\n🔽 下载TS分段...")
    
    headers = {
        'User-Agent': 'ClassIn/6.0.1.1081 AVPlayer/5.15.1',
        'Accept': '*/*',
        'Connection': 'keep-alive',
        'Host': 't0s-cdn.eeo.cn',
        'Icy-MetaData': '1'
    }
    
    try:
        response = requests.get(ts_url, headers=headers, timeout=30)
        if response.status_code != 200:
            print(f"❌ 下载失败，状态码: {response.status_code}")
            return False
        
        encrypted_data = response.content
        print(f"✅ 下载成功！")
        print(f"📊 文件大小: {len(encrypted_data)} 字节")
        print(f"📊 前16字节: {encrypted_data[:16].hex()}")
        print(f"📊 后16字节: {encrypted_data[-16:].hex()}")
        
        # 尝试解密
        print(f"\n🔑 开始尝试解密...")
        
        for i, key in enumerate(key_candidates):
            print(f"\n密钥 {i+1:2d}: {key.hex()}")
            
            try:
                cipher = AES.new(key, AES.MODE_CBC, iv)
                decrypted = cipher.decrypt(encrypted_data)
                
                # 检查解密结果
                print(f"  📊 解密数据前16字节: {decrypted[:16].hex()}")
                
                # 检查TS文件头 (0x47)
                if len(decrypted) > 0 and decrypted[0] == 0x47:
                    print(f"  ✅ 成功！发现TS文件头 (0x47)")
                    
                    # 保存解密结果
                    output_file = f"decrypted_segment_0_key_{i+1}.ts"
                    with open(output_file, 'wb') as f:
                        f.write(decrypted)
                    
                    print(f"  💾 已保存到: {output_file}")
                    
                    # 显示更多信息
                    print(f"  📊 解密文件大小: {len(decrypted)} 字节")
                    
                    # 检查TS包结构
                    ts_packets = len(decrypted) // 188
                    print(f"  📺 TS包数量: {ts_packets}")
                    
                    # 验证更多TS包
                    valid_packets = 0
                    for packet_idx in range(min(10, ts_packets)):
                        packet_start = packet_idx * 188
                        if packet_start < len(decrypted) and decrypted[packet_start] == 0x47:
                            valid_packets += 1
                    
                    print(f"  ✅ 有效TS包: {valid_packets}/10")
                    
                    if valid_packets >= 8:  # 至少80%的包有效
                        print(f"  🎉 解密完全成功！密钥正确！")
                        return True
                    else:
                        print(f"  ⚠️  部分成功，可能需要进一步验证")
                
                else:
                    # 检查是否有其他模式
                    printable_count = sum(1 for b in decrypted[:100] if 32 <= b <= 126)
                    if printable_count > 50:
                        print(f"  ⚠️  包含 {printable_count}% 可打印字符，可能是其他格式")
                    else:
                        print(f"  ❌ 未识别的格式")
                
            except Exception as e:
                print(f"  ❌ 解密失败: {e}")
        
        print(f"\n❌ 所有候选密钥都无法成功解密")
        
        # 提供进一步分析建议
        print(f"\n🔍 进一步分析建议:")
        print("1. 检查KMS密钥响应是否需要额外解密")
        print("2. 尝试不同的密钥派生方法")
        print("3. 分析是否使用了自定义的加密模式")
        print("4. 检查IV是否需要动态计算")
        
        return False
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = test_real_ts_decryption()
    
    print(f"\n" + "=" * 60)
    if success:
        print("🎉 解密测试成功！")
    else:
        print("⚠️  解密测试需要进一步分析")
    
    print("\n💡 如果解密成功，可以使用以下命令播放:")
    print("ffplay decrypted_segment_0_key_X.ts")
    print("\n或者使用VLC等播放器直接打开解密后的文件")
