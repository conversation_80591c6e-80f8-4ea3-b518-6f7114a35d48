@inputBgc:#fcfcfc;

input[type="text"].form-ctrl,
input[type="password"].form-ctrl,
div.inputTips {
    padding: 5px;
    background-color: @inputBgc;
    border: 1px solid transparent;
    overflow: hidden;
}
input[type="text"].form-ctrl,
input[type="password"].form-ctrl{
    border: 1px solid #eee;
}
input[type="text"].form-ctrl:focus,
input[type="password"].form-ctrl:focus{
    border: 1px solid #389be9;
//  box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.2);
}
input[type="text"].block,
input[type="password"].block,
.btn.block{
    display: block;
    width: 100%;
}
.inputTips{
    color: #bbb;
}
.inputTips.focus{
    color: #333;
}
.form-group{
    .input-group {
      position: relative;
      margin-bottom: 15px;
      input[type="password"].inputTips{
          position: absolute;
          left: 0;
          top: 0;
          background-color: transparent;
      }
      input[type="password"].inputTips.focus {
          background-color: @inputBgc;
      }
      &:after{
      	.clearafter();
      }
    }
//  .input-group.tr{
//      display: -webkit-box;
//      width: 100%;
//      div.td{
//            -webkit-box-flex:2.0;
//          input.first{
//              border-top-right-radius: 0;
//              border-bottom-right-radius: 0;
//          }
//          input.last{
//              border-top-left-radius: 0;
//              border-bottom-left-radius: 0;
//          }
//      }
//  }
    .input-group.tr{
        display: inline-table;
        vertical-align: middle;
        width: 100%;
        div.td{
            display: table-cell;
        	vertical-align: middle;

//          input[type="text"].form-ctrl,
//          input[type="password"].form-ctrl,
//          input[type="button"].form-ctrl,
//          input[type="button"].btn,
//          div.inputTips {
//                float: left;
//                display: block;
//                position: relative;
//                z-index: 2;
//          }
            input.first{
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }
            input.last{
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }
        }
    }
}
//单选复选
.radio,
.checkbox {
    display: inline-block;
    font-weight: normal;
    cursor: pointer;
    overflow: hidden;
    background-image: url(../images/icon_radiobox.png);
    background-position: -4px -2px;
    background-repeat: no-repeat;
    padding-left: 20px;
    .icon{
        width: 16px;
        height: 16px;
        top: 3px;
        margin-top: -3px;
        margin-right: 5px;
    }
    input[type="radio"],
    input[type="checkbox"]{
        float: right;
        position: relative;
        top: 0px;
        left: -9999px;
    }

}
.checkbox{
    background-image: url(../images/icon_checkbox.png);
}
.radio.checked,
.checkbox.checked {
  background-position: -4px -26px;
}

//按钮
.btn{
    display: inline-block;
    padding: 4px 10px;
    font-size: 12px;
    line-height: 12px;
    background-color: #fbfbfb;
    border: 1px solid #bbb;
    border-radius: 3px;
    text-align: center;
    cursor: pointer;
}
.btn:hover{
//  box-shadow: 3px 3px 3px rgba(0,0,0,.1);
}
.btn.df{
    padding: 6px 12px;
    font-size: 12px;
    line-height: 12px;
}
.btn.lg{
    padding: 8px 16px;
    font-size: 14px;
    line-height: 14px;
}
.btn.darkblue{
    color: #fff;
    background-color: #4b5781;
    border-color: #4b5781;
}
.btn.darkblue:active{
    color: #fff;
    background-color: #414b6f;
    border-color: #414b6f;
}
//.btn.darkblue:disabled{
//  background-color: #ccc;
//}
.btn.gray{
    color: #fff;
    background-color: #ccc;
    border-color: #ccc;
}

a.btn:hover{
	text-decoration: none;
}

.btn-link{
    color: #8cbf4a;
}
.btn-link:hover{
    text-decoration: underline;
}
input[type="text"].editTag{
	border: 0;
	padding: 0;
	background-color: transparent;




	&.focus{
		color: #999;
		box-shadow: inset 3px 3px 10px rgba(0,0,0,.2);
	}
}