#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ClassIn M3U8流媒体完整解密工具
基于真实案例的完整解密方案
"""

import os
import re
import json
import base64
import binascii
import requests
from urllib.parse import unquote, parse_qs
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import jwt

class ClassInM3U8Decryptor:
    """ClassIn M3U8流媒体解密器"""
    
    def __init__(self):
        print("ClassIn M3U8流媒体完整解密工具")
        print("=" * 60)
        
        # 存储解析的信息
        self.jwt_payload = None
        self.kms_info = None
        self.encryption_key = None
        self.iv = None
        
    def parse_jwt_token(self, token):
        """解析JWT Token"""
        print("\n=== JWT Token 解析 ===")
        
        try:
            # 不验证签名，直接解码payload
            decoded = jwt.decode(token, options={"verify_signature": False})
            self.jwt_payload = decoded
            
            print("JWT Payload:")
            for key, value in decoded.items():
                if key == "PublicKey":
                    # 解码公钥
                    try:
                        public_key = base64.b64decode(value).decode('utf-8')
                        print(f"  {key}: {public_key[:100]}...")
                    except:
                        print(f"  {key}: {value[:100]}...")
                else:
                    print(f"  {key}: {value}")
            
            return decoded
            
        except Exception as e:
            print(f"JWT解析失败: {e}")
            return None
    
    def parse_kms_key_url(self, key_url):
        """解析KMS密钥URL"""
        print("\n=== KMS密钥URL解析 ===")
        
        # 提取查询参数
        if '?' in key_url:
            base_url, query_string = key_url.split('?', 1)
            params = parse_qs(query_string)
            
            # 解析关键参数
            ciphertext = params.get('Ciphertext', [None])[0]
            if ciphertext:
                ciphertext = unquote(ciphertext)
                print(f"密文: {ciphertext}")
                
                # 解析密文格式: ci:bj_00:base64_data
                if ciphertext.startswith('ci:'):
                    parts = ciphertext.split(':')
                    if len(parts) >= 3:
                        region_code = parts[1]  # bj_00
                        encrypted_key = parts[2]  # base64编码的加密密钥
                        
                        print(f"区域代码: {region_code}")
                        print(f"加密密钥: {encrypted_key}")
                        
                        try:
                            # 尝试base64解码
                            key_bytes = base64.b64decode(encrypted_key)
                            print(f"密钥字节长度: {len(key_bytes)}")
                            print(f"密钥hex: {key_bytes.hex()}")
                            
                            self.kms_info = {
                                'region_code': region_code,
                                'encrypted_key': encrypted_key,
                                'key_bytes': key_bytes
                            }
                            
                        except Exception as e:
                            print(f"Base64解码失败: {e}")
            
            kms_region = params.get('KMSRegion', [None])[0]
            if kms_region:
                print(f"KMS区域: {kms_region}")
        
        return self.kms_info
    
    def analyze_real_m3u8(self, m3u8_content):
        """分析真实的M3U8内容"""
        print("\n=== 真实M3U8分析 ===")
        
        # 提取JWT token
        token_match = re.search(r'token=([^&\s]+)', m3u8_content)
        if token_match:
            token = unquote(token_match.group(1))
            print(f"发现JWT Token: {token[:50]}...")
            self.parse_jwt_token(token)
        
        # 提取密钥URI
        key_match = re.search(r'#EXT-X-KEY:METHOD=AES-128,URI="([^"]+)",IV=0x([a-fA-F0-9]+)', m3u8_content)
        if key_match:
            key_uri = key_match.group(1)
            iv_hex = key_match.group(2)
            
            print(f"密钥URI: {key_uri}")
            print(f"IV: {iv_hex}")
            
            # 转换IV为bytes
            self.iv = binascii.unhexlify(iv_hex)
            print(f"IV bytes: {self.iv.hex()}")
            
            # 解析KMS密钥URL
            self.parse_kms_key_url(key_uri)
        
        # 提取分段信息
        segments = []
        lines = m3u8_content.strip().split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and '.ts' in line:
                segments.append(line)
        
        print(f"发现 {len(segments)} 个TS分段")
        return segments
    
    def decrypt_kms_key(self, encrypted_key_response):
        """尝试解密KMS密钥响应"""
        print("\n=== KMS密钥解密尝试 ===")
        
        # 分析密钥响应的hex数据
        hex_data = encrypted_key_response
        print(f"密钥响应长度: {len(hex_data)} 字节")
        print(f"前32字节: {hex_data[:32].hex()}")
        
        # 尝试不同的解密方法
        
        # 方法1: 直接使用前16字节作为AES密钥
        if len(hex_data) >= 16:
            key_candidate1 = hex_data[:16]
            print(f"候选密钥1 (前16字节): {key_candidate1.hex()}")
            
        # 方法2: 使用特定偏移
        if len(hex_data) >= 32:
            key_candidate2 = hex_data[16:32]
            print(f"候选密钥2 (16-32字节): {key_candidate2.hex()}")
        
        # 方法3: 基于JWT信息推导密钥
        if self.jwt_payload:
            app_id = self.jwt_payload.get('AppId', '')
            bucket_id = self.jwt_payload.get('BucketId', '')
            
            # 尝试使用应用信息生成密钥
            key_material = f"{app_id}_{bucket_id}_key".encode('utf-8')
            if len(key_material) >= 16:
                key_candidate3 = key_material[:16]
            else:
                key_candidate3 = key_material + b'\x00' * (16 - len(key_material))
            
            print(f"候选密钥3 (基于JWT): {key_candidate3.hex()}")
        
        # 方法4: 尝试常见的ClassIn密钥
        common_keys = [
            b'classin_aes_key',  # 14字节，需要填充
            b'eeo_video_stream',  # 14字节，需要填充
            b'video-encrypt-key',  # 16字节
            b'1252412222_key',  # 基于AppId
        ]
        
        print("\n尝试常见密钥:")
        for i, key in enumerate(common_keys):
            if len(key) < 16:
                normalized_key = key + b'\x00' * (16 - len(key))
            elif len(key) > 16:
                normalized_key = key[:16]
            else:
                normalized_key = key
            print(f"常见密钥{i+1}: {normalized_key.hex()}")
        
        # 返回所有候选密钥
        candidates = []
        if len(hex_data) >= 16:
            candidates.append(hex_data[:16])
        if len(hex_data) >= 32:
            candidates.append(hex_data[16:32])
        if self.jwt_payload:
            candidates.append(key_candidate3)
        
        for key in common_keys:
            if len(key) < 16:
                normalized_key = key + b'\x00' * (16 - len(key))
            elif len(key) > 16:
                normalized_key = key[:16]
            else:
                normalized_key = key
            candidates.append(normalized_key)
        
        return candidates
    
    def test_decrypt_with_key(self, encrypted_data, key, iv):
        """测试使用指定密钥解密"""
        try:
            cipher = AES.new(key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(encrypted_data)
            
            # 检查解密结果是否合理
            # TS文件通常以0x47开头
            if decrypted[0] == 0x47:
                return decrypted, True
            
            # 尝试去除填充
            try:
                unpadded = unpad(decrypted, 16)
                if unpadded[0] == 0x47:
                    return unpadded, True
            except:
                pass
            
            return decrypted, False
            
        except Exception as e:
            return None, False
    
    def demonstrate_decryption(self):
        """演示完整的解密过程"""
        print("\n" + "=" * 60)
        print("ClassIn M3U8解密演示")
        print("=" * 60)
        
        # 真实的M3U8内容（从用户提供的数据）
        real_m3u8 = '''#EXTM3U
#EXT-X-VERSION:3
#EXT-X-ALLOW-CACHE:YES
#EXT-X-TARGETDURATION:5
#EXT-X-MEDIA-SEQUENCE:0
#EXT-X-KEY:METHOD=AES-128,URI="/cos-ci/key?ci-process=getDecryptionKey&Ciphertext=ci%3Abj_00%3AnHC%2F7KnGzHZAL1UpZVQ3hgijGySW%2BJPT8m6KQlPPRzI%3D&KMSRegion=ap-beijing&q-sign-algorithm=sha1&q-ak=AKIDwJEwaFA0uN1NDrQSdiAqe_4CXFKKppNmcnuNXWdRospKy5Zp0w6I0x8SBtgoG0i0&q-sign-time=1755868193%3B1755954593&q-key-time=1755868193%3B1755954593&q-header-list=&q-url-param-list=ci-process%3Bciphertext%3Bkmsregion%3Bobject%3Btoken&q-signature=4f24d3143de8e52e38e79d55d8d18e1344613e23&x-cos-security-token=KUYkilSNOCkfuY0wCFulEK0aH8RJXX0aaac67cab96563563b19033318d2b2f8fOWWgifH-OvBp-dJbjGRRc5iqHuJWIdQhgh9fvCgn9_UL9GssIB-5c1-ohypyhE-l0_iz2HLb2IqTjyy4DbaPfdywm18pKkJr79Za1d19YPrgOKVHH2tHIjdHOLuyUm-ZwpLc15q9sRl074VNoN8lp5XAQrLN4Q2-lGyyAfOGFf3s9WyCSHPRayRnO2E3tEQKoairKK8nIASuGnNvjRzIopXY_SysCs1eScIHJOy6rBy9Y9zQNR3hSCSrdmIlf1uQc7mmOzAGil-MXeY5yDYk_KZqCvg2yeISZZigvGerNMmnQSj4dnqsLvaua5RMpDimrPhusqGvu9U23AbLGSuiAJ37q7CGb-8gkxP5GTAiR0wMaHK0fAM6LQCTZxZwFjuN0TuaiDZYNbHXbWwG4y6zf8G_zmOvRoEJIzwsiMrIEKpje7gcWFSkswpEM9zLVH2gyBn9SaTFefrx0KNnAfPL22UGNmKeQfWo_6JUvo7fxjWGygHXb69eGzZIWiRgP_b_x_DOnbN68yrrS7PNG4QOOQ&object=files%252Fpm3u8%252Fvod%252F07%252F12%252F33913%252F0712bc453c98649bbc39b4f2117eef9f.m3u8&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJUeXBlIjoiQ29zQ2lUb2tlbiIsIkFwcElkIjoiMTI1MjQxMjIyMiIsIkJ1Y2tldElkIjoidmlkZW8tZW5jcnlwdC0xMjUyNDEyMjIyIiwiT2JqZWN0IjoiZmlsZXMlMkZwbTN1OCUyRnZvZCUyRjA3JTJGMTIlMkYzMzkxMyUyRjA3MTJiYzQ1M2M5ODY0OWJiYzM5YjRmMjExN2VlZjlmLm0zdTgiLCJJc3N1ZXIiOiJjbGllbnQiLCJJc3N1ZWRUaW1lU3RhbXAiOjE3NTU4NjgxOTMsIkV4cGlyZVRpbWVTdGFtcCI6MTc1NTk1NDU5MywiUmFuZG9tIjowLCJVc2FnZUxpbWl0Ijo1MDAwMCwiUHJvdGVjdFNjaGVtYSI6InJzYTEwMjQiLCJQdWJsaWNLZXkiOiJMUzB0TFMxQ1JVZEpUaUJRVlVKTVNVTWdTMFZaTFMwdExTMEtUVWxIWmsxQk1FZERVM0ZIVTBsaU0wUlJSVUpCVVZWQlFUUkhUa0ZFUTBKcFVVdENaMUZFWlRkbmMxSjZVMEphTlc1NlJEUlpZVTFaUVZoT1YxaFZNZ293VWpKRGRVZ3lRVGMyYVRoSFdYcEZTM0JuY2poeVoxWldVWEV2WTNsaVZXbERWRkVyTm1kT1RGUjJXRVZoVmxwekx6QkNjRzl1WjNaU1NYbEtkbVZTQ2t4TlJsaDVWMG80UzJWNFdEZDNOM1ZNY1dOVFFsWjJjMUI1TUdjNVIyZEtWVEk1Y2xsR2NpOVdWMDlTVEc5dmFrOUVTa3BpYTNsSFNWZ3hRVlpNT0ZjS01WSkxVbUpUTjJGUlJtZzBkRE5wWlVGUlNVUkJVVUZDQ2kwdExTMHRSVTVFSUZCVlFreEpReUJMUlZrdExTMHRMUW89IiwiUHJvdGVjdENvbnRlbnRLZXkiOjF9.nZVjCEBMPVeo6RTDjSe9G9fF_Jse6bms5L74hfNttk8",IV=0x********************************
#EXTINF:5,
0712bc453c98649bbc39b4f2117eef9f-0.ts'''
        
        # 分析M3U8
        segments = self.analyze_real_m3u8(real_m3u8)
        
        # 模拟密钥响应（用户提供的hex数据）
        # 原始数据: 49 12 A9 24 46 1C 98 DC B0 8D 5B BD 4A 0C C5 41 ...
        key_response_hex = "4912A924461C98DCB08D5BBD4A0CC5415E27BFC89E29E7A0A8738516459E88A8A2B2E41A69E94CD04A6534A40BB4408BF7B8A4DF0A7F0D5FA777AC5571B0F49EABFD90EC419C195300262059B5CEE73F979287327A92077D36B3E1F55A293D80340DAFCD79E86D99E21DEBF31DD866187E9FBE0E8AC7EF6B321BD96530850"

        try:
            key_response_bytes = binascii.unhexlify(key_response_hex)
        except binascii.Error:
            # 如果hex字符串长度为奇数，添加一个0
            key_response_hex = key_response_hex + "0"
            key_response_bytes = binascii.unhexlify(key_response_hex)
        
        print(f"\n密钥响应数据: {len(key_response_bytes)} 字节")
        
        # 尝试解密密钥
        key_candidates = self.decrypt_kms_key(key_response_bytes)
        
        # 模拟TS分段数据进行测试
        print(f"\n=== 解密测试 ===")
        print("注意: 需要真实的TS分段数据进行完整测试")
        
        return {
            'jwt_payload': self.jwt_payload,
            'kms_info': self.kms_info,
            'iv': self.iv,
            'key_candidates': key_candidates
        }

def main():
    """主函数"""
    decryptor = ClassInM3U8Decryptor()
    
    # 执行演示
    result = decryptor.demonstrate_decryption()
    
    print(f"\n" + "=" * 60)
    print("解密分析完成")
    print("=" * 60)
    
    print("\n【关键发现】:")
    print("1. 使用JWT Token进行认证")
    print("2. 通过腾讯云KMS管理密钥")
    print("3. 使用AES-128 CBC加密TS分段")
    print("4. IV固定为: ********************************")
    
    print("\n【下一步行动】:")
    print("1. 获取真实的TS分段数据")
    print("2. 使用候选密钥尝试解密")
    print("3. 验证解密结果（TS文件应以0x47开头）")
    print("4. 如果失败，需要进一步分析KMS解密逻辑")

if __name__ == "__main__":
    main()
