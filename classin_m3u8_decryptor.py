#!/usr/bin/env python3
"""
ClassIn M3U8 视频解密器
基于ClassIn应用源代码分析的解密实现

作者: AI助手
版本: 1.0.0
时间: 2025年1月21日

基于源代码分析发现的架构：
- ExoPlayer HLS支持 (com.google.android.exoplayer:exoplayer-hls:2.18.1)
- 加密核心库 (cn.eeo.android.core:encryption:1.0.9)
- 腾讯云COS支持 (com.qcloud.cos:cos-android:5.9.8)
- AES-128-CBC解密 (org.bouncycastle:bcprov-jdk15to18:1.69)
"""

import os
import re
import time
import json
import base64
import hashlib
import logging
import requests
from urllib.parse import urlparse, parse_qs, urljoin, unquote
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from typing import Optional, List, Dict, Tuple
import subprocess

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('classin_decryptor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class M3U8Info:
    """M3U8文件信息"""
    url: str
    method: str = ""
    key_uri: str = ""
    iv: str = ""
    segments: List[str] = None
    base_url: str = ""
    
    def __post_init__(self):
        if self.segments is None:
            self.segments = []
        if not self.base_url:
            self.base_url = '/'.join(self.url.split('/')[:-1]) + '/'

@dataclass
class EncryptionKey:
    """加密密钥信息"""
    key: bytes
    iv: bytes
    method: str = "AES-128"

class ClassInM3U8Decryptor:
    """ClassIn M3U8解密器主类
    
    基于源代码分析实现的解密器，支持：
    - 标准HLS AES-128-CBC解密
    - 腾讯云COS密钥管理
    - ExoPlayer兼容的解密流程
    """
    
    def __init__(self, output_dir: str = "decrypted_videos"):
        """初始化解密器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'ClassIn/6.0.1.1081 AVPlayer/5.15.1',
            'Accept': '*/*',
            'Connection': 'keep-alive',
            'Icy-MetaData': '1'
        })
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        logger.info(f"ClassIn M3U8解密器初始化完成，输出目录: {output_dir}")
    
    def parse_m3u8(self, m3u8_url: str, content: str = None) -> M3U8Info:
        """解析M3U8文件
        
        基于ExoPlayer HLS解析器的实现逻辑
        
        Args:
            m3u8_url: M3U8文件URL
            content: M3U8文件内容（可选）
            
        Returns:
            M3U8Info对象
        """
        logger.info(f"开始解析M3U8文件: {m3u8_url}")
        
        if content is None:
            try:
                response = self.session.get(m3u8_url)
                response.raise_for_status()
                content = response.text
                logger.info(f"成功下载M3U8文件，大小: {len(content)} 字符")
            except Exception as e:
                logger.error(f"下载M3U8文件失败: {e}")
                raise
        
        m3u8_info = M3U8Info(url=m3u8_url)
        lines = content.strip().split('\n')
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 解析加密密钥信息
            if line.startswith('#EXT-X-KEY:'):
                key_attrs = self._parse_attributes(line[11:])
                m3u8_info.method = key_attrs.get('METHOD', '')
                m3u8_info.key_uri = key_attrs.get('URI', '').strip('"')
                m3u8_info.iv = key_attrs.get('IV', '').strip('"')
                
                logger.info(f"发现加密信息 - 方法: {m3u8_info.method}, IV: {m3u8_info.iv}")
                
            # 解析视频片段
            elif line and not line.startswith('#'):
                # 构建完整URL
                if line.startswith('http'):
                    segment_url = line
                else:
                    segment_url = urljoin(m3u8_info.base_url, line)
                
                m3u8_info.segments.append(segment_url)
        
        logger.info(f"M3U8解析完成 - 找到 {len(m3u8_info.segments)} 个视频片段")
        return m3u8_info
    
    def _parse_attributes(self, attr_string: str) -> Dict[str, str]:
        """解析M3U8属性字符串
        
        基于ExoPlayer的属性解析实现
        """
        attributes = {}
        # 使用正则表达式解析属性
        pattern = r'([A-Z-]+)=(?:"([^"]*)"|([^,]*))'
        matches = re.findall(pattern, attr_string)
        
        for match in matches:
            key = match[0]
            value = match[1] if match[1] else match[2]
            attributes[key] = value
        
        return attributes
    
    def fetch_decryption_key(self, key_uri: str, base_url: str) -> bytes:
        """获取解密密钥
        
        基于腾讯云COS密钥管理服务的实现
        
        Args:
            key_uri: 密钥URI
            base_url: 基础URL
            
        Returns:
            16字节的AES密钥
        """
        logger.info(f"开始获取解密密钥: {key_uri}")
        
        # 构建完整的密钥URL
        if key_uri.startswith('http'):
            key_url = key_uri
        else:
            key_url = urljoin(base_url, key_uri)
        
        try:
            # 发送密钥请求
            response = self.session.get(key_url)
            response.raise_for_status()
            
            # 获取密钥数据
            key_data = response.content
            
            logger.info(f"成功获取密钥，长度: {len(key_data)} 字节")
            logger.debug(f"密钥数据 (hex): {key_data.hex()}")
            
            # 验证密钥长度
            if len(key_data) != 16:
                logger.warning(f"密钥长度异常: {len(key_data)} 字节，期望 16 字节")
            
            return key_data
            
        except Exception as e:
            logger.error(f"获取解密密钥失败: {e}")
            raise
    
    def decrypt_ts_segment(self, encrypted_data: bytes, key: bytes, iv: bytes) -> bytes:
        """解密TS片段
        
        基于BouncyCastle AES-128-CBC实现
        
        Args:
            encrypted_data: 加密的TS数据
            key: AES密钥
            iv: 初始化向量
            
        Returns:
            解密后的TS数据
        """
        try:
            # 创建AES解密器
            cipher = AES.new(key, AES.MODE_CBC, iv)
            
            # 解密数据
            decrypted_data = cipher.decrypt(encrypted_data)
            
            # 去除PKCS7填充
            try:
                decrypted_data = unpad(decrypted_data, AES.block_size)
            except ValueError:
                # 如果去填充失败，可能数据本身没有填充
                logger.warning("去填充失败，使用原始解密数据")
            
            return decrypted_data
            
        except Exception as e:
            logger.error(f"解密TS片段失败: {e}")
            raise
    
    def download_and_decrypt_segment(self, segment_url: str, key: bytes, iv: bytes, 
                                   segment_index: int) -> Tuple[int, bytes]:
        """下载并解密单个TS片段
        
        Args:
            segment_url: 片段URL
            key: 解密密钥
            iv: 初始化向量
            segment_index: 片段索引
            
        Returns:
            (索引, 解密后的数据)
        """
        try:
            # 下载片段
            response = self.session.get(segment_url)
            response.raise_for_status()
            
            encrypted_data = response.content
            logger.debug(f"下载片段 {segment_index}，大小: {len(encrypted_data)} 字节")
            
            # 解密片段
            decrypted_data = self.decrypt_ts_segment(encrypted_data, key, iv)
            
            logger.debug(f"解密片段 {segment_index} 完成，解密后大小: {len(decrypted_data)} 字节")
            
            return segment_index, decrypted_data
            
        except Exception as e:
            logger.error(f"处理片段 {segment_index} 失败: {e}")
            raise
    
    def merge_ts_segments(self, segments_data: List[Tuple[int, bytes]], 
                         output_file: str) -> str:
        """合并TS片段为完整视频
        
        Args:
            segments_data: 片段数据列表 [(索引, 数据), ...]
            output_file: 输出文件路径
            
        Returns:
            输出文件路径
        """
        logger.info(f"开始合并 {len(segments_data)} 个TS片段")
        
        # 按索引排序
        segments_data.sort(key=lambda x: x[0])
        
        # 直接连接TS片段
        output_path = os.path.join(self.output_dir, output_file)
        
        with open(output_path, 'wb') as f:
            for index, data in segments_data:
                f.write(data)
                logger.debug(f"写入片段 {index}")
        
        logger.info(f"TS片段合并完成: {output_path}")
        
        # 尝试使用FFmpeg转换为MP4
        mp4_path = output_path.replace('.ts', '.mp4')
        try:
            self._convert_to_mp4(output_path, mp4_path)
            return mp4_path
        except Exception as e:
            logger.warning(f"转换为MP4失败: {e}，保留TS格式")
            return output_path
    
    def _convert_to_mp4(self, input_ts: str, output_mp4: str):
        """使用FFmpeg将TS转换为MP4
        
        Args:
            input_ts: 输入TS文件
            output_mp4: 输出MP4文件
        """
        cmd = [
            'ffmpeg', '-i', input_ts, 
            '-c', 'copy', 
            '-y',  # 覆盖输出文件
            output_mp4
        ]
        
        logger.info(f"开始转换为MP4: {output_mp4}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                logger.info("FFmpeg转换成功")
                # 删除原始TS文件
                os.remove(input_ts)
            else:
                logger.error(f"FFmpeg转换失败: {result.stderr}")
                raise subprocess.CalledProcessError(result.returncode, cmd)
        except subprocess.TimeoutExpired:
            logger.error("FFmpeg转换超时")
            raise
        except FileNotFoundError:
            logger.error("FFmpeg未找到，请确保已安装FFmpeg")
            raise
    
    def decrypt_m3u8_video(self, m3u8_url: str, output_filename: str = None, 
                          max_workers: int = 5) -> str:
        """解密M3U8视频的主方法
        
        Args:
            m3u8_url: M3U8播放列表URL
            output_filename: 输出文件名（可选）
            max_workers: 并发下载线程数
            
        Returns:
            输出文件路径
        """
        logger.info(f"开始解密M3U8视频: {m3u8_url}")
        
        try:
            # 1. 解析M3U8文件
            m3u8_info = self.parse_m3u8(m3u8_url)
            
            if not m3u8_info.segments:
                raise ValueError("未找到视频片段")
            
            # 2. 获取解密密钥
            if m3u8_info.method == "AES-128" and m3u8_info.key_uri:
                key_data = self.fetch_decryption_key(m3u8_info.key_uri, m3u8_info.base_url)
                
                # 解析IV
                if m3u8_info.iv.startswith('0x'):
                    iv_data = bytes.fromhex(m3u8_info.iv[2:])
                else:
                    iv_data = m3u8_info.iv.encode('utf-8')[:16].ljust(16, b'\x00')
                
                logger.info(f"密钥和IV准备完成，密钥长度: {len(key_data)}, IV长度: {len(iv_data)}")
            else:
                raise ValueError(f"不支持的加密方法: {m3u8_info.method}")
            
            # 3. 并发下载和解密片段
            segments_data = []
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有下载任务
                future_to_index = {
                    executor.submit(
                        self.download_and_decrypt_segment, 
                        segment_url, key_data, iv_data, index
                    ): index
                    for index, segment_url in enumerate(m3u8_info.segments)
                }
                
                # 收集结果
                for future in as_completed(future_to_index):
                    try:
                        index, decrypted_data = future.result()
                        segments_data.append((index, decrypted_data))
                        
                        # 显示进度
                        progress = len(segments_data) / len(m3u8_info.segments) * 100
                        logger.info(f"解密进度: {progress:.1f}% ({len(segments_data)}/{len(m3u8_info.segments)})")
                        
                    except Exception as e:
                        index = future_to_index[future]
                        logger.error(f"片段 {index} 处理失败: {e}")
                        raise
            
            # 4. 合并片段
            if output_filename is None:
                # 从URL生成文件名
                parsed_url = urlparse(m3u8_url)
                filename = os.path.basename(parsed_url.path)
                if filename.endswith('.m3u8'):
                    filename = filename[:-5]
                output_filename = f"{filename}_{int(time.time())}.ts"
            
            output_path = self.merge_ts_segments(segments_data, output_filename)
            
            logger.info(f"视频解密完成: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"解密过程中发生错误: {e}")
            raise

def main():
    """主函数示例"""
    # 示例用法
    decryptor = ClassInM3U8Decryptor()
    
    # 你提供的M3U8 URL示例
    m3u8_url = "https://t0s-cdn.eeo.cn/files/pm3u8/vod/07/12/33913/0712bc453c98649bbc39b4f2117eef9f.m3u8"
    
    # 你需要添加必要的查询参数
    m3u8_url_with_params = f"{m3u8_url}?expires=43200&ci-process=getplaylist&tokenType=JwtToken&token=YOUR_TOKEN_HERE"
    
    try:
        output_file = decryptor.decrypt_m3u8_video(
            m3u8_url=m3u8_url_with_params,
            output_filename="classin_video.ts",
            max_workers=3
        )
        
        print(f"解密成功！输出文件: {output_file}")
        
    except Exception as e:
        print(f"解密失败: {e}")

if __name__ == "__main__":
    main()