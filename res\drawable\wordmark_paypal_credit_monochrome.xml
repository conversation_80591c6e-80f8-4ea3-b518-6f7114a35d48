<?xml version="1.0" encoding="utf-8"?>
<vector android:height="20.0dip" android:width="134.0dip" android:viewportWidth="134.0" android:viewportHeight="20.0"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillColor="#fffefefe" android:pathData="M111.197,13.96L111.877,13.96C113.687,13.96 115.388,12.973 115.759,10.705C116.084,8.623 114.893,7.451 112.944,7.451L112.445,7.451C112.323,7.451 112.219,7.539 112.2,7.659L111.197,13.96ZM109.547,5.248C109.579,5.042 109.758,4.89 109.967,4.89L113.903,4.89C117.151,4.89 119.425,7.435 118.914,10.705C118.389,13.976 115.295,16.521 112.063,16.521L108.053,16.521C107.901,16.521 107.784,16.385 107.808,16.235L109.547,5.248Z" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M104.449,7.809L104.197,9.394L107.325,9.394C107.477,9.394 107.594,9.53 107.57,9.68L107.269,11.596C107.237,11.803 107.059,11.955 106.849,11.955L104.157,11.955C103.948,11.955 103.77,12.106 103.737,12.311L103.47,13.96L106.784,13.96C106.936,13.96 107.052,14.096 107.029,14.246L106.728,16.162C106.696,16.369 106.517,16.521 106.307,16.521L100.327,16.521C100.174,16.521 100.058,16.385 100.082,16.235L101.82,5.248C101.853,5.042 102.031,4.89 102.24,4.89L108.222,4.89C108.374,4.89 108.491,5.026 108.467,5.176L108.166,7.092C108.134,7.299 107.956,7.451 107.746,7.451L104.869,7.451C104.66,7.451 104.482,7.602 104.449,7.809" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M121.562,16.521L119.184,16.521C119.032,16.521 118.915,16.385 118.939,16.235L120.678,5.248C120.71,5.042 120.889,4.89 121.098,4.89L123.476,4.89C123.628,4.89 123.744,5.026 123.721,5.176L121.982,16.163C121.949,16.369 121.771,16.521 121.562,16.521" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M94.049,10.104L94.343,10.104C95.333,10.104 96.471,9.918 96.678,8.654C96.885,7.389 96.224,7.208 95.167,7.204L94.737,7.204C94.608,7.204 94.498,7.298 94.477,7.425L94.049,10.104ZM99.253,16.521L96.137,16.521C96.004,16.521 95.884,16.445 95.827,16.326L93.77,12.047L93.739,12.047L93.074,16.231C93.047,16.398 92.903,16.521 92.734,16.521L90.287,16.521C90.134,16.521 90.018,16.385 90.041,16.235L91.791,5.18C91.817,5.013 91.962,4.89 92.131,4.89L96.369,4.89C98.673,4.89 100.251,5.985 99.864,8.469C99.601,10.073 98.488,11.461 96.786,11.754L99.465,16.145C99.565,16.31 99.446,16.521 99.253,16.521L99.253,16.521Z" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M127.992,16.521L125.614,16.521C125.462,16.521 125.346,16.385 125.37,16.235L126.762,7.451L124.547,7.451C124.395,7.451 124.278,7.315 124.302,7.165L124.603,5.249C124.635,5.042 124.813,4.89 125.023,4.89L132.412,4.89C132.564,4.89 132.681,5.026 132.657,5.176L132.356,7.092C132.324,7.298 132.145,7.451 131.936,7.451L129.794,7.451L128.412,16.163C128.38,16.369 128.201,16.521 127.992,16.521" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M90.196,8.195C90.161,8.411 89.897,8.489 89.743,8.332C89.224,7.801 88.462,7.521 87.641,7.521C85.786,7.521 84.319,8.939 84.025,10.742C83.747,12.576 84.798,13.901 86.683,13.901C87.46,13.901 88.285,13.606 88.97,13.113C89.159,12.977 89.419,13.14 89.382,13.37L88.95,16.031C88.925,16.19 88.809,16.319 88.655,16.364C87.732,16.635 87.023,16.829 86.158,16.829C81.13,16.829 80.326,12.547 80.585,10.726C81.312,5.621 85.466,4.452 88.012,4.593C88.832,4.638 89.567,4.747 90.287,5.016C90.52,5.104 90.659,5.343 90.619,5.588L90.196,8.195Z" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M59.502,9.02C59.274,10.517 58.127,10.517 57.018,10.517L56.386,10.517L56.829,7.723C56.856,7.554 57.002,7.429 57.173,7.429L57.463,7.429C58.218,7.429 58.931,7.429 59.299,7.858C59.519,8.115 59.586,8.495 59.502,9.02M59.02,5.115L54.836,5.115C54.55,5.115 54.306,5.322 54.262,5.604L52.57,16.297C52.537,16.508 52.7,16.699 52.914,16.699L55.061,16.699C55.261,16.699 55.432,16.554 55.463,16.356L55.943,13.325C55.987,13.043 56.231,12.836 56.517,12.836L57.841,12.836C60.596,12.836 62.187,11.507 62.602,8.872C62.79,7.72 62.61,6.814 62.069,6.18C61.474,5.484 60.419,5.115 59.02,5.115" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M29.663,9.02C29.434,10.517 28.287,10.517 27.178,10.517L26.547,10.517L26.989,7.723C27.016,7.554 27.162,7.429 27.334,7.429L27.623,7.429C28.378,7.429 29.092,7.429 29.459,7.858C29.679,8.115 29.746,8.495 29.663,9.02M29.18,5.115L24.996,5.115C24.71,5.115 24.467,5.322 24.422,5.604L22.73,16.297C22.697,16.508 22.861,16.699 23.075,16.699L25.072,16.699C25.358,16.699 25.602,16.491 25.647,16.21L26.103,13.325C26.148,13.043 26.391,12.836 26.677,12.836L28.001,12.836C30.757,12.836 32.347,11.507 32.763,8.872C32.95,7.72 32.77,6.814 32.229,6.18C31.634,5.484 30.58,5.115 29.18,5.115" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M38.893,12.859C38.699,14 37.791,14.766 36.631,14.766C36.051,14.766 35.585,14.58 35.286,14.227C34.99,13.877 34.878,13.379 34.972,12.825C35.153,11.694 36.076,10.903 37.218,10.903C37.786,10.903 38.248,11.091 38.553,11.447C38.86,11.806 38.98,12.307 38.893,12.859M41.685,8.973L39.681,8.973C39.51,8.973 39.364,9.097 39.337,9.266L39.249,9.824L39.109,9.622C38.675,8.994 37.708,8.785 36.742,8.785C34.529,8.785 32.638,10.456 32.27,12.801C32.079,13.971 32.351,15.089 33.016,15.869C33.628,16.586 34.5,16.884 35.539,16.884C37.323,16.884 38.313,15.742 38.313,15.742L38.224,16.297C38.19,16.508 38.354,16.699 38.568,16.699L40.372,16.699C40.658,16.699 40.901,16.492 40.946,16.21L42.029,9.374C42.063,9.163 41.899,8.973 41.685,8.973" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M68.732,12.859C68.539,14 67.63,14.766 66.471,14.766C65.89,14.766 65.425,14.58 65.126,14.227C64.83,13.877 64.718,13.379 64.812,12.825C64.992,11.694 65.916,10.903 67.057,10.903C67.626,10.903 68.088,11.091 68.393,11.447C68.699,11.806 68.82,12.307 68.732,12.859M71.524,8.973L69.521,8.973C69.349,8.973 69.203,9.097 69.176,9.266L69.088,9.824L68.948,9.622C68.515,8.994 67.548,8.785 66.582,8.785C64.369,8.785 62.478,10.456 62.11,12.801C61.919,13.971 62.191,15.089 62.856,15.869C63.467,16.586 64.34,16.884 65.379,16.884C67.163,16.884 68.152,15.742 68.152,15.742L68.063,16.297C68.03,16.508 68.193,16.699 68.408,16.699L70.212,16.699C70.498,16.699 70.741,16.492 70.786,16.21L71.869,9.374C71.902,9.163 71.739,8.973 71.524,8.973" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M52.354,8.973L50.341,8.973C50.148,8.973 49.968,9.068 49.86,9.227L47.082,13.304L45.905,9.386C45.831,9.141 45.605,8.973 45.348,8.973L43.369,8.973C43.13,8.973 42.962,9.207 43.039,9.432L45.256,15.92L43.171,18.853C43.007,19.083 43.172,19.401 43.455,19.401L45.467,19.401C45.658,19.401 45.836,19.308 45.944,19.152L52.641,9.518C52.801,9.288 52.636,8.973 52.354,8.973" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M73.885,5.409L72.168,16.297C72.135,16.508 72.299,16.699 72.513,16.699L74.24,16.699C74.526,16.699 74.769,16.492 74.814,16.21L76.507,5.517C76.54,5.306 76.377,5.115 76.162,5.115L74.23,5.115C74.058,5.115 73.912,5.24 73.885,5.409" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M8.926,18.643L9.488,15.088L9.524,14.891C9.551,14.72 9.639,14.564 9.772,14.45C9.905,14.338 10.074,14.275 10.248,14.275L10.695,14.275C11.439,14.275 12.123,14.196 12.727,14.04C13.373,13.873 13.946,13.614 14.432,13.269C14.948,12.903 15.381,12.431 15.72,11.865C16.077,11.268 16.339,10.549 16.499,9.729C16.641,9.004 16.667,8.355 16.579,7.8C16.484,7.214 16.258,6.714 15.905,6.313C15.691,6.07 15.418,5.86 15.092,5.688L15.084,5.684L15.084,5.675C15.198,4.951 15.194,4.348 15.071,3.829C14.948,3.308 14.699,2.84 14.31,2.397C13.502,1.48 12.033,1.015 9.944,1.015L4.206,1.015C4.014,1.015 3.828,1.083 3.682,1.207C3.536,1.331 3.44,1.504 3.41,1.693L1.02,16.794C0.999,16.932 1.038,17.072 1.129,17.178C1.22,17.284 1.353,17.345 1.493,17.345L5.052,17.345L5.049,17.362L4.805,18.907C4.786,19.027 4.82,19.149 4.9,19.241C4.979,19.334 5.094,19.387 5.216,19.387L8.201,19.387C8.369,19.387 8.531,19.327 8.658,19.218C8.786,19.11 8.87,18.96 8.896,18.795L8.926,18.643Z" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillAlpha="0.68" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M6.88,5.694C6.918,5.455 7.072,5.259 7.28,5.16C7.374,5.115 7.479,5.09 7.59,5.09L12.087,5.09C12.62,5.09 13.117,5.125 13.571,5.198C13.701,5.218 13.827,5.242 13.95,5.269C14.072,5.297 14.192,5.327 14.307,5.36C14.365,5.377 14.421,5.395 14.477,5.413C14.7,5.487 14.908,5.574 15.099,5.675C15.324,4.244 15.097,3.27 14.321,2.387C13.465,1.416 11.92,1 9.944,1L4.206,1C3.802,1 3.458,1.293 3.395,1.691L1.006,16.792C0.959,17.09 1.19,17.36 1.493,17.36L5.035,17.36L5.924,11.736L6.88,5.694Z" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#fffefefe" android:pathData="M14.307,5.36C14.192,5.327 14.072,5.297 13.95,5.269C13.827,5.242 13.701,5.218 13.571,5.197C13.117,5.125 12.62,5.09 12.087,5.09L7.59,5.09C7.479,5.09 7.374,5.115 7.28,5.16C7.072,5.259 6.918,5.455 6.88,5.694L5.924,11.736L5.897,11.912C5.96,11.514 6.301,11.221 6.705,11.221L8.388,11.221C11.695,11.221 14.284,9.882 15.04,6.01C15.063,5.895 15.082,5.784 15.099,5.675C14.908,5.574 14.7,5.487 14.477,5.413C14.421,5.395 14.365,5.377 14.307,5.36" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillAlpha="0.7" android:fillType="evenOdd" />
</vector>
