<!doctype html><html lang="en"><head><meta charset="utf-8"/><title>EEO APP</title><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"/><meta name="theme-color" content="#000000"/><meta name="apple-mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-status-bar-style" content="black"/><meta name="description" content="Web site created using create-react-app"/><meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"/><meta http-equiv="Pragma" content="no-cache"/><meta http-equiv="Expires" content="0"/><meta name="renderer" content="webkit"/><meta content="always" name="referrer"/><meta http-equiv="x-dns-prefetch-control" content="on"/><link rel="dns-prefetch" href="//static.eeo.cn"/><link rel="dns-prefetch" href="//static.classin.com"/><style id="lms-mobile-skeletonStyle">#lms-mobile-skeleton{min-height: 800px;height:100%;text-align:center;padding:0 16px 32px;position: fixed;left: 0;right: 0;top: 0;background-color: #ffffff;z-index: 1111;}#lms-mobile-skeleton>*{-webkit-transform:translateZ(0)}#lms-mobile-skeleton .item{display:flex}#lms-mobile-skeleton .title{width:45%;height:20px;border-radius:2px;margin-bottom:6px;margin-top:24px}#lms-mobile-skeleton .line{height:20px;border-radius:2px;margin-top:10px;margin-bottom:6px}#lms-mobile-skeleton .left{width:32px;margin-right:10px}#lms-mobile-skeleton .right{flex:1}#lms-mobile-skeleton .animated{animation:lms-skeleton-loading 1.4s ease infinite;background:linear-gradient(90deg,rgb(190,190,190,0.2) 25%,rgb(129,129,129,0.2) 37%,rgb(190,190,190,0.2) 63%);background-size:400% 100%}@keyframes lms-skeleton-loading{0%{background-position:100% 50%}to{background-position:0 50%}}</style><link href="./index.css" rel="stylesheet"></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div id="lms-mobile-skeleton"><div class="animated title"></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="animated title"></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="animated title"></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="animated title"></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div><div class="item"><div class="animated line left"></div><div class="animated line right"></div></div></div><script>var getParam = function(){
      var url = location.search || location.href;
	    var _ParamObj = new Object();
      //判断获取url中"?"符后的字串，并截取存储
      if (url.indexOf("?") != -1) {
        var paramStr = url.substr(url.indexOf("?") + 1);
        var paramArray = paramStr.split("&");
        for (var i = 0; i < paramArray.length; i++) {
          _ParamObj[paramArray[i].split("=")[0]] = (paramArray[i].split("=")[1]);
        }
      }
      return _ParamObj;
    }

    let cd = getParam().template_condition || '';
    if (cd && cd === 'compose_paper') {
      document.getElementById('lms-mobile-skeleton').style.display = 'none';
    } else {
      window.lmsWidget && window.lmsWidget.scrollerEnable && window.lmsWidget.scrollerEnable(0);
      // 兜底操作，10秒后主动关闭
      setTimeout(() => {
        window.lmsWidget && window.lmsWidget.scrollerEnable && window.lmsWidget.scrollerEnable(1);
      }, 10000);
    }</script><div id="root"></div><script src="./assets/locale-font-family.js"></script><script nonce async src="./assets/load-mathjax-v1.js"></script><script defer="defer" src="./index.js"></script></body></html>