meta-ilst=org.mp4parser.boxes.apple.AppleItemListBox
rmra=org.mp4parser.boxes.apple.AppleReferenceMovieBox
rmda=org.mp4parser.boxes.apple.********************************
rmdr=org.mp4parser.boxes.apple.AppleDataRateBox
rdrf=org.mp4parser.boxes.apple.AppleDataReferenceBox

wave=org.mp4parser.boxes.apple.AppleWaveBox

udta-ccid=org.mp4parser.tools.boxes.odf.OmaDrmContentIdBox
udta-yrrc=org.mp4parser.boxes.threegpp.ts26244.RecordingYearBox
udta-titl=org.mp4parser.boxes.threegpp.ts26244.TitleBox
udta-dscp=org.mp4parser.boxes.threegpp.ts26244.DescriptionBox
udta-albm=org.mp4parser.boxes.threegpp.ts26244.AlbumBox
udta-cprt=org.mp4parser.boxes.threegpp.ts26244.CopyrightBox
udta-gnre=org.mp4parser.boxes.threegpp.ts26244.GenreBox
udta-perf=org.mp4parser.boxes.threegpp.ts26244.PerformerBox
udta-auth=org.mp4parser.boxes.threegpp.ts26244.AuthorBox
udta-kywd=org.mp4parser.boxes.threegpp.ts26244.KeywordsBox
udta-loci=org.mp4parser.boxes.threegpp.ts26244.LocationInformationBox
udta-rtng=org.mp4parser.boxes.threegpp.ts26244.RatingBox
udta-clsf=org.mp4parser.boxes.threegpp.ts26244.ClassificationBox




tx3g=org.mp4parser.boxes.sampleentry.TextSampleEntry
stsd-text=org.mp4parser.boxes.apple.QuicktimeTextSampleEntry
enct=org.mp4parser.boxes.sampleentry.TextSampleEntry(type)
samr=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
sawb=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
mp4a=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
drms=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
stsd-alac=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
mp4s=org.mp4parser.boxes.sampleentry.MpegSampleEntry(type)
owma=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
ac-3=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
dac3=org.mp4parser.boxes.dolby.AC3SpecificBox
ec-3=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
dec3=org.mp4parser.boxes.dolby.EC3SpecificBox
stsd-lpcm=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
stsd-dtsc=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
stsd-dtsh=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
stsd-dtsl=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
ddts=org.mp4parser.boxes.dolby.DTSSpecificBox
stsd-dtse=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
stsd-mlpa=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
dmlp=org.mp4parser.boxes.dolby.MLPSpecificBox
enca=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
sowt=org.mp4parser.boxes.sampleentry.AudioSampleEntry(type)
encv=org.mp4parser.boxes.sampleentry.VisualSampleEntry(type)
apcn=org.mp4parser.boxes.sampleentry.VisualSampleEntry(type)
mp4v=org.mp4parser.boxes.sampleentry.VisualSampleEntry(type)
s263=org.mp4parser.boxes.sampleentry.VisualSampleEntry(type)
avc1=org.mp4parser.boxes.sampleentry.VisualSampleEntry(type)
avc2=org.mp4parser.boxes.sampleentry.VisualSampleEntry(type)
dvhe=org.mp4parser.boxes.sampleentry.VisualSampleEntry(type)
dvav=org.mp4parser.boxes.sampleentry.VisualSampleEntry(type)
avc3=org.mp4parser.boxes.sampleentry.VisualSampleEntry(type)
avc4=org.mp4parser.boxes.sampleentry.VisualSampleEntry(type)
hev1=org.mp4parser.boxes.sampleentry.VisualSampleEntry(type)
hvc1=org.mp4parser.boxes.sampleentry.VisualSampleEntry(type)
ovc1=org.mp4parser.boxes.sampleentry.Ovc1VisualSampleEntryImpl
stpp=org.mp4parser.boxes.iso14496.part30.XMLSubtitleSampleEntry
avcC=org.mp4parser.boxes.iso14496.part15.AvcConfigurationBox
hvcC=org.mp4parser.boxes.iso14496.part15.HevcConfigurationBox
alac=org.mp4parser.boxes.apple.AppleLosslessSpecificBox
btrt=org.mp4parser.boxes.iso14496.part12.BitRateBox
ftyp=org.mp4parser.boxes.iso14496.part12.FileTypeBox
mdat=org.mp4parser.boxes.iso14496.part12.MediaDataBox
moov=org.mp4parser.boxes.iso14496.part12.MovieBox
mvhd=org.mp4parser.boxes.iso14496.part12.MovieHeaderBox
trak=org.mp4parser.boxes.iso14496.part12.TrackBox
tkhd=org.mp4parser.boxes.iso14496.part12.TrackHeaderBox
edts=org.mp4parser.boxes.iso14496.part12.EditBox
elst=org.mp4parser.boxes.iso14496.part12.EditListBox
mdia=org.mp4parser.boxes.iso14496.part12.MediaBox
mdhd=org.mp4parser.boxes.iso14496.part12.MediaHeaderBox
hdlr=org.mp4parser.boxes.iso14496.part12.HandlerBox
minf=org.mp4parser.boxes.iso14496.part12.MediaInformationBox
vmhd=org.mp4parser.boxes.iso14496.part12.VideoMediaHeaderBox
smhd=org.mp4parser.boxes.iso14496.part12.SoundMediaHeaderBox
sthd=org.mp4parser.boxes.iso14496.part12.SubtitleMediaHeaderBox
hmhd=org.mp4parser.boxes.iso14496.part12.HintMediaHeaderBox
dinf=org.mp4parser.boxes.iso14496.part12.DataInformationBox
dref=org.mp4parser.boxes.iso14496.part12.DataReferenceBox
url\ =org.mp4parser.boxes.iso14496.part12.DataEntryUrlBox
urn\ =org.mp4parser.boxes.iso14496.part12.DataEntryUrnBox
stbl=org.mp4parser.boxes.iso14496.part12.SampleTableBox
ctts=org.mp4parser.boxes.iso14496.part12.CompositionTimeToSample
stsd=org.mp4parser.boxes.iso14496.part12.SampleDescriptionBox
stts=org.mp4parser.boxes.iso14496.part12.TimeToSampleBox
stss=org.mp4parser.boxes.iso14496.part12.SyncSampleBox
stsc=org.mp4parser.boxes.iso14496.part12.SampleToChunkBox
stsz=org.mp4parser.boxes.iso14496.part12.SampleSizeBox
stco=org.mp4parser.boxes.iso14496.part12.StaticChunkOffsetBox
subs=org.mp4parser.boxes.iso14496.part12.SubSampleInformationBox
udta=org.mp4parser.boxes.iso14496.part12.UserDataBox
skip=org.mp4parser.boxes.iso14496.part12.FreeSpaceBox
tref=org.mp4parser.boxes.iso14496.part12.TrackReferenceBox
iloc=org.mp4parser.boxes.iso14496.part12.ItemLocationBox
idat=org.mp4parser.boxes.iso14496.part12.ItemDataBox

damr=org.mp4parser.boxes.sampleentry.AmrSpecificBox
meta=org.mp4parser.boxes.iso14496.part12.MetaBox
ipro=org.mp4parser.boxes.iso14496.part12.ItemProtectionBox
sinf=org.mp4parser.boxes.iso14496.part12.ProtectionSchemeInformationBox
frma=org.mp4parser.boxes.iso14496.part12.OriginalFormatBox
schi=org.mp4parser.boxes.iso14496.part12.SchemeInformationBox
odaf=org.mp4parser.boxes.oma.OmaDrmAccessUnitFormatBox
schm=org.mp4parser.boxes.iso14496.part12.SchemeTypeBox
uuid=org.mp4parser.boxes.UserBox(userType)
free=org.mp4parser.boxes.iso14496.part12.FreeBox
styp=org.mp4parser.boxes.iso14496.part12.SegmentTypeBox
mvex=org.mp4parser.boxes.iso14496.part12.MovieExtendsBox
mehd=org.mp4parser.boxes.iso14496.part12.MovieExtendsHeaderBox
trex=org.mp4parser.boxes.iso14496.part12.TrackExtendsBox

moof=org.mp4parser.boxes.iso14496.part12.MovieFragmentBox
mfhd=org.mp4parser.boxes.iso14496.part12.MovieFragmentHeaderBox
traf=org.mp4parser.boxes.iso14496.part12.TrackFragmentBox
tfhd=org.mp4parser.boxes.iso14496.part12.TrackFragmentHeaderBox
trun=org.mp4parser.boxes.iso14496.part12.TrackRunBox
sdtp=org.mp4parser.boxes.iso14496.part12.SampleDependencyTypeBox
mfra=org.mp4parser.boxes.iso14496.part12.MovieFragmentRandomAccessBox
tfra=org.mp4parser.boxes.iso14496.part12.TrackFragmentRandomAccessBox
mfro=org.mp4parser.boxes.iso14496.part12.MovieFragmentRandomAccessOffsetBox
tfdt=org.mp4parser.boxes.iso14496.part12.TrackFragmentBaseMediaDecodeTimeBox
nmhd=org.mp4parser.boxes.iso14496.part12.NullMediaHeaderBox
gmhd=org.mp4parser.boxes.apple.GenericMediaHeaderAtom
gmhd-text=org.mp4parser.boxes.apple.GenericMediaHeaderTextAtom
gmin=org.mp4parser.boxes.apple.BaseMediaInfoAtom
cslg=org.mp4parser.boxes.iso14496.part12.CompositionToDecodeBox
pdin=org.mp4parser.boxes.iso14496.part12.ProgressiveDownloadInformationBox
bloc=org.mp4parser.boxes.dece.BaseLocationBox
ftab=org.mp4parser.boxes.threegpp.ts26245.FontTableBox
co64=org.mp4parser.boxes.iso14496.part12.ChunkOffset64BitBox
xml\ =org.mp4parser.boxes.iso14496.part12.XmlBox
avcn=org.mp4parser.boxes.dece.AvcNalUnitStorageBox
ainf=org.mp4parser.boxes.dece.AssetInformationBox
pssh=org.mp4parser.boxes.iso23001.part7.ProtectionSystemSpecificHeaderBox
trik=org.mp4parser.boxes.dece.TrickPlayBox
uuid[********************************]=org.mp4parser.boxes.microsoft.PiffSampleEncryptionBox
uuid[********************************]=org.mp4parser.boxes.microsoft.PiffTrackEncryptionBox
uuid[********************************]=org.mp4parser.boxes.microsoft.TfrfBox
uuid[********************************]=org.mp4parser.boxes.microsoft.TfxdBox
uuid[********************************]=org.mp4parser.boxes.microsoft.UuidBasedProtectionSystemSpecificHeaderBox
senc=org.mp4parser.boxes.iso23001.part7.SampleEncryptionBox
tenc=org.mp4parser.boxes.iso23001.part7.TrackEncryptionBox
amf0=org.mp4parser.boxes.adobe.ActionMessageFormat0SampleEntryBox

esds=org.mp4parser.boxes.iso14496.part14.ESDescriptorBox

tmcd=org.mp4parser.boxes.apple.TimeCodeBox
sidx=org.mp4parser.boxes.iso14496.part12.SegmentIndexBox

sbgp=org.mp4parser.boxes.samplegrouping.SampleToGroupBox
sgpd=org.mp4parser.boxes.samplegrouping.SampleGroupDescriptionBox

tapt=org.mp4parser.boxes.apple.TrackApertureModeDimensionAtom
clef=org.mp4parser.boxes.apple.CleanApertureAtom
prof=org.mp4parser.boxes.apple.TrackProductionApertureDimensionsAtom
enof=org.mp4parser.boxes.apple.TrackEncodedPixelsDimensionsAtom
pasp=org.mp4parser.boxes.apple.PixelAspectRationAtom
load=org.mp4parser.boxes.apple.TrackLoadSettingsAtom


default=org.mp4parser.boxes.UnknownBox(type)



\u00A9nam=org.mp4parser.boxes.apple.AppleNameBox
\u00A9ART=org.mp4parser.boxes.apple.AppleArtistBox
aART=org.mp4parser.boxes.apple.AppleArtist2Box
\u00A9alb=org.mp4parser.boxes.apple.AppleAlbumBox
\u00A9gen=org.mp4parser.boxes.apple.AppleGenreBox
gnre=org.mp4parser.boxes.apple.AppleGenreIDBox
#\u00A9day=AppleRecordingYearBox
\u00A9day=org.mp4parser.boxes.apple.AppleRecordingYear2Box
trkn=org.mp4parser.boxes.apple.AppleTrackNumberBox
cpil=org.mp4parser.boxes.apple.AppleCompilationBox
pgap=org.mp4parser.boxes.apple.AppleGaplessPlaybackBox
disk=org.mp4parser.boxes.apple.AppleDiskNumberBox
apID=org.mp4parser.boxes.apple.AppleAppleIdBox
cprt=org.mp4parser.boxes.apple.AppleCopyrightBox
atID=org.mp4parser.boxes.apple.Apple_atIDBox
geID=org.mp4parser.boxes.apple.Apple_geIDBox
sfID=org.mp4parser.boxes.apple.AppleCountryTypeBoxBox
desc=org.mp4parser.boxes.apple.AppleDescriptionBox
tvnn=org.mp4parser.boxes.apple.AppleTVNetworkBox
tvsh=org.mp4parser.boxes.apple.AppleTVShowBox
tven=org.mp4parser.boxes.apple.AppleTVEpisodeNumberBox
tvsn=org.mp4parser.boxes.apple.AppleTVSeasonBox
tves=org.mp4parser.boxes.apple.AppleTVEpisodeBox
xid\ =org.mp4parser.boxes.apple.Apple_xid_Box
flvr=org.mp4parser.boxes.apple.Apple_flvr_Box
sdes=org.mp4parser.boxes.apple.AppleShortDescriptionBox
ldes=org.mp4parser.boxes.apple.AppleLongDescriptionBox
soal=org.mp4parser.boxes.apple.AppleSortAlbumBox
purd=org.mp4parser.boxes.apple.ApplePurchaseDateBox
stik=org.mp4parser.boxes.apple.AppleMediaTypeBox


#added by Tobias Bley / UltraMixer (04/25/2014)
\u00A9cmt=org.mp4parser.boxes.apple.AppleCommentBox
tmpo=org.mp4parser.boxes.apple.AppleTempoBox
\u00A9too=org.mp4parser.boxes.apple.AppleEncoderBox
\u00A9wrt=org.mp4parser.boxes.apple.AppleTrackAuthorBox
\u00A9grp=org.mp4parser.boxes.apple.AppleGroupingBox
covr=org.mp4parser.boxes.apple.AppleCoverBox
\u00A9lyr=org.mp4parser.boxes.apple.AppleLyricsBox
cinf=org.mp4parser.boxes.dece.ContentInformationBox
tibr=org.mp4parser.boxes.iso14496.part15.TierBitRateBox
tiri=org.mp4parser.boxes.iso14496.part15.TierInfoBox
svpr=org.mp4parser.boxes.iso14496.part15.PriotityRangeBox
emsg=org.mp4parser.boxes.iso23009.part1.EventMessageBox
saio=org.mp4parser.boxes.iso14496.part12.SampleAuxiliaryInformationOffsetsBox
saiz=org.mp4parser.boxes.iso14496.part12.SampleAuxiliaryInformationSizesBox
vttC=org.mp4parser.boxes.iso14496.part30.WebVTTConfigurationBox
vlab=org.mp4parser.boxes.iso14496.part30.WebVTTSourceLabelBox
wvtt=org.mp4parser.boxes.iso14496.part30.WebVTTSampleEntry


#added by marwatk (2/24/2014)
Xtra=org.mp4parser.boxes.microsoft.XtraBox
\u00A9xyz=org.mp4parser.boxes.apple.AppleGPSCoordinatesBox

hint=org.mp4parser.boxes.iso14496.part12.TrackReferenceTypeBox(type)
cdsc=org.mp4parser.boxes.iso14496.part12.TrackReferenceTypeBox(type)
hind=org.mp4parser.boxes.iso14496.part12.TrackReferenceTypeBox(type)
vdep=org.mp4parser.boxes.iso14496.part12.TrackReferenceTypeBox(type)
vplx=org.mp4parser.boxes.iso14496.part12.TrackReferenceTypeBox(type)


rtp\ =org.mp4parser.boxes.iso14496.part12.HintSampleEntry(type)
srtp=org.mp4parser.boxes.iso14496.part12.HintSampleEntry(type)
stdp=org.mp4parser.boxes.iso14496.part12.DegradationPriorityBox

dvcC=org.mp4parser.boxes.dolby.DoViConfigurationBox

dfxp=org.mp4parser.boxes.sampleentry.DfxpSampleEntry