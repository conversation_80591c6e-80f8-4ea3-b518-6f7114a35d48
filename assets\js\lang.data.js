﻿'use strict';

var uiText={'lang':'0'};
switch($.urlGet().ln){
	case 'zh':
		uiText.lang='1';
		break;
};
uiText['change']=function(lang){
	switch(lang){
		case 'en':
			uiText.lang='0';
			break;
		case 'zh-cn':
			uiText.lang='1';
			break;
	}
};
uiText['text']=function(str){
	var tranStr=uiText.content[str]
	if(tranStr==undefined){
		tranStr=str;
	}else{
		tranStr=tranStr[uiText.lang];
		if(tranStr==undefined){
			tranStr=str;
		}
	}
	if(arguments.length>0){
		for(var i=1,len=arguments.length;i<len;i++){
			tranStr=tranStr.replace(' %'+i+' ',arguments[i])
		}
	}
	return tranStr;
};
uiText['content']={

	'Remember Me':{
		'0':'Remember password',
		'1':'记住密码'
	},
	'Forgot Password?':{
		'0':'Forgot password?',
		'1':'忘记密码？'
	},
	'Get Message':{
		'0':'Receive Code',
		'1':'发送验证码'
	},

	//mode
	'Has agreed':{
		'1':'已经同意'
	},
	'Refused your request':{
		'1':'拒绝了你的请求'
	},

	'No Search Results':{
		'1':'无搜索结果'
	},
	'Invite Friends':{
		'1':'邀请好友'
	},
	'I\'m..':{
		'0':'Add Message Here',
		'1':'添加验证信息'
	},
	'Add Friend':{
		'1':'添加好友'
	},
	'Send a message':{
		'0':'Message',
		'1':'发送消息'
	},
	'Confirm rejected':{
		'0':'Confirm Decline',
		'1':'确认拒绝'
	},
	'\'s friends add application?':{
		'0':'\'s friend request?',
		'1':'的好友申请吗?'
	},
	'Reject Reason':{
		'0':'Leave a Message',
		'1':'拒绝原因'
	},
	'Always refused the request by him':{
		'0':'Always decline requests from this person.',
		'1':'总是拒绝此人请求'
	},
	'Delete Friend':{
		'0':'Delete This Friend',
		'1':'删除好友'
	},
	'After you remove your contact list disappears from the other side, and the people will no longer accept the session message.':{
		'0':'After the deletion, he or she will disappear from your friend list, and all the chatting records will be eliminated.',
		'1':'删除该好友后对方将不再显示于好友列表,同时将删除与该联系人的聊天记录'
	},

	'Progress':{
		'0':'Sections',
		'1':'进度'
	},
	'Start CD':{
		'0':'Time left',
		'1':'开课倒计时'
	},
	'Has class':{
		'0':'Class in Session',
		'1':'已上课'
	},
	'Closed':{
		'1':'已关闭'
	},
	'Has ended':{
		'0':'Class Ended',
		'1':'已结束'
	},
	'Start Time':{
		'1':'开始时间'
	},
	'Please enter in<br /> %1  day(s)':{
		'1':' %1 天后<br />开放进入'
	},
	'Please enter in<br /> %1  hour(s)  %2  minute(s)':{
		'1':' %1 小时 %2 分钟后<br />开放进入'
	},
	'Please enter in<br /> %1  minute(s)':{
		'1':' %1 分钟后<br />开放进入'
	},


	'Classroom is full,Please contact the owner':{
		'1':'人数已满，请联系教室创建者'
	},

	//title
	'My Courses':{
		'0':'Classes',
		'1':'我的课程'
	},
	'My timetable':{
		'0':'Class Schedule',
		'1':'我的课程表'
	},
	'Group':{
		'1':'群组'
	},
	'Personal information':{
		'0':'Personal Information',
		'1':'个人资料'
	},
	'Chat':{
		'1':'聊天'
	},
	'Question':{
		'1':'问题'
	},
	'Member':{
		'1':'成员'
	},
	'Evaluate teacher':{
		'1':'对老师进行评价'
	},
	'Class Remind':{
		'0':'Course tips',
		'1':'课程提醒'
	},
	'Add contact request':{
		'1':'好友请求'
	},



	//text tips
	'Saved successfully':{
		'1':'保存成功'
	},
	'Failed to save':{
		'1':'保存失败'
	},
	'Only one chance to change account':{
		'0':'You have only one chance to change the account.',
		'1':'只有一次修改账号的机会'
	},
	'Successfully changed':{
		'1':'修改成功'
	},
	'Can not be empty':{
		'0':'Cannot be empty.',
		'1':'内容不能为空'
	},
	'Re-input not match':{
		'0':'Input not match!',
		'1':'重复输入内容不一致'
	},
	'twice input password is not identical':{
		'0':'New password cannot be the same as the old one.',
		'1':'新密码与原密码不能一样'
	},
	'This number is not registered':{
		'0':'This account hasn\'t been registered.',
		'1':'该账号尚未注册'
	},
	'Request sent successfully':{
		'1':'请求发送成功'
	},
	'Please wait for confirmation':{
		'1':'请等待对方确认'
	},
	'The word limit is':{
		'1':'最大字数限制为'
	},
	'The images limit is':{
		'1':'最大图片数量限制为'
	},
	'You input words ':{
		'1':'当前输入字数 '
	},
	'Message must be at least  %1  characters':{
		'1':'消息字数不能少于  %1  个'
	},
	'Message can not be empty':{
		'1':'消息不能为空'
	},
	'History has no more':{
		'0':'No additional messages.',
		'1':'已无更多历史记录'
	},
	'In the top of history':{
		'0':'Scroll up to view message history.',
		'1':'以上为历史消息'
	},
	'Add members':{
		'1':'添加成员'
	},
	'Searching':{
		'1':'搜索中'
	},
	'Search user or group':{
		'1':'搜索用户或群组'
	},
	'Here is the server search results':{
		'0':'Search Results',
		'1':'以下为网络搜索结果'
	},
	'Your evaluation is incomplete.  Please don\'t forget to rate your teacher':{
		'1':'你的评价还未完成，请给老师评星'
	},
	'Your evaluation is incomplete.  Please don\'t forget to rate your student':{
		'1':'你的评价还未完成，请给学生评星'
	},
	'Register Success':{
		'1':'注册成功'
	},
	'Reset Password Success':{
		'1':'新密码设置成功'
	},


	//input tips
	'please input your phone number':{
		'0':'Mobile number',
		'1':'请输入您的手机号'
	},
	'please input your password':{
		'0':'Password',
		'1':'请输入您的密码'
	},
	'Please input your message code':{
		'0':'Mobile Text Code',
		'1':'输入所收到短信的验证码'
	},
	'Message code':{
		'0':'Mobile Text Code',
		'1':'短信验证码'
	},
	'Set your password':{
		'0':'Password',
		'1':'设置您的密码'
	},
	'Set your nickname':{
		'0':'Nickname',
		'1':'设置您的昵称'
	},
	'Set new password':{
		'0':'Choose New Password',
		'1':'设置新密码'
	},
	're-enter your password':{
		'0':'New Password Again',
		'1':'重新输入密码'
	},
	'Password must be at least 6 characters':{
		'1':'密码长度不得少于6位'
	},
	'Click enter a comment':{
		'0':'Comment',
		'1':'点击输入备注'
	},
	'Please enter the word as Profile':{
		'0':'Enter Profile Information',
		'1':'请输入个性签名'
	},
	'Please enter this information':{
		'0':'Add a message to your friend request.',
		'1':'请输入验证信息'
	},

	'Loading':{
		'1':'加载中'
	},
	'Empty':{
		'1':'暂无内容'
	},



	//submit
	'Sign in':{
		'0':'Sign up',
		'1':'注册'
	},
	'Login':{
		'1':'登录'
	},
	'Edit':{
		'1':'编辑'
	},
	'Agree':{
		'1':'同意'
	},
	'Refuse':{
		'0':'Decline',
		'1':'拒绝'
	},
	'Next':{
		'1':'下一步'
	},
	'Back':{
		'1':'返回'
	},
	'Submit':{
		'1':'提交'
	},
	'Done':{
		'1':'完成'
	},
	'Save':{
		'1':'保存'
	},
	'OK':{
		'1':'确定'
	},
	'Cancel':{
		'1':'取消'
	},
	'Delete':{
		'1':'删除'
	},
	'Start':{
		'1':'开始'
	},
	'Send':{
		'1':'发送'
	},
	'Sending':{
		'1':'发送中'
	},
	'Resend':{
		'1':'再次发送'
	},
	'Add to friends':{
		'0':'Send Friend Request',
		'1':'添加好友'
	},
	'Enter':{
		'1':'进入'
	},
	'Screenshot':{
		'1':'屏幕截图'
	},
	'Screenshot without the first window':{
		'0':'Hide window when taking screenshot',
		'1':'截图时隐藏当前窗口'
	},
	'Kick Out':{
		'1':'移出教室'
	},
	'Permission':{
		'1':'授权'
	},
	'End permission':{
		'1':'取消授权'
	},
	'Reward':{
		'1':'奖励'
	},
	'Mute':{
		'1':'禁言'
	},
	'Speak':{
		'1':'取消禁言'
	},
	'Later':{
		'1':'忽略'
	},
	'Current Server':{
		'1':'当前服务器'
	},
	'Answer':{
		'1':'解答'
	},





	//list class
	'Phone Number':{
		'0':'Mobile Number',
		'1':'手机号'
	},
	'Password':{
		'1':'密码'
	},
	'Old Password':{
		'1':'原密码'
	},
	'New Password':{
		'1':'新密码'
	},
	'Postscript':{
		'1':'附加消息'
	},
	'Reasons':{
		'1':'理由'
	},
	'Remark':{
		'0':'Notes',
		'1':'备注'
	},
	'Account':{
		'1':'账号'
	},
	'Area':{
		'0':'Location',
		'1':'地区'
	},

	'Change avatar':{
		'1':'修改头像'
	},
	'Nickname':{
		'1':'昵称'
	},
	'Sex':{
		'0':'Gender',
		'1':'性别'
	},
	'Change Password':{
		'1':'修改密码'
	},
	'Change Phone Number':{
		'0':'Change Mobile Number',
		'1':'修改手机号码'
	},
	'Male':{
		'1':'男'
	},
	'Female':{
		'1':'女'
	},
	'Age':{
		'1':'年龄'
	},
	'Birthday':{
		'1':'生日'
	},
	'Total Sections':{
		'1':'总课时'
	},
	'Sections':{
		'1':'课时'
	},
	'Time Left':{
		'1':'倒计时'
	},
	'Class Time':{
		'1':'上课时间'
	},
	'Class in Session':{
		'1':'已经上课'
	},
	'Total Rewards':{
		'1':'课堂奖励'
	},
	'Times-num':{
		'0':'s',
		'1':'次'
	},
	'Evaluate students':{
		'1':'调取评价'
	},
	'Student Evaluation':{
		'1':'学生点评 '
	},
	'Teacher':{
		'1':'老师 '
	},
	'Student':{
		'1':'学生 '
	},
	'Same question':{
		'1':'同求解 '
	},


	//btn hoverTips
	'Sticker':{
		'1':'表情 '
	},
	'Temporary Class':{
		'1':'临时教室 '
	},
	'Enter after':{
		'1':'进入时间'
	},


	'year_-':{
		'0':'-',
		'1':'年'
	},
	'month_-':{
		'0':'-',
		'1':'月'
	},
	'day_ ':{
		'0':'',
		'1':'日'
	},


	'firstGuide_searchbar':{
		'0':'Enter your friend\'s phone number to add him/her',
		'1':'搜索好友手机号码添加TA吧'
	},


	'One star':{
		'1':'一颗星'
	},
	'Two star':{
		'1':'二颗星'
	},
	'Three star':{
		'1':'三颗星'
	},
	'Four star':{
		'1':'四颗星'
	},
	'Five star':{
		'1':'五颗星'
	},


//	'The remaining  %1  item(s)':{
//		'1':'还有 %1 项'
//	},
	'  %1  more':{
		'1':'还有 %1 项'
	},

	//xingzuo
	'Aries':{
		'1':'白羊座'
	},
	'Taurus':{
		'1':'金牛座'
	},
	'Gemini':{
		'1':'双子座'
	},
	'Cancer':{
		'1':'巨蟹座'
	},
	'Leo':{
		'1':'狮子座'
	},
	'Virgo':{
		'1':'处女座'
	},
	'Libra':{
		'1':'天秤座'
	},
	'Scorpio':{
		'1':'天蝎座'
	},
	'Sagittarius':{
		'1':'射手座'
	},
	'Capricorn':{
		'1':'摩羯座'
	},
	'Aquarius':{
		'1':'水瓶座'
	},
	'Pisces':{
		'1':'双鱼座'
	}



}

function setUiText(){
	function UiText($dom){
		var myText=$dom.html();
		switch ($dom[0].tagName.toLowerCase()){
			case 'input':
				if($dom.attr('data-tips')!=undefined){
					myText=$dom.attr('data-tips');
					myText=uiText.text(myText);
					if($dom.attr('data-tips')==$dom.val()){
						$dom.attr('data-tips',myText)
						$dom.val(myText);
					}else{
						$dom.attr('data-tips',myText)
					}
				}else{
					myText=$dom.val();
					myText=uiText.text(myText);
					$dom.val(myText);
				};
				$dom.removeClass('uiText')
				break;
			default:
				myText=$dom.html();
				myText=uiText.text(myText);
				$dom.html(myText);
				$dom.removeClass('uiText')
				break;
		}
	}
	$('.uiText').each(function(){
		UiText($(this));
	})
}
$(document).ready(function(){
	setUiText()
})
