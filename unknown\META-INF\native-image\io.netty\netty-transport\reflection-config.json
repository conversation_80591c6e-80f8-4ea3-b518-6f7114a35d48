[{"name": "io.netty.channel.socket.nio.NioServerSocketChannel", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.nio.ch.SelectorImpl", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "allowUnsafeAccess": true}, {"name": "publicSelectedKeys", "allowUnsafeAccess": true}]}, {"name": "java.lang.management.ManagementFactory", "methods": [{"name": "getRuntimeMXBean", "parameterTypes": []}]}, {"name": "java.lang.management.RuntimeMXBean", "methods": [{"name": "getName", "parameterTypes": []}]}]