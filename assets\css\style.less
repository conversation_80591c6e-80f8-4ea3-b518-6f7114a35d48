@charset "utf-8";

@btless: "less/";

@import "@{btless}variables.less"; //变量
@import "@{btless}global.less";  //全局
@import "@{btless}position.less";  //定位
@import "@{btless}animation.less";  //定位


@import "@{btless}text.less";  //文字颜色
@import "@{btless}icon.less";  //图标
@import "@{btless}form.less";  //表单

.display-none{
	display: none;
}

.content{
	word-break: break-all;
	hyphens:auto;
	-webkit-hyphens: auto;
	-ms-hyphens: auto;
	-moz-hyphens: auto;
}

.radius50(){
	border-radius: 50%;
	overflow: hidden;
}

.pageContainer{
	.positionAll();
	padding: 10px;
	//background-color: rgba(255,255,255,.8);
}

.uiText{
	color: transparent !important;
}

.fz14{
	font-size: 14px;
}

.text-primary{
	color: @primary-color;
}
.text-gray{
	color: gray;
}
.text-center{
	text-align: center;
}


.textOverflow(){
	display: block;
	overflow: hidden;

	white-space: nowrap;
	text-overflow: ellipsis;
}


.btn{
	&[disabled="disabled"],
	&[disabled="disabled"]:hover{
		color: #fff;
		background-color: #ccc;
	}
}
.btn-lg{
	font-size: 16px;
}
//btn-defalut
.btn-default{
	background-color: @white-color;
	color: @dark-color;
	&:hover{
		background-color: @white-color-hover;
		color: @dark-color-hover;
	}
}
//btn-primary
.btn-primary{
	background-color: @primary-color;
	border:0;
	color: @white-color;
}
.btn-primary.hover,
.btn-primary:hover{
	background-color: @primary-color-hover;
	color: @white-color;
}
//btn-link
.btn-link{
	background-color: transparent;
	border: 0;
	&:hover{
		background-color: transparent;
	}
	&.btn-primary{
		color: @primary-color;
		&:hover{
			color: @primary-color-hover;
		}
	}
	&.btn-danger{
		color: @danger-color;
		&:hover{
			color: @danger-color-hover;
		}
	}
}

.btn-primary-hover:hover{
	.btn-primary.hover;
}
.btn{
	&.btn-primary{
		&.disabled{
			background-color: #ccc;
			cursor: not-allowed;
		}
	}
}

.table{
	display: table;
	.tr{
		display: table-row;
		.th,.td{
			display: table-cell;
		}
	}
}


img.loading{
	width: 0;
	height: 0;
	border: 0;
	padding: 0;
	margin: 0;
	//display: none;
	&:after{
		content: 'loading';
		display: inline-block;
		width: 100px;
		height: 100px;
		border: 1px solid #999;
	}
	&.loadFailed{
		&:after{
			content: 'loadFailed';
		}
	}
}










.mainTopBar{
	height: 45px;
	border-bottom: 1px solid #ccc;
	font-size: 14px;
	text-align: center;
	line-height: 44px;
}

.bg_logo{
	background-image: url(../images/bg_logo.png);
	background-repeat: no-repeat;
	background-position: center center;
}

.boxShadow(){
	box-shadow: 5px 5px 15px rgba(0,0,0,.5);
}

//主体窗口
@winRadius:3px;
.rootWindow{
	position: absolute;
	//overflow: hidden;
	left: 50%;
	top: 50%;
	width: 900px;
	height: 620px;
	margin-left: -450px;
	margin-top: -310px;
	background-color: #fff;
	border-radius: @winRadius;
	box-shadow: 0px 5px 20px rgba(0,0,0,.4);
	.rootRadius3{
		border-top-right-radius: 3px;
	}
	.winMenu{
		position: absolute;
		width: 54px;
		height: 100%;
		background-color: #474c64;
		background-image: -webkit-linear-gradient( 90deg, rgb(65,70,94) 0%, rgb(79,85,107) 100%);
  		border-radius: @winRadius 0px 0px @winRadius;
		.menuBtnBox{
			position: relative;
			margin-top: 30px;
			.btnMark{
				position: absolute;
				width: 100%;
				height: 50px;
				left: 0;
				top: 0px;
				background-image: url(../images/bg_rootmenu_btnmark.png);
				background-repeat: no-repeat;
				background-position: left center;
			}
			.menuBtnList{
				position: absolute;
				width: 50px;
				height: 50px;
				left: 3px;
				background-image: url(../images/icon_rootmenu.png);
				opacity: .6;
				filter:alpha(opacity=60);
				-webkit-transition: .3s;
				cursor: pointer;
				.menuBtnBg(@n){
					top:@n*70px ;
					background-position: 0px @n*(-50px);
				}
				&.checked,
				&:hover{
					opacity: 1;
					filter:alpha(opacity=100);
				}
				&.chat{
					.menuBtnBg(0);
				}
				&.contacts{
					.menuBtnBg(1);
				}
				&.course{
					.menuBtnBg(2);
				}
				&.cloud{
					.menuBtnBg(3);
				}
			}
		}
	}
	.winMain{
		position: absolute;
		height: 100%;
		left: 54px;
		right: 0;
		background-color: #f7f7f7;
		border-radius:0 @winRadius @winRadius 0;
		.radius-right();
		.winMainLeft{
			-webkit-user-select: none;
			position: absolute;
			height: 100%;
			left: 0;
			top: 0;
			background-color: #f7f7f7;
			//border-right: 1px solid #ccc;
			.winMainLeftTop{
				position: absolute;
				width: 100%;
				height: 45px;
				left: 0;
				top: 0;
				border-bottom: 1px solid #ccc;
			}
			.winMainLeftMain{
				position: absolute;
				width: 100%;
				left: 0;
				top: 0;
				bottom: 0px;
				border-right: 1px solid #ccc;
			}
			.winMainLeftTop + .winMainLeftMain{
				top: 45px;
			}
		}
		.winMainContainer{
			position: absolute;
			height: 100%;
			left: 221px;
			right: 0;
			top: 0;
			background-color: #fefefe;
			border-radius: 0px @winRadius @winRadius 0px;
			.chatWindow{
				border-radius: 0px @winRadius @winRadius 0px;
			}
		}
	}
}
.clearafter{
	.clearafter();
}

//搜索栏
.contactsSearch{
	padding-top: 10px;
	.clearafter();
	.searchInput{
		display: block;
		float: left;
		//width: 160px;
		width: 100%;
		padding:4px;
		padding-left: 20px;
		background-color: transparent;
		background-image: url(../images/icon_search.png);
		background-position: left center;
		background-repeat: no-repeat;
		border: 0;
		border-bottom: 1px solid #ddd;
	}
	.inputClear{
		position: absolute;
		width: 20px;
		height: 20px;
		background-image: url(../images/icon_inputclear.png);
		background-repeat: no-repeat;
		background-position: center center;
		border-radius: 50%;
		cursor: pointer;
	}
	.addBtn{
		display: block;
		float: right;
		width: 26px;
		height: 26px;
		background-color: transparent;
		background-image: url(../images/icon_add.png);
		background-position: center center;
		background-repeat: no-repeat;
		border: 0;
		cursor: pointer;
	}
	.resultBox{
		position: absolute;
		overflow-y: auto;
		z-index: 1;
		max-height: 570px;
		left: 1px;
		right: 1px;
		top: 45px;
		background-color: #fff;
		box-shadow: 0px 2px 6px 2px rgba(0,0,0,.3);
		.resultList{
			position: relative;
			min-height: 58px;
			//border-bottom: 1px solid #ddd;
			cursor: pointer;
			.clearafter();
			.resultIcon{
				position: absolute;
				left: 8px;
				top: 8px;
				img{
					width: 40px;
					height: 40px;
					border-radius: 50%;
				}
			}
			.resultInfo{
				margin-left: 56px;
				padding-top: 8px;
				padding-right: 8px;
				.title{
					float: left;
					line-height: 20px;
				}
				.id{
					float: right;
					color: #aaa;
					line-height: 20px;
				}
				.tips{
					clear: both;
					color: #999;
				}
			}

			&:hover{
				background-color: #fcfcfc;
			}
			&.checked{
				background-color: #f6f6f6;
			}
		}
	}
}

//联系人列表
.contactsList{
	position: relative;
	min-height: 58px;
	//background-image: -webkit-linear-gradient( 270deg, #efefef 0%, #eeeeee 100%);
	border-bottom: 1px solid #ddd;
	cursor: pointer;
	.clearafter();
//	  -webkit-animation:showByFade 0.5s 0.2s ease both;
	.contactsIcon{
		position: absolute;
		left: 8px;
		top: 8px;
		img{
			width: 40px;
			height: 40px;
			border-radius: 50%;
		}
		.redNum{
			margin-top: 3px;
			margin-right: 3px;
		}
	}
	.contactsInfo{
		margin-left: 56px;
		padding-top: 8px;
		padding-right: 8px;
		.title{
			line-height: 20px;
			max-width: 90px;
			height: 20px;
			overflow:hidden;
			white-space:nowrap;
			text-overflow: ellipsis;
		}
		.dateMark{
			position: absolute;
			top: 8px;
			right: 8px;
			color: #aaa;
			line-height: 20px;
		}
		.tips{
			clear: both;
			color: #999;
			height: 18px;
			overflow:hidden;
			white-space:nowrap;
			text-overflow: ellipsis;
		}
	}

	&:hover{
		background-color: #eee;
		//background-image: -webkit-linear-gradient( 270deg, #e6e6e6 0%, #e6e6e6 100%);
	}
	&.checked{
		background-color: #ddd;
		//background-image: -webkit-linear-gradient( 270deg, #ddd 0%, #ddd 100%);
	}
}

.tabBox{
	background-color: #404148;
	border-radius: 2px;
	//box-shadow: 3px 3px 20px rgba(0,0,0,.5);
	border: 1px solid #45464d;
	.tabBar{
		position: absolute;
		display: table;
		width: 100%;
		height: 40px;
		left: 0;
		top: 0;
		.tab{
			display: table-cell;
			padding: 10px;
			background-color: #34363b;
			font-size: 14px;
			line-height: 14px;
			color: #74767d;
			text-align: center;
			cursor: pointer;
			&.checked{
				background-color: transparent;
				color: #7cc276;
			}
		}
	}
	.tabCon{
		position: absolute;
		left: 0;
		top: 40px;
		right: 0;
		bottom: 0;
		.con{
			display: none;
			&.checked{
				display: block;
			}
		}
	}
}



//聊天窗口
.chatWindow{
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	//writing-mode: lr-tb;
	//窗口按钮
	.winBtns{
		position: absolute;
		height: 20px;
		top: 7px;
		right: 7px;
		.winBtn{
			display: inline-block;
			width: 20px;
			height: 20px;
			margin-right: 5px;
			background-image: url(../images/icon_winbtns.png);
			background-position: 0 0;
			cursor: pointer;
			&:hover{
				background-position-x: -20px;
			}
			&.close{
				background-position-y: 0;
			}
			&.maximize{
				background-position-y: -20px;
			}
			&.minimize{
				background-position-y: -40px;
			}
		}
	}

	//聊天内容
	.chatContainer{
		position: absolute;
		left: 0;
		top: 45px;
		right: 0;
		bottom: 90px;
		padding: 10px;
		overflow: auto;
		.chatDate{
			text-align: center;
			color: #bbb;
			//border-top: 1px solid #ccc;
			margin: 10px 20px;
		}
		.chatMsgTips{
			display: inline-block;
			padding: 4px;
			margin-left: 78px;
			margin-top: 10px;
			background-color: #ddd;
			border-radius: 3px;
		}
		//聊天对话框
		@chat-userhead-size:40px;
		@chat-img-width-max:200px;
		@chat-img-height-max:200px;
		@chat-img-height-min:100px;
		.chatBubble{
			position: relative;
			min-height: @chat-userhead-size;
			.clearafter();
			.userInfo{
				position: absolute;
				.chatShow_headimg;
				.headImg{
					width: @chat-userhead-size + 2;
					height: @chat-userhead-size + 2;
					border:1px solid #999;
					.radius50();
					img{
						display: block;
						width: @chat-userhead-size;
						height: @chat-userhead-size;
						.radius50();
					}
				}
				.nickname{
					white-space: nowrap;
				}
			}
			.chatInfo{
				.clearafter();
				.infoBox{
					position: relative;
					.conBox{
						position: relative;
					}
					.textBox{
						-webkit-user-select:auto;
						padding:0 5px;
						word-break: break-all;
						line-height: 1.8;
						.clearafter();
						a{
							text-decoration: underline;
						}
						img{
							display: inline-block;
							max-width: 100%;
							max-height: 200px;
							&.emoji{
								display: inline-block;
								max-width: 20px;
								max-height: 20px;
							}
						}
					}
					.imgBox{
						img{
							display: block;
							max-width: @chat-img-width-max;
							max-height: @chat-img-height-max;
							border-radius: 11px;
						}
					}
					.nickname{
						display: none;
					}
					.sendMode{
						position: absolute;
						width: 16px;
						height: 16px;
						background-position: center center;
						background-repeat: no-repeat;
						bottom: -16px;
					}
					.sendMode.error{
						background-image: url(../images/icon_mark_excited.png);
						background-color: #f94d30;
						border-radius: 50%;
						cursor: pointer;
					}
					.sendMode.sending{
						background-image: url(../images/icon_chatsending.png);
						-webkit-animation:hoverByRotate 0.5s 0s linear infinite;
					}


					.classroom{
						position: relative;
						width: 320px;
						min-height: 102px;
						background-color: #fefefe;
						border-radius: 3px;
						border: 1px solid #ddd;
						box-shadow: 0px 2px 2px rgba(0,0,0,.1);
						.ownerIcon{
							position: absolute;
							left: 10px;
							top: 15px;
							border: 1px solid #ddd;
							img{
								width: 68px;
								height: 68px;
							}
						}
						.classInfo{
							margin-left: 90px;
							padding-top: 15px;
							padding-right: 15px;
							min-height: 90px;
							.classTitle{
								font-size: 14px;
								line-height: 20px;
							}
							.classTips{
								.date{
									color: #aaa;
								}
							}
						}
						.btnBox{
							.btn{
								display: block;
								padding: 8px;
								margin-top: 6px;
								border-top-left-radius: 0;
								border-top-right-radius: 0;
								font-size: 14px;
							}
						}
						&:hover{
							background-color: #fcfcfc;
						}
						&.checked{
					//	  background-image: -webkit-linear-gradient( 270deg, #ddd 0%, #ddd 100%);
						}
					}




				}

			}
			&.other{
				.userInfo{
					left: 10px;
				}
				.chatInfo{
					margin-left: @chat-userhead-size + 10;
					margin-right: @chat-userhead-size + 40;
					.infoBox{
						float: left;
						border-top: 16px solid transparent;
						border-right: 16px solid transparent;
						border-bottom: 20px solid transparent;
						border-left: 28px solid transparent;
						border-image:url(../images/dialog_info_bg_other.png) fill 16 16 20 28 stretch;
						.chatShow_other_info;
						.textBox{
							margin: -5px -5px -9px -7px;
						}
						.imgBox{
							margin: -10px -10px -14px -12px;
						}
						.sendMode{
							right: -40px;
						}
					}

				}
				&.special{
					.chatInfo{
						.infoBox{
							border-image:url(../images/dialog_info_bg_other_special.png) fill 16 16 20 28 stretch;
						}
					}
				}
			}
			&.self{
				.userInfo{
					right: 10px;
				}
				.chatInfo{
					margin-left: @chat-userhead-size + 40;
					margin-right: @chat-userhead-size + 10;
					.infoBox{
						float: right;
						border-top: 16px solid transparent;
						border-right: 28px solid transparent;
						border-bottom: 20px solid transparent;
						border-left: 16px solid transparent;
						border-image:url(../images/dialog_info_bg__self.png) fill 16 28 20 16 stretch;
						.chatShow_self_info;
						.textBox{
							margin: -5px -7px -9px -5px;
						}
						.imgBox{
							margin: -10px -12px -14px -10px;
						}
						.sendMode{
							left: -40px;
						}
					}
				}
			}
			&.type-html{
				.chatInfo{
					.infoBox{
						border-image: none;
					}
				}
				&.self{
					.chatInfo{
						.infoBox{
							padding-right: 20px;
						}
					}
				}
				&.other{
					.chatInfo{
						.infoBox{
							padding-left: 20px;
						}
					}
				}
			}
		}
	}

	&.full{
		.chatContainer{
			bottom: 160px;
			.chatBubble{
				margin-bottom: 60px;
				.userInfo{
					bottom: 10px;
					.nickname{
						position: absolute;
						bottom: -30px;
					}
				}
				&.other{
					.userInfo{
						.nickname{
							left: 0;
						}
					}
				}
				&.self{
					.userInfo{
						.nickname{
							right: 0;
						}
					}
				}
			}
		}
		.chatEditor{
			height: 160px;
			.textAreaBox{
				height: 120px;
				.chatWrite{
					height: 100%;
				}
			}
		}
	}

	&.inClass{
		.chatTitle{
			display: none;
		}
		.chatContainer{
			top: 0px;
			bottom: 160px;

			//聊天对话框
			@chat-userhead-size:0px;
			@chat-img-width-max:200px;
			@chat-img-height-max:200px;
			@chat-img-height-min:100px;
			.chatBubble{
				position: relative;
				min-height: @chat-userhead-size;
				padding-top: 30px;
				.userInfo{
					position: absolute;
					z-index: 1;
					width: auto;
					height: auto;
					top: 5px;
					bottom: auto;
					.headImg{
						display: none;
					}
					.nickname{
						display: block;
						color: #eee;
					}
				}
				.chatInfo{
					.infoBox{
						padding: 10px;
						border-image: none;
						border-width: 0;
						border-radius: 8px;
						.nickname{
//						  display: block;
//						  font-weight: bold;
//						  padding-bottom: 5px;
						}
						.textBox{
							-webkit-user-select:auto;
							padding:0px;
							word-break: break-all;
							line-height: 1.8;
							a{
								text-decoration: underline;
							}
						}
						.imgBox{
							img{
								display: block;
								//min-height: @chat-img-height-min;
								border-radius: 6px;
							}
						}
						.sendMode{
							position: absolute;
							width: 16px;
							height: 16px;
							background-position: center center;
							background-repeat: no-repeat;
							bottom: -16px;
						}
						.sendMode.error{
							background-image: url(../images/icon_mark_excited.png);
							background-color: #f94d30;
							border-radius: 50%;
							cursor: pointer;
						}
						.sendMode.sending{
							background-image: url(../images/icon_chatsending.png);
							-webkit-animation:hoverByRotate 0.5s 0s linear infinite;
						}
					}

				}
				&.msgMerge{
					padding-top: 0;
					margin-top: 5px;
					.userInfo{
						.nickname{
							display: none;
						}
					}

				}
				&.other{
					color: #eee;
					.userInfo{
						left: 10px;
					}
					.chatInfo{
						margin-left: @chat-userhead-size + 10;
						margin-right: @chat-userhead-size + 40;
						.nickname{
							color: #777;
						}
						.infoBox{
							padding-left: 10px;
							background-color: #4a4b53;
							.chatShow_other_info;
							.textBox{
								margin: 0;
							}
							.imgBox{
								margin: -6px;
							}
							.sendMode{
								right: -40px;
							}
						}

					}
					&.special{
						.chatInfo{
							.infoBox{
								border-image:url(../images/dialog_info_bg_other_special.png) fill 16 16 20 28 stretch;
							}
						}
					}
				}
				&.self{
					.userInfo{
						right: 10px;
					}
					.chatInfo{
						margin-left: @chat-userhead-size + 40;
						margin-right: @chat-userhead-size + 10;
						.infoBox{
							background-color: #76b571;
							.chatShow_self_info;
							.textBox{
								margin: 0;
							}
							.imgBox{
								margin: -6px;
							}
							.sendMode{
								left: -40px;
							}
						}
					}
				}
			}
		}
		.chatEditor{
			height: 160px;
			.textAreaBox{
				height: 120px;
				.chatWrite{
					height: 100%;
				}
			}
		}
	}
	.chatNoticeBar{
		position: absolute;
		width: 100%;
		left: 0;
		top: 44px;
		.noticeAlert{
			line-height: 36px;
			.clearafter();
			.noticeText{
				float: left;
				padding-left: 10px;
			}
			.noticeBtn{
				float: right;
				padding: 0 20px;
				cursor: pointer;
			}
			&.green{
				background-color: #96ce91;
				.noticeBtn{
					background-color: #6cb367;
					color: #fff;
				}
			}
		}
	}
}

//窗口标题
.chatTopBar{
	//
	background-color: #f7f7f7;
	border-bottom: 1px solid #ccc;
	color: #777;
	line-height: 24px;
	font-weight: bold;
	text-align: center;
	input[type="text"].editTag{
		text-align: center;
		line-height: 24px;
		border-radius: 100px;
	}
	.title{
		padding: 10px;
	}
	.titleText{
		cursor: pointer;
	}
	.titleBtn{
		display: inline-block;
		display: none;
//	  position: absolute;
//	  display: block;
//	  width: 20px;
//	  height: 20px;
//	  left: 14px;
//	  top: 14px;
//	  line-height: 16px;
//	  text-align: center;
		cursor: pointer;
		-webkit-transition: .2s;
		&.checked{
			-webkit-transform: rotate(180deg);

		}
	}
}


.chatMemberBox{
	position: absolute;
	display: none;
	left: 0px;
	right: 0px;
	top: 44px;
	bottom: 160px;
	z-index: 1;
	.clearafter();
	.toolsBox{
		position: absolute;
		right: 10px;
		top: 10px;
		width: 60px;
		padding-top: 10px;
	}
	.memberListBox{
		display: none;
		position: absolute;
		max-height: 100%;
		padding: 0px;
		padding-right: 60px;
		left: 0px;
		top: 0px;
		right: 0px;
		background-color: #f7f7f7;
		box-shadow: 0px 5px 10px rgba(0,0,0,.2);
		overflow-y: auto;
		.clearafter();
		.memberList{
			float: left;
			position: relative;
			width:70px;
			height: 90px;
			padding: 5px;
			cursor: pointer;
			.headImg,
			.headImg img{
				width: 40px;
				height: 40px;
				border-radius: 50%;
				margin: 0 auto;
			}
			.userName{
				width: 100%;
				height: 20px;
				line-height: 20px;
				text-align: center;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			.deleThis{
				position: absolute;
				width: 16px;
				height: 16px;
				right: 12px;
				top: 2px;
				background-color: #fd451a;
				border-radius: 50%;
				font-size: 16px;
				line-height: 14px;
				text-align: center;
				cursor: pointer;
				&:before{
					content: '-';
					color: #fff;
				}
			}
		}
		.memberAdd{
			float: left;
			width:70px;
			padding-top: 5px;
			cursor: pointer;
			.headImg{
				width: 40px;
				height: 40px;
				margin: 0 auto;
				background-color: #eee;
				border:2px dashed #ccc;
				border-radius: 50%;
				font-size: 30px;
				line-height: 36px;
				color: #aaa;
				text-align: center;
			}
			.userName{
				line-height: 20px;
				text-align: center;
			}
		}
	}
}


//编辑窗口
.chatEditor{
	position: absolute;
	width: 100%;
	left: 0;
	bottom: 0;
	padding-bottom: 15px;
	box-shadow: 0px -1px 0px #ccc;
	//border-top: 1px solid #ccc;
	.chatNotice{
		display: none;
		padding-left: 5px;
		background-color: #ddd;
		line-height: 24px;
	}
	//工具栏
	.toolsBox{
		height: 28px;
		padding-left: 15px;
		padding-top: 4px;
		//border-top: 1px solid rgba(0,0,0,.2);
		.toolsBtn{
			float: left;
			width: 20px;
			height: 20px;
			margin-right: 15px;
			background-image: url(../images/icon_dialog_tools_black.png);
			background-repeat: no-repeat;
			cursor: pointer;
		}
		.toolsBtn:hover{
			background-position-x:-40px;
		}
		.toolsBtn.disabled{
			opacity: .3;
		}
		.toolsBtnSN(@n){
			background-position-y:-@n*20px;
		}
		.toolsBtn.arrow{
			.toolsBtnSN(0);
		}
		.toolsBtn.moodFace{
			.toolsBtnSN(1);
		}
		.toolsBtn.screenshot{
			width: 30px;
			.toolsBtnSN(2);
		}
		.toolsBtn.classroom{
			.toolsBtnSN(3);
		}
	}
	.textAreaBox{
		position: relative;
//	  padding-bottom: 22px;
		margin:0 10px;
		background-color: #fff;

		.chatWrite{
			display: block;
			width: 100%;
			white-space: pre-wrap;
			-webkit-user-select: auto;
			user-select: auto;
			//height: 24px;
			padding: 2px;
			border: 0;
			font-size: 12px;
			line-height: 18px;
			overflow: auto;
			-webkit-user-select: auto;
			img{
				display: inline-block;
				max-width: 200px;
				max-height: 88px;
				vertical-align: middle;
				zoom: 1;
			}
			img.emoji{
				height: 18px;
			}
			img.screenshot{
			}
			*{
				-webkit-user-select: auto;
				user-select: auto;
			}
		   // -webkit-transition: background-color .3s;
		}
		.sendBtn{
			position: absolute;
			display: none;
			width: 48px;
			height: 20px;
			right: 2px;
			bottom: 2px;
			background-color: #fff;
			border: 0;
			outline: 2px solid #62c479;
		}
		&:hover{
			.sendBtn{
				display: block;
			}
		}
	}
}
.black{
	.chatEditor{
		box-shadow: 0px -1px 0px #33343a;
		//border-top-color: #33343a;
		.textAreaBox{
			.chatWrite{
				background-color: #61636b;
				color: #eee;
				//box-shadow: 0px 22px 0px 0px #61636b;
				&.focus,
				&:focus{
					background-color: #eee;
					color: #61636b;
					//box-shadow: 0px 22px 0px 0px #eee;
				}
			}
		}
	}
}
.searchBar{
	padding: 15px 45px;
	.searchInput{
		display: block;
		width: 100%;
		padding: 6px 6px 6px 27px;
		background-color: #4a4b53;
		background-image: url(../images/icon_search_black.png);
		background-repeat: no-repeat;
		background-position: 7px center;
		border: 0;
		border-radius: 30px;

		font-size: 12px;
		line-height: 12px;
		-webkit-transition: .3s;
		&:focus{
			background-color: #eee;
		}
	}
}
//问答窗口
.qnaWindow{
	.qnaTitle{
		padding: 10px;
		background-color: #f7f7f7;
		border-bottom: 1px solid #ccc;
		color: #777;
		line-height: 24px;
		font-weight: bold;
		text-align: center;
		input[type="text"].editTag{
			text-align: center;
			line-height: 24px;
			border-radius: 100px;
		}
		.titleBtn{
			position: absolute;
			display: block;
			width: 20px;
			height: 20px;
			left: 14px;
			top: 14px;
			line-height: 16px;
			text-align: center;
			cursor: pointer;
			-webkit-transition: .2s;
			&.checked{
				-webkit-transform: rotate(180deg);

			}
		}
	}
	//聊天内容
	.qnaContainer{
		position: absolute;
		left: 0;
		top: 60px;
		right: 0;
		bottom: 160px;
		padding:0 10px;
		overflow: auto;
		//聊天对话框
		@chat-userhead-size:40px;
		@chat-img-width-max:100px;
		@chat-img-height-max:100px;
		.bubble{
			position: relative;
			margin-bottom: 18px;
			background-color: #4a4b53;
			border-radius: 8px;
			.clearafter();
			.bubbleContent{
				color: #eee;
				padding: 20px 10px;
				border-bottom: 1px solid #404148;

				word-break: break-all;
				.textBox{
					-webkit-user-select:auto;
					img{
						max-width: 100%;
						max-height: 300px;
					}
				}
			}
			.bubbleInfo{
				height: 30px;
				color: #787a83;
				.left{
					float: left;
				}
				.right{
					float: right;
				}
				.asker,
				.likeNum{
					float: left;
					padding: 0 12px;
					line-height: 30px;
				}
				.likeBtn{
					float: left;
					width: 70px;
					height: 30px;
					background-color: @primary-color;
					background-image: url(../images/icon_like.png);
					background-repeat: no-repeat;
					background-position: center center;
					border-bottom-right-radius: 8px;

					color: #393939;
					text-align: center;
					line-height: 30px;
					cursor: pointer;
					&:hover{
						background-color: @primary-color-hover;
					}
					&.answer{
						background-image: none;
					}
				}
			}
			&.liked{
				.bubbleInfo{
					.likeBtn{
						background-color: #585962;
						cursor: not-allowed;
					}
				}
			}

		}
	}
	.chatEditor{
		height: 160px;
		.textAreaBox{
			height: 120px;
			.chatWrite{
				height: 100%;
			}
		}
	}
}

.classroomMemberWindow{
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	overflow: auto;
	.memberListTitle{
		height: 40px;
		padding: 0px 12px;

		color: #777;
		line-height: 40px;
	}
	.memberListBox{
		.memberList{
			position: relative;
			height: 41px;
			padding: 0 12px;
			border-bottom: 1px solid #393a41;
			cursor: default;
//		  .iconAuthorize{
//			  float: left;
//			  width: 20px;
//			  height: 40px;
//			  background-image: url(../images/icon_authorize.png);
//			  background-repeat: no-repeat;
//			  background-position: right center;
//		  }
			.memberName{
				.textOverflow();
				padding-left: 3px;
				color: #eee;
				line-height: 40px;

			}
			.memberCtrlBtn(){
				position: absolute;
				width: 24px;
				height: 24px;
				top: 50%;
				margin-left: 30px;
				margin-top: -12px;
				background-position: center center;
				background-repeat: no-repeat;
				cursor: pointer;
			}
			.prize{
				.memberCtrlBtn();
				width: auto;
				right: 20px;
				padding-left: 20px;
				background-image: url(../images/icon_prize.png);
				background-position: left center;

				color: #eee;
				line-height: 20px;
			}
			.getOut{
				.memberCtrlBtn();
				right: 80px;
				background-image: url(../images/icon_getout.png);
			}
			.showUp{
				.memberCtrlBtn();
				right: 110px;
				background-image: url(../images/icon_showup.png);
				&.disabled{
					background-image: url(../images/icon_showup_disabled.png);
				}
			}
			.microphone{
				.memberCtrlBtn();
				right: 140px;
				background-image: url(../images/icon_microphone.png);
				&.disabled{
					background-image: url(../images/icon_microphone_disabled.png);
				}
			}
			.iconAuthorize{
				.memberCtrlBtn();
				right: 170px;
				background-image: url(../images/icon_authorize.png);
				&.disabled{
					background-image: url(../images/icon_authorize_disabled.png);
				}
			}
			.raiseHand{
				.memberCtrlBtn();
				right: 200px;
				background-image: url(../images/icon_raisehand.png);
				cursor: default;
				&.disabled{
					background-image: url(../images/icon_raisehand_disabled.png);
				}
			}
			&:hover{
				background-color: #34363b;
			}
			.btnMode{
				background-color: rgba(255,255,255,.1);
				border:1px solid rgba(255,255,255,.3);
				border-radius: 4px;
				box-shadow: 1px 1px 3px 0px rgba(255,255,255,.05);
			}
		}
		&.assistant{
			.memberList{
				.memberName{
					&:after{
						content:'（助教）'
					}
				}
			}
		}
		&.member{
			.memberList{
				.memberName{
					//max-width: 125px;

					margin-right: 220px;

				}
			}
		}
	}
}

.chatShow_headimg{
	-webkit-transform-origin: center center;
	-webkit-animation:showByScale 0.5s 0s ease both;
}
.chatShow_self_info{
	-webkit-transform-origin: bottom right;
	-webkit-animation:showByScale 0.3s 0.2s ease both;
}
.chatShow_other_info{
	-webkit-transform-origin: bottom left;
	-webkit-animation:showByScale 0.3s 0.2s ease both;
}

























.noticeListBox{
	position: absolute;
	width: 100%;
	height: 100%;
	padding-bottom: 10px;
	overflow-x: hidden;
	overflow-y: auto;
	.noticeList{
		position: relative;
		min-height: 58px;
		//background-image: -webkit-linear-gradient( 270deg, #efefef 0%, #eeeeee 100%);
		border-bottom: 1px solid #ddd;
		cursor: pointer;
		.clearafter();

//	  -webkit-animation:showByFade 0.5s 0.2s ease both;
		.noticeIcon{
			position: absolute;
			left: 8px;
			top: 8px;
			img{
				width: 40px;
				height: 40px;
				border-radius: 50%;
			}
			.redBallNum{
				margin-top: 3px;
				margin-right: 3px;
			}
		}
		.noticeInfo{
			margin-left: 56px;
			padding-top: 8px;
			padding-right: 8px;
			.noticeTitle{
				float: left;
				line-height: 20px;
			}
			.noticeDate{
				float: right;
				color: #aaa;
				line-height: 20px;
			}
			.noticeTips{
				clear: both;
				color: #999;
			}
		}

		&:hover{
			background-color: #eee;
			//background-image: -webkit-linear-gradient( 270deg, #e6e6e6 0%, #e6e6e6 100%);
		}
		&.checked{
			background-color: #ddd;
			//background-image: -webkit-linear-gradient( 270deg, #ddd 0%, #ddd 100%);
		}
	}
	.searchResult{
		position: absolute;
		//width: 100%;
		max-height: 100%;
		left: 10px;
		right: 10px;
		top: 0;
		overflow-y: auto;
		background-color: #fff;
		box-shadow: 0px 5px 10px rgba(0,0,0,.5);
		.searchResultListBox{
			.noticeSearchList{
				.noticeList;
			}
		}
	}
	.clearbefore();
}
	.courseList{
		position: relative;
		overflow: hidden;
		width: 320px;
		min-height: 102px;
		//margin: 10px 10px auto 10px;
		margin-left: 10px;
		margin-top: 10px;
		background-color: #fefefe;
		border-radius: 3px;
		border: 1px solid #ddd;
		box-shadow: 0px 2px 2px rgba(0,0,0,.1);
		.courseInfo{
			position: relative;
			padding: 15px 15px 15px 90px;
//		  margin-left: 90px;
//		  padding-top: 15px;
//		  padding-right: 15px;
//		  padding-bottom: 10px;
			min-height: 90px;
			.courseIcon{
				position: absolute;
				left: 10px;
				top: 50%;
				margin-top: -35px;
				border: 1px solid #ddd;
				img{
					width: 68px;
					height: 68px;
				}
				.redBallNum{
					margin-top: 3px;
					margin-right: 3px;
				}
			}

			.courseTitle{
				margin-top: 12px;
				font-size: 14px;
				line-height: 20px;
				.textOverflow();
			}
			.courseProgress{
				display: table;
				width: 100%;
				.name{
					display: table-cell;
					width: 60px;
					line-height: 32px;
				}
				.bar{
					position: relative;
					height: 3px;
					top: -3px;
					background-color: #cbcbcb;
					.completed{
						width: 50%;
						height: 3px;
						background-color: @primary-color;
					}
				}
				.num{
					display: table-cell;
					width: 50px;
					text-align: right;
				}
			}

		}
		.lesson{
			position: relative;
			padding: 10px;
			background-color: #f9f9f9;
			border-top: 1px solid #dcdcdc;
			.lessonInfo{
				margin-right: 150px;
				.lessonTitle{
					.textOverflow();
				}
				.timeRange{
					color: #999;
				}
			}
			.btnBox{
				position: absolute;
				top: 50%;
				right: 12px;
				margin-top: -16px;
				.btn{
					display: block;
					width: 140px;
					height: 32px;
					padding: 8px;
//				  border-radius: 0;
	//			  border-top-left-radius: 0;
	//			  border-top-right-radius: 0;
					font-size: 14px;
					line-height: 16px;
					&.btn-link{
						position: relative;
						padding: 0;
						font-size: 12px;
						color: #aaa;
						cursor: default;
						&:hover{
							text-decoration: none;
						}
					}
				}
			}
		}
		&:hover{
			background-color: #fcfcfc;
		}
		&.checked{
	//	  background-image: -webkit-linear-gradient( 270deg, #ddd 0%, #ddd 100%);
		}
	}

//角标数字
.redBallNumBox{
	position: relative;
}
.redNum,
.redBallNum{
	position: absolute;
	width: 18px;
	height: 18px;
	right: -9px;
	top: -9px;
	font-size: 12px;
	text-align: center;
	line-height: 18px;
	color: #fff;
	background-color: #f94d30;
	border-radius: 50%;
	box-shadow: 1px 1px 1px rgba(0,0,0,.5);
}



.requestListBox{
	padding-bottom: 10px;
	.requestList{
		position: relative;
		min-height: 60px;
		margin: 0px 30px;
		border-bottom: 1px solid #ddd;
		.clearafter();

//	  -webkit-transform-origin: top center;
		-webkit-animation:showByFade 0.5s 0.2s ease both;
		.requestIcon{
			position: absolute;
			left: 8px;
			top: 8px;
			img{
				width: 40px;
				height: 40px;
				border-radius: 50%;
			}
			.redBallNum{
				margin-top: 3px;
				margin-right: 3px;
			}
		}
		.requestInfo{
			margin-left: 56px;
			margin-right: 160px;
			padding-top: 8px;
			padding-right: 8px;
			.requestTitle{
				float: left;
				line-height: 20px;
			}
			.requestDate{
				float: right;
				color: #aaa;
				line-height: 20px;
			}
			.requestTips{
				clear: both;
				color: #999;
			}
		}
		.requestConfirm{
			position: absolute;
			width: 160px;
			right: 0;
			top: 0;
			padding: 16px 0px;
			line-height: 24px;
			.btn{
				line-height: 12px;
			}
		}
	}
	.requestList:hover{
		background-color: #f4f4f4;
	}
}




.close{
	position: absolute;
	top: 0px;
	right: 0px;
	cursor: pointer;

	&:before{
		content: '×';
		display: block;
		width: 40px;
		height: 40px;
		color: #999;
		font-size: 22px;
		line-height: 40px;
		text-align: center;
		-webkit-transition: .3s;
	}
	&:hover{
		&:before{
			color: #4daa63;
		}
	}
	&.rect{
		 &:before{
			content: '×';
			display: block;
			width: 20px;
			height: 20px;
			background-color: #222;
			border-radius: 4px;

			color: #444;
			font-size: 20px;
			line-height: 18px;
			text-align: center;
			-webkit-transition: .3s;
		}
		&:hover{
			&:before{
				color: @danger-color;
			}
		}
	}
}

//半透明input下边框版
.loginBox{
	position: absolute;
	overflow: hidden;
	width: 350px;
	height: 480px;
	left: 50%;
	top: 50%;
	margin-left: -175px;
	margin-top: -265px;
	//background-color: rgba(240,240,240,.9);
	background-color: rgba(240,240,240,1);
	border:1px solid #ddd;
	border-radius: 4px;
	box-shadow: 0px 4px 9px 0px rgba(0,0,0,.5);
	.formBox{
		position: absolute;
		width: 100%;
		left: 0;
		top: 0;
		bottom: 0;
		&.form_login{
			/*
			-webkit-transition: .3s .3s;
			.loginSubmitBtn {
				-webkit-transition: .3s .3s;
			}
			.setMoreBox{
				height: 0;
				-webkit-transition: .3s .3s;
				.setMoreOpition{
					opacity: 0;
					-webkit-transition: .3s;
				}
			}
			&.setLogin{
				-webkit-transition: .3s;
				top: -40px;
				.loginSubmitBtn {
					-webkit-transition: .3s;
					bottom: 100px;
				}
				.setMoreBox{
					-webkit-transition: .3s;
					height: 50px;
					.setMoreOpition{
						-webkit-transition: .3s .3s;
						opacity: 1;
					}
					.setMoreBtn{
						background-position: 0 -50px;
						&:hover{
							background-position: -50px -50px;
						}
					}
				}
			}
			*/
			@setMoreBoxHeight:0px;
			-webkit-transition: .3s .15s;
			.loginSubmitBtn {
				-webkit-transition: .3s .15s;
			}
			.setMoreBox{
				height: 0;
				-webkit-transition: .3s .15s;
				.setMoreOpition{
					opacity: 0;
					-webkit-transition: .3s;
				}
			}
			&.setLogin{
				-webkit-transition: .3s;
				top: -@setMoreBoxHeight - 40;
				.loginSubmitBtn {
					-webkit-transition: .3s;
					bottom: @setMoreBoxHeight + 100;
				}
				.setMoreBox{
					-webkit-transition: .3s;
					height: @setMoreBoxHeight + 50;
					.setMoreBtn{
						background-position: 0 0px;
						&:hover{
							background-position: -50px 0px;
						}
					}
					.setMoreOpition{
						-webkit-transition: .3s .15s;
						opacity: 1;
					}
				}
			}
			.setMoreBox{
				position: absolute;
				width: 100%;
				left: 0;
				bottom: 0;
				padding-bottom: 10px;
				.setMoreBtn{
					display: block;
					position: absolute;
					width: 50px;
					height: 50px;
					left: 50%;
					top: -40px;
					margin-left: -25px;
					background-image: url(../images/icon_login_setmore.png);
					background-position: 0 -50px;
					&:hover{
						background-position: -50px -50px;
					}
				}
				.selectServer{
					position: relative;
					padding-left: 36px;
					line-height: 30px;
					.serverListBox{
						position: absolute;
						overflow: hidden;
						z-index: 4;
						width: 200px;
						left: 120px;
						bottom: 0px;
						.serverList{
							cursor: pointer;
							position: relative;
							display: none;
							padding-left: 30px;
							overflow: hidden;
							white-space:nowrap;
							text-overflow: ellipsis;
							&:hover{
								background-color: #ddd;
							}
							&.disabled,
							&.checked{
								display: block;
								&:hover{
									background-color: transparent;
								}
							}
							&.checked{
								&:after{
									content: '';
									cursor: pointer;
//								  position: absolute;
									width: 30px;
									height: 30px;
//								  right: 0;
//								  top: 0;
									position: relative;
									top: 10px;
									margin-top: -10px;
									display: inline-block;
									background-image: url(../images/icon_select.png);
									background-position: 0 0;
								}
								&:hover{
									background-color: transparent;
									&:after{
										background-position: -30px 0;
									}
								}
							}
							&:before{
								content: '';
								position: absolute;
								display: block;
								width: 8px;
								height: 8px;
								left: 15px;
								top: 50%;
								margin-top: -4px;
								background-color: transparent;
								border-radius: 50%;
							}
							&.status0:before{
								background-color: transparent;
							}
							&.status1:before{
								background-color: #0bcf24;
							}
							&.status2:before{
								background-color: #ffb10a;
							}
							&.status3:before{
								background-color: #fb4b39;
							}
							&.status4:before{
								background-color: #999999;
							}
						}
						&.open{
							overflow: auto;
							max-height: 450px;
							background-color: #fff;
							border-radius: 4px;
							box-shadow: 2px 2px 8px rgba(0,0,0,.5);
							&::-webkit-scrollbar-thumb {
								background-color: rgba(145,145,155,255);
								border:1px solid transparent;
							}
							.serverList{
								display: block;
								&.checked{
									background-color: #eee;
									&:after{
										background-image: none;
									}
									&:hover{
										//background-color: #eee;
									}
								}
							}
						}
					}
				}
			}
		}
	}
	.texthoverBtn{
		display: inline-block;
		padding: 5px 5px;
		border:1px solid transparent;
		color: @primary-color;
		//border:1px solid #4daa63;
		border-radius: 3px;
		text-decoration: none;
		-webkit-transition: .2s;
		&:hover{
			background-color: @primary-color;
			color: #fff;
		}
	}
	.topNav{
		height: 70px;
		padding: 15px 15px 0px 15px;
		margin: 0 auto;
		text-align: left;
		a.text{
			display: inline-block;
			padding: 5px 16px;
			border:1px solid transparent;
			color: #4daa63;
			border-radius: 3px;
			text-decoration: none;
			-webkit-transition: .2s;
			&:hover{
				border:1px solid @primary-color;
				//background-color: #4daa63;
				//color: #fff;
			}
		}
	}
	.logoBox{
		height: 125px;
		text-align: center;
		.logoImg{
			width: 260px;
			height: 90px;
		}
	}
	.checkbox{
		color: #777;
	}
	.form-group{
		margin: 0 36px;
		.input-group{
			padding-bottom: 2px;
			margin-bottom: 20px;
			border-bottom: 1px solid #ccc;

			input[type="text"].form-ctrl,
			input[type="password"].form-ctrl,
			div.inputTips {
				padding: 5px;
				background-color: transparent;
				border: 0;
				//color: #777;
				font-size: 14px;
				line-height: 18px;
			}
			div.inputTips{
				font-weight: normal;
				border-bottom: 0;
			}
			.sendMsg{
				height: 30px;
			}
		}
	}
	.loginSubmitBtn{
		position: absolute;
		width: 280px;
		height: 50px;
		left: 50%;
		margin-left: -140px;
		bottom: 60px;
	}
}
.loginBoxHide{
	-webkit-animation: hideByRotate .3s linear;
}
.loginBoxShow{
	-webkit-animation: showByRotate .2s linear;
}




.selectBox{
	position: relative;
	display: inline-block;
	min-width: 120px;
	padding-right: 16px;
	background-color: #fff;
	border: 1px solid #eee;
	.arrow{
		width: 20px;
		text-align: center;
		height: 30px;
		line-height: 30px;
		position: absolute;
		right: 0;
		top: 0%;
		cursor: pointer;
		color: #999999;
	}
	.arrow:before{
		content: '▼';
	}
	.selectedBox{
		padding: 8px;
		cursor: default;
		line-height: 12px;
		display: block;
	}
}
.optionBoxBg{
	position: fixed;
	z-index: 998;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	background-color: #000;
	opacity: 0;
	filter:alpha(opacity=0);

}
.optionBox{
	position: absolute;
	z-index: 999;
	left: -1px;
	border: 1px solid #eee;
	background-color: #fff;
	overflow-x: hidden;
	overflow-y: auto;
	.optionListBox{
		position: relative;
		margin: 0;
		padding: 0;
		list-style: none;
		.optionList{
			cursor: pointer;
			padding: 10px 5px;
			line-height: 1em;
			background-color: #fff;
		}
		.optionList:hover{
			color: #fff;
			background-color: #7cc276;
		}
	}
}
.optionTopWhite{
	z-index: 1000;
	position: absolute;
	height: 1px;
	left: 0px;
	top: -1px;
	font-size: 0;
	background-color: #fff;
}


.popUpBoxBlackBg{
	position: fixed;
	z-index: 9999;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	background-color: #000;
	opacity: .7;
	filter:alpha(opacity=70);
}

////旧版
//.popUpBox{
//  position: fixed;
//  z-index: 9999;
//  min-width: 290px;
//  left: 0;
//  top: 0;
//  padding: 20px;
//	.close{
//	}
//  .popUpBoxTitle{
//	  padding-bottom: 20px;
//	  font-size: 14px;
//	  color: #515a7a;
//
//  }
//  .popUpBoxContent{
//	  padding-bottom: 20px;
//  }
//  .submitBtnBox{
//	  margin: 0 -10px;
//	  text-align: center;
//	  .clearafter();
//	  .btn.yes,
//	  .btn.no{
//	  	width: 100%;
//	  	padding: 8px;
//		  margin: 0 auto;
//		  border:0;
//		  color: #fff;
//	  }
//
//  }
//  &.alertBox{
//		width: 300px;
//		.close{
//			position: absolute;
//			top: 10px;
//			right: 10px;
//			cursor: pointer;
//		}
//
//	}
//	&.alert{
//		.popUpBoxContent{
//			//text-align: center;
//		}
//		.submitBtnBox{
//			.btn.yes{
//				width: 110px;
//				margin: 0 auto;
//			}
//		}
//
//	}
//	&.tips{
//		display: table;
//		min-width: 0;
//		padding: 0px;
//		background-color: transparent;
//		border: 1px solid transparent;
//		border-top: 1px solid transparent;
//		color: #fff;
//		text-align: center;
//		.tipsContent{
//			display:table-cell;
//			vertical-align:middle;
//			width:100%;
//			height:100%;
//			padding: 20px;
//		}
//		&.white{
//			color: #888;
//		}
//
//	}
//	&.mark{
//		position: absolute;
//		display: table;
//		min-width: 0;
//		padding: 0px;
//		background-color: rgba(0,0,0,.4);
//		border: 1px solid transparent;
//		border-top: 1px solid transparent;
//		color: #fff;
//		text-align: center;
//		.markContent{
//			display:table-cell;
//			vertical-align:middle;
//			width:100%;
//			height:100%;
//			padding: 2px 6px;
//		}
//		.arrow{
//			position: absolute;
//			width: 7px;
//			height: 7px;
//			background-image: url(../images/icon_popupbox_arrow.png);
//			opacity: .4;
//			&.left{
//				left: -8px;
//				top: 8px;
//				background-position: left center;
//			}
//			&.right{
//				right: -8px;
//				top: 8px;
//				background-position: right center;
//			}
//			&.top{
//				left: 22px;
//				top: -8px;
//				background-position: center top;
//			}
//			&.bottom{
//				left: 22px;
//				bottom: -8px;
//				background-position: center bottom;
//			}
//		}
//		&.white{
//			color: #888;
//		}
//
//	}
//	&.wait{
//		min-width: 0;
//		background-color: rgba(0,0,0,.8);
//		border: 0;
//		.popUpBoxContent{
//			padding-bottom: 0;
//		}
//	}
//	&.white{
//		min-width: 220px;
//		background-color: #fff;
//		box-shadow: 0px 0px 5px rgba(0,0,0,.2) ;
//		border: 1px solid transparent;
//		border-top: 1px solid transparent;
//		border-radius: 0;
//		.close{
//			top: 0px;
//			right: 0px;
//		}
//		.close:before{
//			color: #555;
//		}
////  .close:hover:before{
////	  -webkit-animation:hoverByRotate 2s 0s ease both;
////  }
//		.popUpBoxTitle{
//			height: auto;
//			padding-left:0px;
//			padding-bottom: 5px;
//			font-size: 14px;
//			line-height: 1;
//		}
//		.popUpBoxContent{
//			padding: 0px;
//			padding-bottom: 10px;
//		}
//		.submitBtnBox{
//			padding-bottom: 0px;
//		}
//
//	}
//  &.black{
//	  background-color: rgba(0,0,0,.8);
//	  color: #fff;
//	  border:0;
//	  .close{
//		  &:hover:before{
//			  color: #fff;
//		  }
//	  }
//	  .close:before{
//		  color: #ccc;
//	  }
//	  .submitBtnBox{
//		  .btn.yes,
//		  .btn.no{
//			  border:0;
//		  }
//	  }
//  }
//  &.textCenter{
//	  .popUpBoxContent{
//		  text-align: center;
//	  }
//  }
//	&.center{
//		left: 50%;
//		margin-left: -150px;
//		margin-top: 80px;
//		background-color: transparent;
//		border: 0;
//		.close{
//			display: none;
//		}
//	}
//	&.white.alertBox .popUpBoxContent {
//		padding-bottom: 0px;
//	}
//}

.pageContent.profileInfo{
	width: 550px;
	margin: 0 auto;
	.userHead{
		position: relative;
		width: 70px;
		height: 70px;
		margin: 35px auto 0px auto;
		border:1px solid #ddd;
		border-radius: 50%;
		overflow: hidden;
		cursor: pointer;
		.headImg{
			width: 68px;
			height: 68px;
			border-radius: 50%;
		}
		.changeBtn{
			display: none;
			position: absolute;
			width: 68px;
			height: 68px;
			left: 0;
			top: 0;
			background-color: rgba(0,0,0,.5);
			line-height: 68px;
			color: #fff;
			text-align: center;
		}
		&:hover .changeBtn{
			display: block;
		}
	}
	.userName{
		height: 30px;
		padding-top: 18px;
		margin-bottom: 5px;
		border-bottom: 1px solid #ddd;
		.textBox{
			width: 210px;
			height: 20px;
			margin: 0 auto;
			background-color: #fefefe;

			font-size: 14px;
			text-align: center;
			font-weight: bold;
		}
	}
	.userTips{
		width: 400px;
		margin: 0 auto;
		.userTipsInput{
			display: block;
			width: 100%;
			padding: 5px;
			background-color: #fefefe;
			border:1px solid transparent;
			font-size: 12px;
			color: #999;
			text-align: center;
		}
	}
	.userInfoListBox{
		position: relative;
		padding:0 36px;
		margin-top: 50px;
		background-color: #fff;
		//border: 1px solid #ddd;
		.row{
			padding: 4px 0px;
			line-height: 30px;
			.th{
				color: #888;
			}
		}
		.editInfoBtn,
		.saveInfoBtn{
			position: absolute;
			right: 0;
			top: -30px;
		}
		.selectBox{
			min-width: 50px;
			border-radius: 3px;
		}
		input[type="text"].form-ctrl,
		input[type="password"].form-ctrl,
		div.inputTips {
			background-color: #fff;
			border-radius: 3px;
		}
		.btn.df{
			height: 29px;
		}
	}
	#userInfoEdit.userInfoListBox{
		.row{
//			padding: 6px 0px 7px 0px;
		}
	}
	#conName{
		margin-left: 15px;
	}
}
//.popUpBox.moodFace{
//	@icon_colNum:4;
//	@icon_rowNum:4;
//	min-width: 0;
//	padding: 10px;
//	padding-right: 0;
//	margin-top:@icon_rowNum*(-30px)-20px;
//	box-shadow: 0px 3px 5px rgba(0,0,0,.4);
//	.popUpBoxContent{
//		width: @icon_colNum*30px + 10px;
//		height: @icon_rowNum*30px;
//		padding: 0;
//		overflow-x: hidden;
//		overflow-y: auto;
//		.moodFaceBox{
//			position: relative;
//			width: @icon_colNum*30px;
//			background-image: url(../images/border_moodface.png);
//			background-position: 1px 0px;
//			.clearafter();
//			.moodFaceList{
//				float: left;
//				padding: 5px;
//				//-webkit-transform: translate(-1px,-1px);
//				img{
//					width: 20px;
//					height: 20px;
//					cursor: pointer;
//				}
//			}
//			.moodFaceList:nth-child(@{icon_colNum}n+1){
//				padding-left: 4px;
//				border-left: 1px solid #fcfcfc;
//			}
//			.clearLine{
//				position: absolute;
//				width: 100%;
//				height: 1px;
//				left: 0;
//				bottom: 0px;
//				background-color: #fcfcfc;
//			}
//		}
//		.arrow{
//			position: absolute;
//			width: 30px;
//			height: 30px;
//			left: 0px;
//			bottom: -30px;
//			background-image: url(../images/icon_moodface_arrow.png);
//			background-position: center top;
//		}
//	}
//}



.popUpWin{
	position: fixed;
	z-index: 9999;
	.popUpBoxBg{
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		background-color: rgba(0,0,0,.8);
	}
	.popUpBoxFg{
		position: relative;
//	  display: inline-block;
		padding: 5px;
		background-color: #ddd;
		border-radius: 4px;
		.close{
			position: absolute;
			top: 10px;
			right: 10px;
			cursor: pointer;
			&:before{
				content: '×';
				display: block;
				width: 20px;
				height: 20px;
				color: #999;
				font-size: 20px;
				line-height: 18px;
				text-align: center;
			}
			&:hover:before{
				color: @primary-color;
			}
		}
		.popUpBoxTitle,
		.popUpBoxContent,
		.submitBtnBox{
			background-color: rgba(255,255,255,.8);
		}
		.popUpBoxTitle{
			padding: 10px;
			//border-bottom: 1px solid rgba(0,0,0,.1);
			font-weight: bold;
		}
		.popUpBoxContent{
			padding: 40px;
			color: #363636;
		}
		.submitBtnBox{
			padding: 0px 10px 10px 10px;
			width: 100%;
			.col{
				padding: 5px;
				text-align: center;
			}
			&.confirm{
				.col{
					display: inline-block;
					width: 50%;
				}
			}
		}
	}
	&.hidden{
		display: block;
		.popUpBoxBg{
			background-color: transparent;
		}
	}
	&.wait{
		.popUpBoxFg{
			background-color: transparent;
			border: 0;
			.popUpBoxTitle,
			.popUpBoxContent,
			.submitBtnBox{
				background-color: transparent;
			}
		}
		&.black{
			.popUpBoxFg{
				background-color: rgba(0,0,0,.5);
				box-shadow: none;
				.popUpBoxContent{
					padding: 0;
				}
			}
		}
	}
	&.fullScreen{
		width: 100%;
		height: 100%;
		.popUpBoxFg{
			position: absolute;
			left: 10px;
			top: 10px;
			right: 10px;
			bottom: 10px;
		}
	}
	&.menu{
		border: 0;
		width: 0;
		height: 0;
		.popUpBoxFg{
			position: absolute;
			padding: 0;
			left: 0;
			top: 0;
			border: 0;
			.popUpBoxContent{
				position: absolute;
				padding: 0;
				background-color: transparent;
				border-radius: 3px;
				.menuBtnBox{
					padding: 0;
					//border: 1px solid #eee;
					border-radius: 3px;
					box-shadow: 0px 3px 5px rgba(0,0,0,.4);
					overflow: hidden;
					.menuBtn{
						padding: 8px 8px 8px 16px;
						cursor: default;
						background-image: url(../images/icon_point_green.png);
						background-repeat: no-repeat;
						background-position: -100px center;
						border-top: 1px solid #eee;
						white-space: nowrap;
						&:first-child{
							border-top-width: 0px;
						}
						&:hover{
							background-position: 7px center;
						}
					}
				}
				.menuArrow{
					position: absolute;
					background-repeat: no-repeat;
					&:before{
						content: '';
						position: absolute;
						display: block;
					}
				}
			}
		}
		&.arrowDown{
			.popUpBoxFg{
				.popUpBoxContent{
					left: 0;
					bottom: 0;
					.menuArrow{
						width: 30px;
						height: 12px;
						bottom: -12px;
						left: 0;
						background-position: left bottom;
						&:before{
							height: 1px;
							width: 36%;
							left: 32%;
							top: -1px;
						}
					}
				}
			}
		}
		&.arrowTop{
			.popUpBoxFg{
				.popUpBoxContent{
					left: 0;
					top: 0;
					.menuArrow{
						width: 30px;
						height: 12px;
						top: -12px;
						left: 0;
						background-position: left top;
						&:before{
							height: 1px;
							width: 36%;
							left: 32%;
							bottom: -1px;
						}
					}
				}
			}
		}
		//颜色设定
		//白色
		.popUpBoxFg{
			.popUpBoxContent{
				border: 1px solid #eee;
				.menuBtnBox{
					background-color: #fff;
					.menuBtn{
						color: #333;
						background-color: #fff;
						&:hover{
							background-color: #efefef;
						}

					}
				}
				.menuArrow{
					background-image: url(../images/icon_menu_arrow_white.png);
					&:before{
						background-color: #fff;
					}
				}
			}
		}
		//黑色
		&.black{
			.popUpBoxFg{
				.popUpBoxContent{
					border: 1px solid #61626a;
					.menuBtnBox{
						background-color: #52535b;
						.menuBtn{
							color: #fff;
							background-color: #52535b;
							border-top-color:#61626a;
							&:hover{
								background-color: #47484f;
							}

						}
					}
					.menuArrow{
						background-image: url(../images/icon_menu_arrow.png);
						&:before{
							background-color: #52535b;
						}
					}
				}
			}
		}
	}
	&.tips{
		.popUpBoxFg{
			.popUpBoxContent{
				padding: 20px;
			}
		}
	}
	//黑色
	&.black{
		.popUpBoxFg{
			background-color: rgba(0,0,0,.7);
			.boxShadow();
			.popUpBoxContent{
				background-color: transparent;
				border-color:transparent;
				color: #fff;
			}
		}
	}
	//表情菜单
	&.menu.moodFace{
		@icon_colNum:8;
		@icon_rowNum:4;
		.popUpBoxFg{
			.popUpBoxContent{
				.menuBtnBox{
					width: @icon_colNum*30px + 18px;
					height: @icon_rowNum*30px + 10px;
					//padding: 5px 0px 5px 5px;
					border:5px solid transparent;
					border-right: 0;
					overflow-x: hidden;
					overflow-y: auto;
					.clearafter();
					.menuBtn{
						float: left;
						padding: 5px;
						border-width:0 ;
						border-left-width:0;
						border-top-width:0;
						border-right-width:1px;
						border-bottom-width:1px;
						background-image: none;
						img{
							display: block;
							width: 20px;
							height: 20px;
						}
						&:first-child {
							border-top-width: 0px;
							border-radius: 3px 0px 0px 0px;
						}
					}
					.menuBtn:nth-child(@{icon_colNum}n){
						border-right-width:0px;
					}
					.clearLine{
						position: absolute;
						width: 100%;
						height: 1px;
						left: 0;
						bottom: 0px;
						background-color: #fcfcfc;
					}
				}

			}
		}

	}
	&.wait.fullscreen.white{
		.popUpBoxBg{
			background-color: #fff;
		}
		.popUpBoxFg{
			box-shadow: none;
		}
	}
}

//弹出框内容样式补充
.popUpWin{
	.popUpBoxFg{
		background-color: #fcfcfc;
		border: 1px solid #aaa;
		border-top: 1px solid #ddd;
		border-radius: 4px;
		box-shadow: 5px 5px 5px rgba(0,0,0,.2);
		//padding: 0;
		.popUpBoxContent{
			padding: 10px;
		}
		.close{
			position: absolute;
			display: block;
			width: 20px;
			height: 20px;
			padding: 0;
			margin: 0;
			top: 10px;
			right: 10px;
			background-color: transparent;
			border:0;

			font-size: 20px;
			line-height: 18px;
			text-align: center;
			color: #999;
			cursor: pointer;
			&:hover{
				color: @primary-color-hover;
			}
		}

		.submitBtnBox{
			padding: 0px 10px 10px 10px;
			width: 100%;
			.col{
				.btn{
					display: block;
				}
			}
			&.confirm{
				.col{
					.btn{
						display: block;
					}
				}
			}
			.btn{
				padding: 8px 16px;
				font-size: 14px;
				line-height: 14px;
			}
			.btn.yes{
				color: #fff;
				background-color: @primary-color;
				border-color: @primary-color;
				&:hover{
					background-color: @primary-color-hover;
				}
			}
			.btn.no{
				color: #fff;
				background-color: @gray-color;
				border-color: @gray-color;
				&:hover{
					background-color: @gray-color-hover;
				}
			}
		}
	}
	//半透明黑色
	&.black_alpha80{
		.popUpBoxFg{
			.popUpBoxContent{
				background-color: rgba(0,0,0,.8);
				.menuTipsBox{
					padding: 6px 12px;
					.menuTips{
						white-space: nowrap;
						color: #fff;
					}
				}
				.menuArrow{
					background-image: url(../images/icon_menu_arrow_black_alpha80.png);
					&:before{
						background-color: rgba(0,0,0,.8);
					}
				}
			}
		}
	}
	.userIntroBox{
		border-bottom: 1px solid #f0f0f0;
		padding-bottom: 15px;
		.headImg{
			width: 70px;
			height: 70px;
			border-radius: 50%;
			overflow: hidden;
			margin: 0 auto;
			margin-bottom: 5px;
			img{
				width: 70px;
				height: 70px;
			}
		}
		.userName{
			font-size: 14px;
			font-weight: bold;
		}
		.userSign{
			color: #999;
			padding: 0 30px;
		}
		&.default{
			margin-left:-10px;
			margin-right:-10px;
			text-align: center;
		}
		&.style2{
			padding-top: 10px;
			border-bottom: 0;
			table{
				line-height: 22px;
				tr{
					th{
						width: 60px;
						color: #999;
						font-weight: normal;
						text-align: left;
					}
				}
			}
		}
	}
	.userInfoBox{
		.userTipsInput{
			border: 0;
			background-color: transparent;
		}
		table{
			width: 100%;
			padding: 10px 0px;
			tr{
				th,td{
					padding: 5px;
					line-height: 1;
				}
				th{
					width: 40%;
					color: #999;
					font-weight: normal;
					text-align: right;
				}
				td{

				}
			}
		}
	}
	&.centerInFullscreen{
		.popUpBoxFg{
			background-color: transparent;
			border-color: transparent;
			box-shadow: none;
			.close{
				display: none;
			}
		}
	}
}




.questionAlertBox{
	position: absolute;
	left: 20px;
	top: 20px;
	right: 20px;
	bottom: 20px;
	background-color: #4a4b53;
	border: 1px solid #323237;
	border-radius: 8px;
	box-shadow: 0px 5px 5px rgba(0,0,0,.3);
	.content{
		position: absolute;
		left: 15px;
		top: 45px;
		right: 15px;
		bottom: 15px;
		overflow: auto;

		color: #eee;
		img{
			max-width: 100%;
		}
	}
	.info{
		position: absolute;
		width: 100%;
		height: 30px;
		left: 0;
		top: 0;
		border-bottom: 1px solid #404148;

		color: #787a83;
		line-height: 30px;
		.left{
			float: left;
			padding-left: 15px;
		}
		.right{
			float: right;
			padding-right: 15px;
		}
	}
}
.evaluationTitle{
	font-size: 16px;
	padding-bottom: 20px;
}
.prizeRank{
	padding: 15px;
	margin-bottom: 20px;
	background-color: #eee;
	border:1px solid #ccc;
	.table{
		display: block;
		.clearafter();
		.tr{
			display: block;
			.td{
				display: block;
				float: left;
				text-align: center;
			}
		}
	}
	.nickname{
		width: 100%;
		padding: 0 2px;
		border-bottom:1px solid #ddd;
		color: #bbb;

		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	.prizeNum{
		width: 100%;
		.num{
			font-size: 18px;
		}
	}
}
.lessonComment{
	position: absolute;
	width: 100%;
	left: 0px;
	top: 230px;
	bottom: 0px;
	border:1px solid #ccc;
}
.studentEvaluationBox{
	position: absolute;
	width: 100%;
	left: 0;
	top: 45px;
	bottom: 60px;
	overflow: auto;
}
.studentEvaluation{
	padding-top: 15px;
	.clearafter();
	.userInfo{
		float: left;
		width: 80px;
		text-align: center;
		.headImg{
			img{
				width: 40px;
				height: 40px;
				border:1px solid #ccc;
				border-radius: 50%;
				box-shadow: -1px 1px 2px rgba(0,0,0,.1);
			}
		}
		.nickname{
			.textOverflow();
		}
	}
	.evaluationInfo{
		margin-left: 90px;
		.scoreStar{
			display: block;
		}
		textarea.studentComment{
			display: block;
			width: 100%;
			height: 50px;
			border:1px solid #ccc;
			border-radius: 4px;
		}
	}
}
.scoreStar{
	position: relative;
	display: inline-block;
	width: 125px;
	height: 25px;
	top: 7px;
	margin-top: -7px;
	background-image: url(../images/icon_scorestar.png);
	background-position: -125px 0;
	&.check1{
		background-position: -100px 0;
	}
	&.check2{
		background-position: -75px 0;
	}
	&.check3{
		background-position: -50px 0;
	}
	&.check4{
		background-position: -25px 0;
	}
	&.check5{
		background-position: 0 0;
	}
	li.star{
		float: left;
		width: 25px;
		height: 25px;
	}
	li.val{
		display: none;
	}
	&.edit{
		li.star{
		   cursor: pointer;
		}
	}
}



body.mobileFrame{
	overflow: hidden;
	.loginBox{
		width: auto;
		height: auto;
		left: 0px;
		right: 0px;
		top: 0px;
		bottom: 0px;
		margin: auto;
	}
}


/* model class  */
@dicePerspective:6em;
.diceBox{
	font-size: 100px;
	height: 1em;
	width: 1em;
	position: absolute;
	left: 50%;
	top: 50%;
	margin-left: -.5em;
	margin-top: -.5em;
	z-index: 1;


	-webkit-transform-style: preserve-3d;
//  -webkit-perspective: 650px;
	-webkit-transform: perspective(@dicePerspective) rotateX(0deg) rotateY(0deg);
	-webkit-transition: 1.5s -webkit-transform ease-out;


	&.rolling{
		-webkit-transition: .5s all linear;
	}
	.faceBox{
		@pointSize:.2em;
		@pointColor:#e00;
		@poiontPosition:@pointSize * 1.25;
		@faceScale:0.6;
		position: absolute;
		width: @pointSize*5;
		height: @pointSize*5;
		left: 0;
		top: 0;
		background-color: #fff;
		//border:1px solid #ccc;
		border-radius: .2em;
		box-shadow: 0px 0px .4em rgba(0,0,0,.1) inset;
		&:after{
			content: '';
			position: absolute;
			display: block;
			width: @pointSize;
			height: @pointSize;
			left: 50%;
			top: 50%;
			margin-left: -@pointSize /2;
			margin-top: -@pointSize /2;
			background-color: @pointColor;
			border-radius: 50%;
			z-index: 100;
		}

		&[data-num="x"],
		&[data-num="y"],
		&[data-num="z"],
		&[data-num="x1"],
		&[data-num="x2"],
		&[data-num="y1"],
		&[data-num="y2"],
		&[data-num="z1"],
		&[data-num="z2"]{
			background-color: #f2f2f2;
			border:2px solid transparent;
			border-radius: .1em;
			box-shadow: none;
			&:after{
				background-color: transparent;
			}
		}
		@innerFaceTranslate:0.28em;
		&[data-num="x"]{
			border-radius: 0;
			-webkit-transform: rotateX(90deg) translateZ(0) scaleX(@faceScale) scaleY(@faceScale);
		}
		&[data-num="x1"]{
			-webkit-transform: rotateX(90deg) translateZ(@innerFaceTranslate) scaleX(@faceScale) scaleY(@faceScale);
		}
		&[data-num="x2"]{
			-webkit-transform: rotateX(90deg) translateZ(-@innerFaceTranslate) scaleX(@faceScale) scaleY(@faceScale);
		}
		&[data-num="y"]{
			border-radius: 0;
			-webkit-transform: rotateY(90deg) translateZ(0) scaleX(@faceScale) scaleY(@faceScale);
		}
		&[data-num="y1"]{
			-webkit-transform: rotateY(90deg) translateZ(@innerFaceTranslate) scaleX(@faceScale) scaleY(@faceScale);
		}
		&[data-num="y2"]{
			-webkit-transform: rotateY(90deg) translateZ(-@innerFaceTranslate) scaleX(@faceScale) scaleY(@faceScale);
		}
		&[data-num="z"]{
			border-radius: 0;
			-webkit-transform: rotateZ(90deg) translateZ(0) scaleX(@faceScale) scaleY(@faceScale);
		}
		&[data-num="z1"]{
			-webkit-transform: rotateZ(90deg) translateZ(@innerFaceTranslate) scaleX(@faceScale) scaleY(@faceScale);
		}
		&[data-num="z2"]{
			-webkit-transform: rotateZ(90deg) translateZ(-@innerFaceTranslate) scaleX(@faceScale) scaleY(@faceScale);
		}
//	  &[data-num="x"]{
//		  -webkit-transform: rotateX(90deg) rotateY(0deg) translateZ(0px) scaleX(@faceScale) scaleY(@faceScale);
//	  }
//	  &data-num="y"]{
//		  -webkit-transform: rotateX(0deg) rotateY(90deg) translateZ(0px) scaleX(@faceScale) scaleY(@faceScale);
//	  }
//	  &data-num="z"]{
//		  -webkit-transform: rotateX(90deg) rotateY(90deg) translateZ(0px) scaleX(@faceScale) scaleY(@faceScale);
//	  }

		@outFaceTranslate:0.3em;
		&[data-num="1"]{
			-webkit-transform: rotateX(0deg) rotateY(0deg) translateZ(@outFaceTranslate) scaleX(@faceScale) scaleY(@faceScale);
			&:after{
				width: 30%;
				height: 30%;
				margin-left: -15%;
				margin-top: -15%;
			}
		}
		&[data-num="2"]{
			-webkit-transform: rotateX(0deg) rotateY(90deg) translateZ(@outFaceTranslate) scaleX(@faceScale) scaleY(@faceScale); opacity: 1;
			&:after{
				background-color: transparent;
				box-shadow: -@poiontPosition -@poiontPosition 0 #c00,@poiontPosition @poiontPosition 0 #c00;
			}
		}
		&[data-num="3"]{
			-webkit-transform: rotateX(0deg) rotateY(180deg) translateZ(@outFaceTranslate) scaleX(@faceScale) scaleY(@faceScale); opacity: 1;
			&:after{
				background-color: #c00;
				box-shadow: -@poiontPosition -@poiontPosition 0 #c00,@poiontPosition @poiontPosition 0 #c00;
			}
		}
		&[data-num="4"]{
			-webkit-transform: rotateX(0deg) rotateY(270deg) translateZ(@outFaceTranslate) scaleX(@faceScale) scaleY(@faceScale); opacity: 1;
			&:after{
				background-color: transparent;
				box-shadow: -@poiontPosition -@poiontPosition 0 #c00,@poiontPosition -@poiontPosition 0 #c00,@poiontPosition @poiontPosition 0 #c00,-@poiontPosition @poiontPosition 0 #c00;
			}
		}
		&[data-num="5"]{
			-webkit-transform: rotateX(-90deg) rotateY(0deg) translateZ(@outFaceTranslate) scaleX(@faceScale) scaleY(@faceScale); opacity: 1;
			&:after{
				background-color: #c00;
				box-shadow: -@poiontPosition -@poiontPosition 0 #c00,@poiontPosition -@poiontPosition 0 #c00,@poiontPosition @poiontPosition 0 #c00,-@poiontPosition @poiontPosition 0 #c00;
			}
		}
		&[data-num="6"]{
			-webkit-transform: rotateX(90deg) rotateY(0deg) translateZ(@outFaceTranslate) scaleX(@faceScale) scaleY(@faceScale); opacity: 1;
			&:after{
				background-color: transparent;
				box-shadow: -@poiontPosition -@poiontPosition 0 #c00,@poiontPosition -@poiontPosition 0 #c00,-@poiontPosition 0px 0 #c00,@poiontPosition 0px 0 #c00,@poiontPosition @poiontPosition 0 #c00,-@poiontPosition @poiontPosition 0 #c00;
			}
		}
	}
	&.roll1{
		transform: perspective(@dicePerspective) rotateX(0deg) rotateY(0deg);
	}
	&.roll2{
		transform: perspective(@dicePerspective) rotateX(0deg) rotateY(-90deg);
	}
	&.roll3{
		transform: perspective(@dicePerspective) rotateX(0deg) rotateY(-180deg);
	}
	&.roll4{
		transform: perspective(@dicePerspective) rotateX(0deg) rotateY(90deg);
	}
	&.roll5{
		transform: perspective(@dicePerspective) rotateX(90deg) rotateY(0deg);
	}
	&.roll6{
		transform: perspective(@dicePerspective) rotateX(-90deg) rotateY(0deg);
	}
}
@-webkit-keyframes diceRolling{
	0%{
		-webkit-transform: perspective(@dicePerspective) rotateX(0deg) rotateY(0deg);
	}
	100%{
		-webkit-transform: perspective(@dicePerspective) rotateX(360deg) rotateY(360deg);
	}
}


@clockPerspective:6em;
.clockTimerBox{
	position: relative;
	display: inline-block;
	top: 50%;
	padding: .2em 0 .2em 0;
	margin-top: -0.9em;
	font-size: 100px;
	white-space: nowrap;
	text-align: center;
	vertical-align: middle;
	.wordBox{
		display: inline-block;
	}
	.charBox{
		position: relative;
		display: inline-block;
		margin: .1em;
		font-family: arial;
		&:after{
			content: '';
			position: absolute;
			display: block;
			width: 100%;
			height: 2px;
			left: 0;
			top: 50%;
			margin-top: -1px;
			//background-color: rgba(0,0,0,0.2);
			background-color: rgba(100,100,100,.5);
			z-index: 2;
		}
		.charPrev,
		.charNow,
		.charNowTop,
		.charPrevTop{
			height: 1.2em;
			padding: .1em;
			background-color: #eee;
			border-radius: .05em;
			box-shadow: .02em .02em .04em rgba(0,0,0,.3);

			line-height: 1em;
			font-family: arial;
			color: #333;
			text-shadow: .02em .02em .04em rgba(0,0,0,.3);
		}
		.charPrev{
			position: relative;
		}
		.charNow,
		.charNowTop,
		.charPrevTop{
			position: absolute;
			left: 0;
			top: 0;
			z-index: 1;
		}
		.charNowTop,
		.charPrevTop{
			overflow: hidden;
			height: 0.6em;
			-webkit-transform-origin: center bottom;
		}
		.charNowTop{
			border-bottom-left-radius: 0;
			border-bottom-right-radius: 0;
			box-shadow: none;
		}

		.charPrevTop{
			box-shadow: none;
			
		}
		.charNow{
		}
        &.showAnimate{
            .charPrevTop{
                -webkit-animation: wordCharPrevTopHide .5s linear both ;
            }
            .charNow{
                -webkit-transform:perspective(@clockPerspective) rotateX(270deg);
                -webkit-animation: wordCharNowShow .5s .5s linear both ;
            }
        }
	}
	.wordBox.colon .charBox,
	.charBox.colon{
		margin-top: 0;
		margin-bottom: 0;
		&:after{
			display: none;
		}
		.charPrev,
		.charNowTop,
		.charPrevTop{
			opacity: 0;
			padding: 0 0;
		}
		.charNow{
			top: -0.12em;
			padding: 0 0;
			background-color: transparent;
			box-shadow: none;
			font-family: "微软雅黑";
			color: #eee;
			-webkit-animation: none;
			-webkit-transform:perspective(@clockPerspective) rotateX(0deg);
		}
	}
	.replayBtn{
		display: none;
		position: absolute;
		z-index: 9;
	    width: 64%;
	    height: 50%;
	    left: 18%;
	    top: 25%;
	    background-color: #1cba77;
	    border-radius: 0.2em;
		.btnText{
			position: absolute;
			width: 100%;
			height: 1em;
			left: 0;
			top: 50%;
			margin-top: -0.5em;
			font-size: .5em;
			line-height: 1;
			text-align: center;
			color: #fff;
			cursor: pointer;
		}
	}
	&.timeUp{
		.charBox.colon,
		.wordBox.colon .charBox,
		.wordBox .charBox{

			.charPrev,
			.charPrevTop,
			.charNowTop{
                color: #c00;
			}
			.charNow{
				color: #c00;
				text-shadow: none;
				-webkit-transform:perspective(@clockPerspective) rotateX(0deg);
				//-webkit-animation: wordCharTimeup .5s ease infinite alternate ;
			}
		}
		&:hover{
			.replayBtn{
				display: block;
			}
		}

	}
	&.clockSet{
		position: relative;
		padding: .4em 0 .4em 0;
		padding-right: 1.2em;
		margin-top: -1.1em;
		.charBox{
			.charPrev{
				-webkit-animation: none;
			}
			.charNow{
				-webkit-animation: none;
				position: relative;

			}
			.setTopBtn,.setBottomBtn{
				position: absolute;
				width: 0px;
				height: 0px;
				left: 50%;
				margin-left: -0.2em;
				border-left: .2em solid transparent;
				border-right: .2em solid transparent;
				cursor: pointer;
			}
			.setTopBtn{
				top: -0.3em;
				border-bottom: .2em solid #fff;
				&:hover{
					border-bottom: .2em solid #7ac970;
				}
			}
			.setBottomBtn{
				bottom: -0.3em;
				border-top: .2em solid #fff;
				&:hover{
					border-top: .2em solid #7ac970;
				}
			}
		}
		.setSubmitBtn{
			position: absolute;
			height: 1.3em;
			right: 0.5em;
			top: 50%;
			padding: 0.5em;
			margin-top: -0.65em;
			background-color: #7cc276;
			border-radius: 0.04em;

			font-size: .3em;
			line-height: .3em;
			text-align: center;
			color: #fff;
			cursor: pointer;
		}
	}

}
@-webkit-keyframes wordCharPrevTopHide{
    0%{
        -webkit-transform:perspective(@clockPerspective) rotateX(360deg);
    }
    100%{
        -webkit-transform:perspective(@clockPerspective) rotateX(270deg);
    }
}
@-webkit-keyframes wordCharNowShow{
    0%{
        -webkit-transform:perspective(@clockPerspective) rotateX(90deg);
    }
    100%{
        -webkit-transform:perspective(@clockPerspective) rotateX(0deg);
    }
}
@-webkit-keyframes wordCharTimeup{
//  0%{
//    opacity: 1;
//  }
//  100%{
//    opacity: 0;
//  }
    0%{
        color: #000;
    }
    100%{
        color: #c00;
    }
//  0%{
//    color: rgba(0,0,0,0);
//  }
//  100%{
//    color: rgba(0,0,0,1);
//  }
}

.clocker{
    @numPaddingWidth:.1em;
    
    font-size: 100px;
    
    position: absolute;
    width: 4.3em;
    height: 1.8em;
    left: 50%;
	top: 50%;
	padding:.2em .1em;
	-webkit-transform:translate(-50%,-50%) translateZ(0);
        
	white-space: nowrap;
	text-align: center;
	.numGroup{
		display: inline-block;
	}
	.numBox{
		position: relative;
		display: inline-block;
		margin: .1em;
		font-family: arial;
        
//		&:after{
//			content: '';
//			position: absolute;
//			display: block;
//			width: 100%;
//			height: 2px;
//			left: 0;
//			top: 50%;
//			margin-top: -1px;
//			//background-color: rgba(0,0,0,0.2);
//			background-color: rgba(100,100,100,.5);
//			z-index: 2;
//		}
        .numPaper{
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            box-shadow: .02em .02em .04em rgba(0,0,0,.3);
            &:after{
                content: "";
                display: none;
                position: absolute;
                width: 100%;
                height: 2px;
                left: 0;
                top: .6em;
                margin-top: -1px;
                z-index: 999999;
                //background-color: #f00;
                //background-image:-webkit-gradient(linear, 0 0, 0 bottom, from(rgba(0,0,0,.5)), to(rgba(0, 0, 0, 0)));
                background-color: #54555d; 
            }
            .pageNowTop,
            .pageNowBottom,
            .pageNextTop,
            .pageNextBottom{
                position: relative;
                position: absolute;
                left: 0;
                height: 0.6em;
                background-color: #eee;
    
                line-height: 1em;
                font-family: arial;
                color: #333;
                text-shadow: .02em .02em .04em rgba(0,0,0,.3);
                overflow: hidden;
                .v{
                    position: absolute;
                    top: 0;
                    height: 1.2em;
                    left: 0;
                    padding: 0 @numPaddingWidth;
                    
                    line-height: 1.2em;
//                  display: none;
                }
                .space{
                    visibility: hidden;
                    padding: 0 @numPaddingWidth;
                    
                    line-height: 1.2em;
                }
            }
            .pageNowTop,
            .pageNextTop{
                top: 0;
                -webkit-transform-origin: center bottom;
                
                border-radius: .05em .05em 0em 0em;
//              color: #f00;
                .v{
                    top: 0;
                }
            }
            .pageNowBottom,
            .pageNextBottom{
                top: .6em;
                -webkit-transform-origin: center top;
                
                border-radius: 0em 0em .05em .05em;
                box-shadow: .02em .02em .04em rgba(0,0,0,.3);
                &:after{
                    content: "";
                    position: absolute;
                    width: 100%;
                    height: 10%;
                    left: 0;
                    top: 0;
                    margin-top: -1px;
                    //background-color: #f00;
                    background-image:-webkit-gradient(linear, 0 0, 0 bottom, from(rgba(0,0,0,.5)), to(rgba(0, 0, 0, 0)));
//                  background-color: #54555d; 
                }
                .v{
                    top: -0.6em;
                }
            }
            .pageNowBottom{
                &.flipUp{
                    z-index: 1;
                    -webkit-animation: clockerPageBottomflipUp .4s linear both ;
                }
            }
            .pageNextTop{
                &.flipUp{
                    z-index: 1;
                    -webkit-transform:perspective(@clockPerspective) rotateX(270deg);
                    -webkit-animation: clockerPageTopflipUp .4s .4s linear both ;
                }
            }
            .pageNowTop{
                &.flipDown{
                    z-index: 1;
                    -webkit-animation: clockerPageTopflipDown .4s linear both ;
                }
            }
            .pageNextBottom{
                &.flipDown{
                    z-index: 1;
                    -webkit-transform:perspective(@clockPerspective) rotateX(90deg);
                    -webkit-animation: clockerPageTopflipUp .4s .4s linear both ;
                }
            }
        }
		.addBtn,.lessBtn{
            position: absolute;
            width: 0px;
            height: 0px;
            overflow: hidden;
            left: 50%;
            margin-left: -0.2em;
            border-left: .2em solid transparent;
            border-right: .2em solid transparent;
            cursor: pointer;
        }
        .addBtn{
            top: -0.3em;
            border-bottom: .2em solid #fff;
            &:hover{
                border-bottom: .2em solid #7ac970;
            }
        }
        .lessBtn{
            bottom: -0.3em;
            border-top: .2em solid #fff;
            &:hover{
                border-top: .2em solid #7ac970;
            }
        }
        .numText{
            padding: 0 @numPaddingWidth;
            height: 1.2em;
        }
		
	}
	.point{
	    position: relative;
	    display: inline-block;
	    top: -.2em;
	    line-height: 1.2em;
        background-color: transparent;
        font-family: "微软雅黑";
        color: #eee;
        text-shadow: .02em .02em .04em rgba(0,0,0,.3);
	}
	.startBtn{
		position: absolute;
		z-index: 9;
	    width: 90%;
	    height: .6em;
	    left: 5%;
	    bottom: .1em;
	    background-color: #1cba77;
	    border-radius: 0.05em;
		.label{
			position: absolute;
			width: 100%;
			height: 1em;
			left: 0;
			top: 50%;
			margin-top: -0.5em;
			font-size: .4em;
			text-transform: uppercase;
			line-height: 1;
			text-align: center;
			color: #fff;
			cursor: pointer;
		}
	}
	.resetBtn{
	    display: none;
		position: absolute;
		z-index: 9;
	    
        width: 80%;
        height: .6em;
        left: 10%;
        top: 50%;
        -webkit-transform:translateY(-50%) translateZ(0);
	    background-color: #1cba77;
        border-radius: 0.05em;
		.label{
			position: absolute;
			width: 100%;
			height: 1em;
			left: 0;
			top: 50%;
			margin-top: -0.5em;
			font-size: .4em;
			line-height: 1;
			text-align: center;
			color: #fff;
			cursor: pointer;
		}
	}
	&:hover{
	    .resetBtn{
	        display: block;
	    }
	}
	&.setting{
	    height: 2.8em;
	    padding-top: .3em;
	    padding-bottom: 1.2em;
	}
	&.timeup{
	    .numGroup{
	        .numBox{
	            .numPaper{
	                .v{
	                    color: #c00;
	                    //-webkit-animation: wordCharTimeup .5s ease infinite alternate ;
	                }
	            }
	        }
	    }
	}

}
@-webkit-keyframes clockerPageBottomflipUp{
    0%{
        -webkit-transform:perspective(@clockPerspective) rotateX(0deg);
    }
    100%{
        -webkit-transform:perspective(@clockPerspective) rotateX(90deg);
    }
}
@-webkit-keyframes clockerPageTopflipUp{
    0%{
        -webkit-transform:perspective(@clockPerspective) rotateX(270deg);
    }
    100%{
        -webkit-transform:perspective(@clockPerspective) rotateX(360deg);
    }
}
@-webkit-keyframes clockerPageTopflipDown{
    0%{
        -webkit-transform:perspective(@clockPerspective) rotateX(360deg);
    }
    100%{
        -webkit-transform:perspective(@clockPerspective) rotateX(270deg);
    }
}
@-webkit-keyframes clockerPageBottomflipDown{
    0%{
        -webkit-transform:perspective(@clockPerspective) rotateX(90deg);
    }
    100%{
        -webkit-transform:perspective(@clockPerspective) rotateX(0deg);
    }
}


.screenshotImgMenu{
	position: absolute;
	padding: 5px;
	color: #fff;
	.menuList{
		&.addtoboard{
			width: 20px;
			height: 20px;
			background-color: rgba(0,0,0,.5);
			background-image: url(../images/icon_add.png);
			background-repeat: no-repeat;
			background-position: center center;
			border-radius: 4px;
			cursor: pointer;
		}
	}
}
.accountListBox{
	position: absolute;
	z-index: 1;
	left: 0;
	right: 62px;
	top: 31px;
	//padding-top: 31px;
	&.open{
		//top: 0;
		.slideBtn{
			//top: 14px;
			//right: 12px;
		}
	}
//	.slideBtn{
//		position: absolute;
//		z-index: 3;
//		display: none;
//		right: 10px;
//		top: -14px;
//		border-left: 6px solid transparent;
//		border-right: 6px solid transparent;
//		border-top: 8px solid #666;
//		margin-top: -4px;
//		cursor: pointer;
//	}
	.slideBtn{
		position: absolute;
		z-index: 3;
		display: none;
		width: 30px;
		height: 30px;
		right: 10px;
		top: -32px;
		background-image: url(../images/icon_select.png);
		background-repeat: no-repeat;
		background-position: 0px -30px;
		cursor: pointer;
		&:hover{
			background-position: -30px -30px;
		}
	}
	.accountList{
		display: none;
		overflow: auto;
		max-height: 3 * 28px;
		background-color: #fff;
		box-shadow: 3px 3px 3px 0px rgba(0,0,0,.2);
		.account{
			position: relative;
			padding: 4px 8px;
			font-size: 14px;
			line-height: 20px;
			-webkit-transition:font-size .1s linear,background-color .1s linear;
			.removeBtn{
				position: absolute;
				z-index: 3;
				display: none;
				width: 16px;
				height: 16px;
				right: 6px;
				top: 50%;
				margin-top: -8px;
				font-size: 16px;
				text-align: center;
				line-height: 16px;
				color: #999;
				cursor: pointer;
				&:before{
					content: "×";
				}
			}
			&:hover{
				background-color: #3879d9;
				color: #fff;
				font-size: 18px;
				.removeBtn{
					display: block;
					color: #fff;
				}
			}
		}
	}
}