@charset "utf-8";
* {
  outline: 0;
  box-sizing: border-box;
  -webkit-user-select: none;
  -webkit-appearance: none;
  -webkit-tap-highlight-color: transparent;
  cursor: default;
}
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: transparent;
  right: 5px;
}
*::-webkit-scrollbar-track {
  background-color: transparent;
}
*::-webkit-scrollbar-thumb {
  background-color: rgba(145, 145, 155, 0);
  background-clip: content-box;
  border: 10px solid transparent;
  border-radius: 999px;
  -webkit-transition: .2s;
}
*:hover::-webkit-scrollbar-thumb {
  background-color: #91919b;
  border: 1px solid transparent;
}
body,
html {
  margin: 0;
  background-color: transparent;
  font-family: "ff-tisa-web-pro-1", "ff-tisa-web-pro-2", "Lucida Grande", "Hiragino Sans GB", "Hiragino Sans GB W3", "Microsoft YaHei";
  font-size: 12px;
  line-height: 1.5;
  color: #333333;
  height: 100%;
  min-height: 100%;
}
input,
textarea,
pre {
  font-family: "ff-tisa-web-pro-1", "ff-tisa-web-pro-2", "Lucida Grande", "Hiragino Sans GB", "Hiragino Sans GB W3", "Microsoft YaHei";
  resize: none;
  -webkit-user-select: auto;
}
img {
  -webkit-user-select: none;
}
ul,
li,
dl,
dt,
dd,
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
  list-style: none;
  display: block;
}
p {
  padding-top: 5px;
  padding-bottom: 10px;
}
a {
  color: #333333;
  cursor: pointer;
}
a {
  text-decoration: none;
}
a:hover {
  text-decoration: underline;
}
img {
  border: 0;
}
.radius-left {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.radius-right {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.show,
.block {
  display: block !important;
}
.hide,
.none {
  display: none;
}
.pull-left {
  float: left;
}
.pull-right {
  float: right !important;
}
.clearfix:before {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.clearfix:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.clearfix:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.row {
  margin-left: -10px;
  margin-right: -10px;
}
.row:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.row:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.col-20 {
  float: left;
  width: 20%;
  padding-left: 10px;
  padding-right: 10px;
}
.col-25 {
  float: left;
  width: 25%;
  padding-left: 10px;
  padding-right: 10px;
}
.col-40 {
  float: left;
  width: 40%;
  padding-left: 10px;
  padding-right: 10px;
}
.col-50 {
  float: left;
  width: 50%;
  padding-left: 10px;
  padding-right: 10px;
}
.col-100 {
  float: left;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
}
@-webkit-keyframes hideByRotate {
  0% {
    -webkit-transform: perspective(1000px) rotateY(0deg);
  }
  100% {
    -webkit-transform: perspective(1000px) rotateY(90deg);
  }
}
@-webkit-keyframes showByRotate {
  0% {
    -webkit-transform: perspective(1000px) rotateY(270deg);
  }
  100% {
    -webkit-transform: perspective(1000px) rotateY(360deg);
  }
}
.bold {
  font-weight: bold;
}
.text-size-14 {
  font-size: 14px;
}
.text.darkblue,
.text-darkblue {
  color: #365676;
}
.icon {
  position: relative;
  display: inline-block;
  width: 24px;
  height: 24px;
  top: 7px;
  margin-top: -12px;
  background-repeat: no-repeat;
  background-position: center center;
}
.icon.pencil {
  background-image: url(../images/icon_pencil.png);
}
.icon.checkmark {
  background-image: url(../images/icon_checkmark.png);
}
.icon.mark_right {
  background-image: url(../images/icon_mark_right.png);
}
.icon.clock_gray {
  background-image: url(../images/icon_clock_gray.png);
}
.icon.prize_gray {
  background-image: url(../images/icon_prize_gray.png);
}
@-webkit-keyframes iconload {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: -280px 0;
  }
}
.icon_load {
  width: 40px;
  height: 40px;
  background-image: url(../images/icon_loading.png);
  background-position: 0 0;
  opacity: .7;
  -webkit-animation: iconload 0.5s steps(7) infinite;
}
input[type="text"].form-ctrl,
input[type="password"].form-ctrl,
div.inputTips {
  padding: 5px;
  background-color: #fcfcfc;
  border: 1px solid transparent;
  overflow: hidden;
}
input[type="text"].form-ctrl,
input[type="password"].form-ctrl {
  border: 1px solid #eee;
}
input[type="text"].form-ctrl:focus,
input[type="password"].form-ctrl:focus {
  border: 1px solid #389be9;
}
input[type="text"].block,
input[type="password"].block,
.btn.block {
  display: block;
  width: 100%;
}
.inputTips {
  color: #bbb;
}
.inputTips.focus {
  color: #333;
}
.form-group .input-group {
  position: relative;
  margin-bottom: 15px;
}
.form-group .input-group input[type="password"].inputTips {
  position: absolute;
  left: 0;
  top: 0;
  background-color: transparent;
}
.form-group .input-group input[type="password"].inputTips.focus {
  background-color: #fcfcfc;
}
.form-group .input-group:after:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.form-group .input-group:after:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.form-group .input-group.tr {
  display: inline-table;
  vertical-align: middle;
  width: 100%;
}
.form-group .input-group.tr div.td {
  display: table-cell;
  vertical-align: middle;
}
.form-group .input-group.tr div.td input.first {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.form-group .input-group.tr div.td input.last {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.radio,
.checkbox {
  display: inline-block;
  font-weight: normal;
  cursor: pointer;
  overflow: hidden;
  background-image: url(../images/icon_radiobox.png);
  background-position: -4px -2px;
  background-repeat: no-repeat;
  padding-left: 20px;
}
.radio .icon,
.checkbox .icon {
  width: 16px;
  height: 16px;
  top: 3px;
  margin-top: -3px;
  margin-right: 5px;
}
.radio input[type="radio"],
.checkbox input[type="radio"],
.radio input[type="checkbox"],
.checkbox input[type="checkbox"] {
  float: right;
  position: relative;
  top: 0px;
  left: -9999px;
}
.checkbox {
  background-image: url(../images/icon_checkbox.png);
}
.radio.checked,
.checkbox.checked {
  background-position: -4px -26px;
}
.btn {
  display: inline-block;
  padding: 4px 10px;
  font-size: 12px;
  line-height: 12px;
  background-color: #fbfbfb;
  border: 1px solid #bbb;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
}
.btn.df {
  padding: 6px 12px;
  font-size: 12px;
  line-height: 12px;
}
.btn.lg {
  padding: 8px 16px;
  font-size: 14px;
  line-height: 14px;
}
.btn.darkblue {
  color: #fff;
  background-color: #4b5781;
  border-color: #4b5781;
}
.btn.darkblue:active {
  color: #fff;
  background-color: #414b6f;
  border-color: #414b6f;
}
.btn.gray {
  color: #fff;
  background-color: #ccc;
  border-color: #ccc;
}
a.btn:hover {
  text-decoration: none;
}
.btn-link {
  color: #8cbf4a;
}
.btn-link:hover {
  text-decoration: underline;
}
input[type="text"].editTag {
  border: 0;
  padding: 0;
  background-color: transparent;
}
input[type="text"].editTag.focus {
  color: #999;
  box-shadow: inset 3px 3px 10px rgba(0, 0, 0, 0.2);
}
.display-none {
  display: none;
}
.content {
  word-break: break-all;
  hyphens: auto;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
}
.pageContainer {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 10px;
}
.uiText {
  color: transparent !important;
}
.fz14 {
  font-size: 14px;
}
.text-primary {
  color: #71bd6a;
}
.text-gray {
  color: gray;
}
.text-center {
  text-align: center;
}
.btn[disabled="disabled"],
.btn[disabled="disabled"]:hover {
  color: #fff;
  background-color: #ccc;
}
.btn-lg {
  font-size: 16px;
}
.btn-default {
  background-color: #ffffff;
  color: #333333;
}
.btn-default:hover {
  background-color: #fcfcfc;
  color: #000000;
}
.btn-primary {
  background-color: #71bd6a;
  border: 0;
  color: #ffffff;
}
.btn-primary.hover,
.btn-primary:hover {
  background-color: #7cc276;
  color: #ffffff;
}
.btn-link {
  background-color: transparent;
  border: 0;
}
.btn-link:hover {
  background-color: transparent;
}
.btn-link.btn-primary {
  color: #71bd6a;
}
.btn-link.btn-primary:hover {
  color: #7cc276;
}
.btn-link.btn-danger {
  color: #dd3333;
}
.btn-link.btn-danger:hover {
  color: #cc0000;
}
.btn-primary-hover:hover {
  background-color: #7cc276;
  color: #ffffff;
}
.btn.btn-primary.disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
.table {
  display: table;
}
.table .tr {
  display: table-row;
}
.table .tr .th,
.table .tr .td {
  display: table-cell;
}
img.loading {
  width: 0;
  height: 0;
  border: 0;
  padding: 0;
  margin: 0;
}
img.loading:after {
  content: 'loading';
  display: inline-block;
  width: 100px;
  height: 100px;
  border: 1px solid #999;
}
img.loading.loadFailed:after {
  content: 'loadFailed';
}
.mainTopBar {
  height: 45px;
  border-bottom: 1px solid #ccc;
  font-size: 14px;
  text-align: center;
  line-height: 44px;
}
.bg_logo {
  background-image: url(../images/bg_logo.png);
  background-repeat: no-repeat;
  background-position: center center;
}
.rootWindow {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 900px;
  height: 620px;
  margin-left: -450px;
  margin-top: -310px;
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.4);
}
.rootWindow .rootRadius3 {
  border-top-right-radius: 3px;
}
.rootWindow .winMenu {
  position: absolute;
  width: 54px;
  height: 100%;
  background-color: #474c64;
  background-image: -webkit-linear-gradient(90deg, #41465e 0%, #4f556b 100%);
  border-radius: 3px 0px 0px 3px;
}
.rootWindow .winMenu .menuBtnBox {
  position: relative;
  margin-top: 30px;
}
.rootWindow .winMenu .menuBtnBox .btnMark {
  position: absolute;
  width: 100%;
  height: 50px;
  left: 0;
  top: 0px;
  background-image: url(../images/bg_rootmenu_btnmark.png);
  background-repeat: no-repeat;
  background-position: left center;
}
.rootWindow .winMenu .menuBtnBox .menuBtnList {
  position: absolute;
  width: 50px;
  height: 50px;
  left: 3px;
  background-image: url(../images/icon_rootmenu.png);
  opacity: .6;
  filter: alpha(opacity=60);
  -webkit-transition: .3s;
  cursor: pointer;
}
.rootWindow .winMenu .menuBtnBox .menuBtnList.checked,
.rootWindow .winMenu .menuBtnBox .menuBtnList:hover {
  opacity: 1;
  filter: alpha(opacity=100);
}
.rootWindow .winMenu .menuBtnBox .menuBtnList.chat {
  top: 0px;
  background-position: 0px 0px;
}
.rootWindow .winMenu .menuBtnBox .menuBtnList.contacts {
  top: 70px;
  background-position: 0px -50px;
}
.rootWindow .winMenu .menuBtnBox .menuBtnList.course {
  top: 140px;
  background-position: 0px -100px;
}
.rootWindow .winMenu .menuBtnBox .menuBtnList.cloud {
  top: 210px;
  background-position: 0px -150px;
}
.rootWindow .winMain {
  position: absolute;
  height: 100%;
  left: 54px;
  right: 0;
  background-color: #f7f7f7;
  border-radius: 0 3px 3px 0;
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.rootWindow .winMain .winMainLeft {
  -webkit-user-select: none;
  position: absolute;
  height: 100%;
  left: 0;
  top: 0;
  background-color: #f7f7f7;
}
.rootWindow .winMain .winMainLeft .winMainLeftTop {
  position: absolute;
  width: 100%;
  height: 45px;
  left: 0;
  top: 0;
  border-bottom: 1px solid #ccc;
}
.rootWindow .winMain .winMainLeft .winMainLeftMain {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
  bottom: 0px;
  border-right: 1px solid #ccc;
}
.rootWindow .winMain .winMainLeft .winMainLeftTop + .winMainLeftMain {
  top: 45px;
}
.rootWindow .winMain .winMainContainer {
  position: absolute;
  height: 100%;
  left: 221px;
  right: 0;
  top: 0;
  background-color: #fefefe;
  border-radius: 0px 3px 3px 0px;
}
.rootWindow .winMain .winMainContainer .chatWindow {
  border-radius: 0px 3px 3px 0px;
}
.clearafter:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.contactsSearch {
  padding-top: 10px;
}
.contactsSearch:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.contactsSearch:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.contactsSearch .searchInput {
  display: block;
  float: left;
  width: 100%;
  padding: 4px;
  padding-left: 20px;
  background-color: transparent;
  background-image: url(../images/icon_search.png);
  background-position: left center;
  background-repeat: no-repeat;
  border: 0;
  border-bottom: 1px solid #ddd;
}
.contactsSearch .inputClear {
  position: absolute;
  width: 20px;
  height: 20px;
  background-image: url(../images/icon_inputclear.png);
  background-repeat: no-repeat;
  background-position: center center;
  border-radius: 50%;
  cursor: pointer;
}
.contactsSearch .addBtn {
  display: block;
  float: right;
  width: 26px;
  height: 26px;
  background-color: transparent;
  background-image: url(../images/icon_add.png);
  background-position: center center;
  background-repeat: no-repeat;
  border: 0;
  cursor: pointer;
}
.contactsSearch .resultBox {
  position: absolute;
  overflow-y: auto;
  z-index: 1;
  max-height: 570px;
  left: 1px;
  right: 1px;
  top: 45px;
  background-color: #fff;
  box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.3);
}
.contactsSearch .resultBox .resultList {
  position: relative;
  min-height: 58px;
  cursor: pointer;
}
.contactsSearch .resultBox .resultList:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.contactsSearch .resultBox .resultList:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.contactsSearch .resultBox .resultList .resultIcon {
  position: absolute;
  left: 8px;
  top: 8px;
}
.contactsSearch .resultBox .resultList .resultIcon img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.contactsSearch .resultBox .resultList .resultInfo {
  margin-left: 56px;
  padding-top: 8px;
  padding-right: 8px;
}
.contactsSearch .resultBox .resultList .resultInfo .title {
  float: left;
  line-height: 20px;
}
.contactsSearch .resultBox .resultList .resultInfo .id {
  float: right;
  color: #aaa;
  line-height: 20px;
}
.contactsSearch .resultBox .resultList .resultInfo .tips {
  clear: both;
  color: #999;
}
.contactsSearch .resultBox .resultList:hover {
  background-color: #fcfcfc;
}
.contactsSearch .resultBox .resultList.checked {
  background-color: #f6f6f6;
}
.contactsList {
  position: relative;
  min-height: 58px;
  border-bottom: 1px solid #ddd;
  cursor: pointer;
}
.contactsList:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.contactsList:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.contactsList .contactsIcon {
  position: absolute;
  left: 8px;
  top: 8px;
}
.contactsList .contactsIcon img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.contactsList .contactsIcon .redNum {
  margin-top: 3px;
  margin-right: 3px;
}
.contactsList .contactsInfo {
  margin-left: 56px;
  padding-top: 8px;
  padding-right: 8px;
}
.contactsList .contactsInfo .title {
  line-height: 20px;
  max-width: 90px;
  height: 20px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.contactsList .contactsInfo .dateMark {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #aaa;
  line-height: 20px;
}
.contactsList .contactsInfo .tips {
  clear: both;
  color: #999;
  height: 18px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.contactsList:hover {
  background-color: #eee;
}
.contactsList.checked {
  background-color: #ddd;
}
.tabBox {
  background-color: #404148;
  border-radius: 2px;
  border: 1px solid #45464d;
}
.tabBox .tabBar {
  position: absolute;
  display: table;
  width: 100%;
  height: 40px;
  left: 0;
  top: 0;
}
.tabBox .tabBar .tab {
  display: table-cell;
  padding: 10px;
  background-color: #34363b;
  font-size: 14px;
  line-height: 14px;
  color: #74767d;
  text-align: center;
  cursor: pointer;
}
.tabBox .tabBar .tab.checked {
  background-color: transparent;
  color: #7cc276;
}
.tabBox .tabCon {
  position: absolute;
  left: 0;
  top: 40px;
  right: 0;
  bottom: 0;
}
.tabBox .tabCon .con {
  display: none;
}
.tabBox .tabCon .con.checked {
  display: block;
}
.chatWindow {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
.chatWindow .winBtns {
  position: absolute;
  height: 20px;
  top: 7px;
  right: 7px;
}
.chatWindow .winBtns .winBtn {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 5px;
  background-image: url(../images/icon_winbtns.png);
  background-position: 0 0;
  cursor: pointer;
}
.chatWindow .winBtns .winBtn:hover {
  background-position-x: -20px;
}
.chatWindow .winBtns .winBtn.close {
  background-position-y: 0;
}
.chatWindow .winBtns .winBtn.maximize {
  background-position-y: -20px;
}
.chatWindow .winBtns .winBtn.minimize {
  background-position-y: -40px;
}
.chatWindow .chatContainer {
  position: absolute;
  left: 0;
  top: 45px;
  right: 0;
  bottom: 90px;
  padding: 10px;
  overflow: auto;
}
.chatWindow .chatContainer .chatDate {
  text-align: center;
  color: #bbb;
  margin: 10px 20px;
}
.chatWindow .chatContainer .chatMsgTips {
  display: inline-block;
  padding: 4px;
  margin-left: 78px;
  margin-top: 10px;
  background-color: #ddd;
  border-radius: 3px;
}
.chatWindow .chatContainer .chatBubble {
  position: relative;
  min-height: 40px;
}
.chatWindow .chatContainer .chatBubble:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.chatWindow .chatContainer .chatBubble:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.chatWindow .chatContainer .chatBubble .userInfo {
  position: absolute;
  -webkit-transform-origin: center center;
  -webkit-animation: showByScale 0.5s 0s ease both;
}
.chatWindow .chatContainer .chatBubble .userInfo .headImg {
  width: 42px;
  height: 42px;
  border: 1px solid #999;
  border-radius: 50%;
  overflow: hidden;
}
.chatWindow .chatContainer .chatBubble .userInfo .headImg img {
  display: block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}
.chatWindow .chatContainer .chatBubble .userInfo .nickname {
  white-space: nowrap;
}
.chatWindow .chatContainer .chatBubble .chatInfo:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.chatWindow .chatContainer .chatBubble .chatInfo:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox {
  position: relative;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .conBox {
  position: relative;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .textBox {
  -webkit-user-select: auto;
  padding: 0 5px;
  word-break: break-all;
  line-height: 1.8;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .textBox:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .textBox:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .textBox a {
  text-decoration: underline;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .textBox img {
  display: inline-block;
  max-width: 100%;
  max-height: 200px;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .textBox img.emoji {
  display: inline-block;
  max-width: 20px;
  max-height: 20px;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .imgBox img {
  display: block;
  max-width: 200px;
  max-height: 200px;
  border-radius: 11px;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .nickname {
  display: none;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .sendMode {
  position: absolute;
  width: 16px;
  height: 16px;
  background-position: center center;
  background-repeat: no-repeat;
  bottom: -16px;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .sendMode.error {
  background-image: url(../images/icon_mark_excited.png);
  background-color: #f94d30;
  border-radius: 50%;
  cursor: pointer;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .sendMode.sending {
  background-image: url(../images/icon_chatsending.png);
  -webkit-animation: hoverByRotate 0.5s 0s linear infinite;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .classroom {
  position: relative;
  width: 320px;
  min-height: 102px;
  background-color: #fefefe;
  border-radius: 3px;
  border: 1px solid #ddd;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.1);
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .classroom .ownerIcon {
  position: absolute;
  left: 10px;
  top: 15px;
  border: 1px solid #ddd;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .classroom .ownerIcon img {
  width: 68px;
  height: 68px;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .classroom .classInfo {
  margin-left: 90px;
  padding-top: 15px;
  padding-right: 15px;
  min-height: 90px;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .classroom .classInfo .classTitle {
  font-size: 14px;
  line-height: 20px;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .classroom .classInfo .classTips .date {
  color: #aaa;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .classroom .btnBox .btn {
  display: block;
  padding: 8px;
  margin-top: 6px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  font-size: 14px;
}
.chatWindow .chatContainer .chatBubble .chatInfo .infoBox .classroom:hover {
  background-color: #fcfcfc;
}
.chatWindow .chatContainer .chatBubble.other .userInfo {
  left: 10px;
}
.chatWindow .chatContainer .chatBubble.other .chatInfo {
  margin-left: 50px;
  margin-right: 80px;
}
.chatWindow .chatContainer .chatBubble.other .chatInfo .infoBox {
  float: left;
  border-top: 16px solid transparent;
  border-right: 16px solid transparent;
  border-bottom: 20px solid transparent;
  border-left: 28px solid transparent;
  border-image: url(../images/dialog_info_bg_other.png) fill 16 16 20 28 stretch;
  -webkit-transform-origin: bottom left;
  -webkit-animation: showByScale 0.3s 0.2s ease both;
}
.chatWindow .chatContainer .chatBubble.other .chatInfo .infoBox .textBox {
  margin: -5px -5px -9px -7px;
}
.chatWindow .chatContainer .chatBubble.other .chatInfo .infoBox .imgBox {
  margin: -10px -10px -14px -12px;
}
.chatWindow .chatContainer .chatBubble.other .chatInfo .infoBox .sendMode {
  right: -40px;
}
.chatWindow .chatContainer .chatBubble.other.special .chatInfo .infoBox {
  border-image: url(../images/dialog_info_bg_other_special.png) fill 16 16 20 28 stretch;
}
.chatWindow .chatContainer .chatBubble.self .userInfo {
  right: 10px;
}
.chatWindow .chatContainer .chatBubble.self .chatInfo {
  margin-left: 80px;
  margin-right: 50px;
}
.chatWindow .chatContainer .chatBubble.self .chatInfo .infoBox {
  float: right;
  border-top: 16px solid transparent;
  border-right: 28px solid transparent;
  border-bottom: 20px solid transparent;
  border-left: 16px solid transparent;
  border-image: url(../images/dialog_info_bg__self.png) fill 16 28 20 16 stretch;
  -webkit-transform-origin: bottom right;
  -webkit-animation: showByScale 0.3s 0.2s ease both;
}
.chatWindow .chatContainer .chatBubble.self .chatInfo .infoBox .textBox {
  margin: -5px -7px -9px -5px;
}
.chatWindow .chatContainer .chatBubble.self .chatInfo .infoBox .imgBox {
  margin: -10px -12px -14px -10px;
}
.chatWindow .chatContainer .chatBubble.self .chatInfo .infoBox .sendMode {
  left: -40px;
}
.chatWindow .chatContainer .chatBubble.type-html .chatInfo .infoBox {
  border-image: none;
}
.chatWindow .chatContainer .chatBubble.type-html.self .chatInfo .infoBox {
  padding-right: 20px;
}
.chatWindow .chatContainer .chatBubble.type-html.other .chatInfo .infoBox {
  padding-left: 20px;
}
.chatWindow.full .chatContainer {
  bottom: 160px;
}
.chatWindow.full .chatContainer .chatBubble {
  margin-bottom: 60px;
}
.chatWindow.full .chatContainer .chatBubble .userInfo {
  bottom: 10px;
}
.chatWindow.full .chatContainer .chatBubble .userInfo .nickname {
  position: absolute;
  bottom: -30px;
}
.chatWindow.full .chatContainer .chatBubble.other .userInfo .nickname {
  left: 0;
}
.chatWindow.full .chatContainer .chatBubble.self .userInfo .nickname {
  right: 0;
}
.chatWindow.full .chatEditor {
  height: 160px;
}
.chatWindow.full .chatEditor .textAreaBox {
  height: 120px;
}
.chatWindow.full .chatEditor .textAreaBox .chatWrite {
  height: 100%;
}
.chatWindow.inClass .chatTitle {
  display: none;
}
.chatWindow.inClass .chatContainer {
  top: 0px;
  bottom: 160px;
}
.chatWindow.inClass .chatContainer .chatBubble {
  position: relative;
  min-height: 0px;
  padding-top: 30px;
}
.chatWindow.inClass .chatContainer .chatBubble .userInfo {
  position: absolute;
  z-index: 1;
  width: auto;
  height: auto;
  top: 5px;
  bottom: auto;
}
.chatWindow.inClass .chatContainer .chatBubble .userInfo .headImg {
  display: none;
}
.chatWindow.inClass .chatContainer .chatBubble .userInfo .nickname {
  display: block;
  color: #eee;
}
.chatWindow.inClass .chatContainer .chatBubble .chatInfo .infoBox {
  padding: 10px;
  border-image: none;
  border-width: 0;
  border-radius: 8px;
}
.chatWindow.inClass .chatContainer .chatBubble .chatInfo .infoBox .textBox {
  -webkit-user-select: auto;
  padding: 0px;
  word-break: break-all;
  line-height: 1.8;
}
.chatWindow.inClass .chatContainer .chatBubble .chatInfo .infoBox .textBox a {
  text-decoration: underline;
}
.chatWindow.inClass .chatContainer .chatBubble .chatInfo .infoBox .imgBox img {
  display: block;
  border-radius: 6px;
}
.chatWindow.inClass .chatContainer .chatBubble .chatInfo .infoBox .sendMode {
  position: absolute;
  width: 16px;
  height: 16px;
  background-position: center center;
  background-repeat: no-repeat;
  bottom: -16px;
}
.chatWindow.inClass .chatContainer .chatBubble .chatInfo .infoBox .sendMode.error {
  background-image: url(../images/icon_mark_excited.png);
  background-color: #f94d30;
  border-radius: 50%;
  cursor: pointer;
}
.chatWindow.inClass .chatContainer .chatBubble .chatInfo .infoBox .sendMode.sending {
  background-image: url(../images/icon_chatsending.png);
  -webkit-animation: hoverByRotate 0.5s 0s linear infinite;
}
.chatWindow.inClass .chatContainer .chatBubble.msgMerge {
  padding-top: 0;
  margin-top: 5px;
}
.chatWindow.inClass .chatContainer .chatBubble.msgMerge .userInfo .nickname {
  display: none;
}
.chatWindow.inClass .chatContainer .chatBubble.other {
  color: #eee;
}
.chatWindow.inClass .chatContainer .chatBubble.other .userInfo {
  left: 10px;
}
.chatWindow.inClass .chatContainer .chatBubble.other .chatInfo {
  margin-left: 10px;
  margin-right: 40px;
}
.chatWindow.inClass .chatContainer .chatBubble.other .chatInfo .nickname {
  color: #777;
}
.chatWindow.inClass .chatContainer .chatBubble.other .chatInfo .infoBox {
  padding-left: 10px;
  background-color: #4a4b53;
  -webkit-transform-origin: bottom left;
  -webkit-animation: showByScale 0.3s 0.2s ease both;
}
.chatWindow.inClass .chatContainer .chatBubble.other .chatInfo .infoBox .textBox {
  margin: 0;
}
.chatWindow.inClass .chatContainer .chatBubble.other .chatInfo .infoBox .imgBox {
  margin: -6px;
}
.chatWindow.inClass .chatContainer .chatBubble.other .chatInfo .infoBox .sendMode {
  right: -40px;
}
.chatWindow.inClass .chatContainer .chatBubble.other.special .chatInfo .infoBox {
  border-image: url(../images/dialog_info_bg_other_special.png) fill 16 16 20 28 stretch;
}
.chatWindow.inClass .chatContainer .chatBubble.self .userInfo {
  right: 10px;
}
.chatWindow.inClass .chatContainer .chatBubble.self .chatInfo {
  margin-left: 40px;
  margin-right: 10px;
}
.chatWindow.inClass .chatContainer .chatBubble.self .chatInfo .infoBox {
  background-color: #76b571;
  -webkit-transform-origin: bottom right;
  -webkit-animation: showByScale 0.3s 0.2s ease both;
}
.chatWindow.inClass .chatContainer .chatBubble.self .chatInfo .infoBox .textBox {
  margin: 0;
}
.chatWindow.inClass .chatContainer .chatBubble.self .chatInfo .infoBox .imgBox {
  margin: -6px;
}
.chatWindow.inClass .chatContainer .chatBubble.self .chatInfo .infoBox .sendMode {
  left: -40px;
}
.chatWindow.inClass .chatEditor {
  height: 160px;
}
.chatWindow.inClass .chatEditor .textAreaBox {
  height: 120px;
}
.chatWindow.inClass .chatEditor .textAreaBox .chatWrite {
  height: 100%;
}
.chatWindow .chatNoticeBar {
  position: absolute;
  width: 100%;
  left: 0;
  top: 44px;
}
.chatWindow .chatNoticeBar .noticeAlert {
  line-height: 36px;
}
.chatWindow .chatNoticeBar .noticeAlert:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.chatWindow .chatNoticeBar .noticeAlert:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.chatWindow .chatNoticeBar .noticeAlert .noticeText {
  float: left;
  padding-left: 10px;
}
.chatWindow .chatNoticeBar .noticeAlert .noticeBtn {
  float: right;
  padding: 0 20px;
  cursor: pointer;
}
.chatWindow .chatNoticeBar .noticeAlert.green {
  background-color: #96ce91;
}
.chatWindow .chatNoticeBar .noticeAlert.green .noticeBtn {
  background-color: #6cb367;
  color: #fff;
}
.chatTopBar {
  background-color: #f7f7f7;
  border-bottom: 1px solid #ccc;
  color: #777;
  line-height: 24px;
  font-weight: bold;
  text-align: center;
}
.chatTopBar input[type="text"].editTag {
  text-align: center;
  line-height: 24px;
  border-radius: 100px;
}
.chatTopBar .title {
  padding: 10px;
}
.chatTopBar .titleText {
  cursor: pointer;
}
.chatTopBar .titleBtn {
  display: inline-block;
  display: none;
  cursor: pointer;
  -webkit-transition: .2s;
}
.chatTopBar .titleBtn.checked {
  -webkit-transform: rotate(180deg);
}
.chatMemberBox {
  position: absolute;
  display: none;
  left: 0px;
  right: 0px;
  top: 44px;
  bottom: 160px;
  z-index: 1;
}
.chatMemberBox:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.chatMemberBox:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.chatMemberBox .toolsBox {
  position: absolute;
  right: 10px;
  top: 10px;
  width: 60px;
  padding-top: 10px;
}
.chatMemberBox .memberListBox {
  display: none;
  position: absolute;
  max-height: 100%;
  padding: 0px;
  padding-right: 60px;
  left: 0px;
  top: 0px;
  right: 0px;
  background-color: #f7f7f7;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.2);
  overflow-y: auto;
}
.chatMemberBox .memberListBox:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.chatMemberBox .memberListBox:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.chatMemberBox .memberListBox .memberList {
  float: left;
  position: relative;
  width: 70px;
  height: 90px;
  padding: 5px;
  cursor: pointer;
}
.chatMemberBox .memberListBox .memberList .headImg,
.chatMemberBox .memberListBox .memberList .headImg img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin: 0 auto;
}
.chatMemberBox .memberListBox .memberList .userName {
  width: 100%;
  height: 20px;
  line-height: 20px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
}
.chatMemberBox .memberListBox .memberList .deleThis {
  position: absolute;
  width: 16px;
  height: 16px;
  right: 12px;
  top: 2px;
  background-color: #fd451a;
  border-radius: 50%;
  font-size: 16px;
  line-height: 14px;
  text-align: center;
  cursor: pointer;
}
.chatMemberBox .memberListBox .memberList .deleThis:before {
  content: '-';
  color: #fff;
}
.chatMemberBox .memberListBox .memberAdd {
  float: left;
  width: 70px;
  padding-top: 5px;
  cursor: pointer;
}
.chatMemberBox .memberListBox .memberAdd .headImg {
  width: 40px;
  height: 40px;
  margin: 0 auto;
  background-color: #eee;
  border: 2px dashed #ccc;
  border-radius: 50%;
  font-size: 30px;
  line-height: 36px;
  color: #aaa;
  text-align: center;
}
.chatMemberBox .memberListBox .memberAdd .userName {
  line-height: 20px;
  text-align: center;
}
.chatEditor {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  padding-bottom: 15px;
  box-shadow: 0px -1px 0px #cccccc;
}
.chatEditor .chatNotice {
  display: none;
  padding-left: 5px;
  background-color: #ddd;
  line-height: 24px;
}
.chatEditor .toolsBox {
  height: 28px;
  padding-left: 15px;
  padding-top: 4px;
}
.chatEditor .toolsBox .toolsBtn {
  float: left;
  width: 20px;
  height: 20px;
  margin-right: 15px;
  background-image: url(../images/icon_dialog_tools_black.png);
  background-repeat: no-repeat;
  cursor: pointer;
}
.chatEditor .toolsBox .toolsBtn:hover {
  background-position-x: -40px;
}
.chatEditor .toolsBox .toolsBtn.disabled {
  opacity: .3;
}
.chatEditor .toolsBox .toolsBtn.arrow {
  background-position-y: 0px;
}
.chatEditor .toolsBox .toolsBtn.moodFace {
  background-position-y: -20px;
}
.chatEditor .toolsBox .toolsBtn.screenshot {
  width: 30px;
  background-position-y: -40px;
}
.chatEditor .toolsBox .toolsBtn.classroom {
  background-position-y: -60px;
}
.chatEditor .textAreaBox {
  position: relative;
  margin: 0 10px;
  background-color: #fff;
}
.chatEditor .textAreaBox .chatWrite {
  display: block;
  width: 100%;
  white-space: pre-wrap;
  user-select: auto;
  padding: 2px;
  border: 0;
  font-size: 12px;
  line-height: 18px;
  overflow: auto;
  -webkit-user-select: auto;
}
.chatEditor .textAreaBox .chatWrite img {
  display: inline-block;
  max-width: 200px;
  max-height: 88px;
  vertical-align: middle;
  zoom: 1;
}
.chatEditor .textAreaBox .chatWrite img.emoji {
  height: 18px;
}
.chatEditor .textAreaBox .chatWrite * {
  -webkit-user-select: auto;
  user-select: auto;
}
.chatEditor .textAreaBox .sendBtn {
  position: absolute;
  display: none;
  width: 48px;
  height: 20px;
  right: 2px;
  bottom: 2px;
  background-color: #fff;
  border: 0;
  outline: 2px solid #62c479;
}
.chatEditor .textAreaBox:hover .sendBtn {
  display: block;
}
.black .chatEditor {
  box-shadow: 0px -1px 0px #33343a;
}
.black .chatEditor .textAreaBox .chatWrite {
  background-color: #61636b;
  color: #eee;
}
.black .chatEditor .textAreaBox .chatWrite.focus,
.black .chatEditor .textAreaBox .chatWrite:focus {
  background-color: #eee;
  color: #61636b;
}
.searchBar {
  padding: 15px 45px;
}
.searchBar .searchInput {
  display: block;
  width: 100%;
  padding: 6px 6px 6px 27px;
  background-color: #4a4b53;
  background-image: url(../images/icon_search_black.png);
  background-repeat: no-repeat;
  background-position: 7px center;
  border: 0;
  border-radius: 30px;
  font-size: 12px;
  line-height: 12px;
  -webkit-transition: .3s;
}
.searchBar .searchInput:focus {
  background-color: #eee;
}
.qnaWindow .qnaTitle {
  padding: 10px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ccc;
  color: #777;
  line-height: 24px;
  font-weight: bold;
  text-align: center;
}
.qnaWindow .qnaTitle input[type="text"].editTag {
  text-align: center;
  line-height: 24px;
  border-radius: 100px;
}
.qnaWindow .qnaTitle .titleBtn {
  position: absolute;
  display: block;
  width: 20px;
  height: 20px;
  left: 14px;
  top: 14px;
  line-height: 16px;
  text-align: center;
  cursor: pointer;
  -webkit-transition: .2s;
}
.qnaWindow .qnaTitle .titleBtn.checked {
  -webkit-transform: rotate(180deg);
}
.qnaWindow .qnaContainer {
  position: absolute;
  left: 0;
  top: 60px;
  right: 0;
  bottom: 160px;
  padding: 0 10px;
  overflow: auto;
}
.qnaWindow .qnaContainer .bubble {
  position: relative;
  margin-bottom: 18px;
  background-color: #4a4b53;
  border-radius: 8px;
}
.qnaWindow .qnaContainer .bubble:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.qnaWindow .qnaContainer .bubble:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.qnaWindow .qnaContainer .bubble .bubbleContent {
  color: #eee;
  padding: 20px 10px;
  border-bottom: 1px solid #404148;
  word-break: break-all;
}
.qnaWindow .qnaContainer .bubble .bubbleContent .textBox {
  -webkit-user-select: auto;
}
.qnaWindow .qnaContainer .bubble .bubbleContent .textBox img {
  max-width: 100%;
  max-height: 300px;
}
.qnaWindow .qnaContainer .bubble .bubbleInfo {
  height: 30px;
  color: #787a83;
}
.qnaWindow .qnaContainer .bubble .bubbleInfo .left {
  float: left;
}
.qnaWindow .qnaContainer .bubble .bubbleInfo .right {
  float: right;
}
.qnaWindow .qnaContainer .bubble .bubbleInfo .asker,
.qnaWindow .qnaContainer .bubble .bubbleInfo .likeNum {
  float: left;
  padding: 0 12px;
  line-height: 30px;
}
.qnaWindow .qnaContainer .bubble .bubbleInfo .likeBtn {
  float: left;
  width: 70px;
  height: 30px;
  background-color: #71bd6a;
  background-image: url(../images/icon_like.png);
  background-repeat: no-repeat;
  background-position: center center;
  border-bottom-right-radius: 8px;
  color: #393939;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
}
.qnaWindow .qnaContainer .bubble .bubbleInfo .likeBtn:hover {
  background-color: #7cc276;
}
.qnaWindow .qnaContainer .bubble .bubbleInfo .likeBtn.answer {
  background-image: none;
}
.qnaWindow .qnaContainer .bubble.liked .bubbleInfo .likeBtn {
  background-color: #585962;
  cursor: not-allowed;
}
.qnaWindow .chatEditor {
  height: 160px;
}
.qnaWindow .chatEditor .textAreaBox {
  height: 120px;
}
.qnaWindow .chatEditor .textAreaBox .chatWrite {
  height: 100%;
}
.classroomMemberWindow {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  overflow: auto;
}
.classroomMemberWindow .memberListTitle {
  height: 40px;
  padding: 0px 12px;
  color: #777;
  line-height: 40px;
}
.classroomMemberWindow .memberListBox .memberList {
  position: relative;
  height: 41px;
  padding: 0 12px;
  border-bottom: 1px solid #393a41;
  cursor: default;
}
.classroomMemberWindow .memberListBox .memberList .memberName {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding-left: 3px;
  color: #eee;
  line-height: 40px;
}
.classroomMemberWindow .memberListBox .memberList .prize {
  position: absolute;
  width: 24px;
  height: 24px;
  top: 50%;
  margin-left: 30px;
  margin-top: -12px;
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
  width: auto;
  right: 20px;
  padding-left: 20px;
  background-image: url(../images/icon_prize.png);
  background-position: left center;
  color: #eee;
  line-height: 20px;
}
.classroomMemberWindow .memberListBox .memberList .getOut {
  position: absolute;
  width: 24px;
  height: 24px;
  top: 50%;
  margin-left: 30px;
  margin-top: -12px;
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
  right: 80px;
  background-image: url(../images/icon_getout.png);
}
.classroomMemberWindow .memberListBox .memberList .showUp {
  position: absolute;
  width: 24px;
  height: 24px;
  top: 50%;
  margin-left: 30px;
  margin-top: -12px;
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
  right: 110px;
  background-image: url(../images/icon_showup.png);
}
.classroomMemberWindow .memberListBox .memberList .showUp.disabled {
  background-image: url(../images/icon_showup_disabled.png);
}
.classroomMemberWindow .memberListBox .memberList .microphone {
  position: absolute;
  width: 24px;
  height: 24px;
  top: 50%;
  margin-left: 30px;
  margin-top: -12px;
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
  right: 140px;
  background-image: url(../images/icon_microphone.png);
}
.classroomMemberWindow .memberListBox .memberList .microphone.disabled {
  background-image: url(../images/icon_microphone_disabled.png);
}
.classroomMemberWindow .memberListBox .memberList .iconAuthorize {
  position: absolute;
  width: 24px;
  height: 24px;
  top: 50%;
  margin-left: 30px;
  margin-top: -12px;
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
  right: 170px;
  background-image: url(../images/icon_authorize.png);
}
.classroomMemberWindow .memberListBox .memberList .iconAuthorize.disabled {
  background-image: url(../images/icon_authorize_disabled.png);
}
.classroomMemberWindow .memberListBox .memberList .raiseHand {
  position: absolute;
  width: 24px;
  height: 24px;
  top: 50%;
  margin-left: 30px;
  margin-top: -12px;
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
  right: 200px;
  background-image: url(../images/icon_raisehand.png);
  cursor: default;
}
.classroomMemberWindow .memberListBox .memberList .raiseHand.disabled {
  background-image: url(../images/icon_raisehand_disabled.png);
}
.classroomMemberWindow .memberListBox .memberList:hover {
  background-color: #34363b;
}
.classroomMemberWindow .memberListBox .memberList .btnMode {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  box-shadow: 1px 1px 3px 0px rgba(255, 255, 255, 0.05);
}
.classroomMemberWindow .memberListBox.assistant .memberList .memberName:after {
  content: '（助教）';
}
.classroomMemberWindow .memberListBox.member .memberList .memberName {
  margin-right: 220px;
}
.chatShow_headimg {
  -webkit-transform-origin: center center;
  -webkit-animation: showByScale 0.5s 0s ease both;
}
.chatShow_self_info {
  -webkit-transform-origin: bottom right;
  -webkit-animation: showByScale 0.3s 0.2s ease both;
}
.chatShow_other_info {
  -webkit-transform-origin: bottom left;
  -webkit-animation: showByScale 0.3s 0.2s ease both;
}
.noticeListBox {
  position: absolute;
  width: 100%;
  height: 100%;
  padding-bottom: 10px;
  overflow-x: hidden;
  overflow-y: auto;
}
.noticeListBox .noticeList {
  position: relative;
  min-height: 58px;
  border-bottom: 1px solid #ddd;
  cursor: pointer;
}
.noticeListBox .noticeList:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.noticeListBox .noticeList:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.noticeListBox .noticeList .noticeIcon {
  position: absolute;
  left: 8px;
  top: 8px;
}
.noticeListBox .noticeList .noticeIcon img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.noticeListBox .noticeList .noticeIcon .redBallNum {
  margin-top: 3px;
  margin-right: 3px;
}
.noticeListBox .noticeList .noticeInfo {
  margin-left: 56px;
  padding-top: 8px;
  padding-right: 8px;
}
.noticeListBox .noticeList .noticeInfo .noticeTitle {
  float: left;
  line-height: 20px;
}
.noticeListBox .noticeList .noticeInfo .noticeDate {
  float: right;
  color: #aaa;
  line-height: 20px;
}
.noticeListBox .noticeList .noticeInfo .noticeTips {
  clear: both;
  color: #999;
}
.noticeListBox .noticeList:hover {
  background-color: #eee;
}
.noticeListBox .noticeList.checked {
  background-color: #ddd;
}
.noticeListBox .searchResult {
  position: absolute;
  max-height: 100%;
  left: 10px;
  right: 10px;
  top: 0;
  overflow-y: auto;
  background-color: #fff;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.5);
}
.noticeListBox .searchResult .searchResultListBox .noticeSearchList {
  position: relative;
  min-height: 58px;
  border-bottom: 1px solid #ddd;
  cursor: pointer;
}
.noticeListBox .searchResult .searchResultListBox .noticeSearchList:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.noticeListBox .searchResult .searchResultListBox .noticeSearchList:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.noticeListBox .searchResult .searchResultListBox .noticeSearchList .noticeIcon {
  position: absolute;
  left: 8px;
  top: 8px;
}
.noticeListBox .searchResult .searchResultListBox .noticeSearchList .noticeIcon img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.noticeListBox .searchResult .searchResultListBox .noticeSearchList .noticeIcon .redBallNum {
  margin-top: 3px;
  margin-right: 3px;
}
.noticeListBox .searchResult .searchResultListBox .noticeSearchList .noticeInfo {
  margin-left: 56px;
  padding-top: 8px;
  padding-right: 8px;
}
.noticeListBox .searchResult .searchResultListBox .noticeSearchList .noticeInfo .noticeTitle {
  float: left;
  line-height: 20px;
}
.noticeListBox .searchResult .searchResultListBox .noticeSearchList .noticeInfo .noticeDate {
  float: right;
  color: #aaa;
  line-height: 20px;
}
.noticeListBox .searchResult .searchResultListBox .noticeSearchList .noticeInfo .noticeTips {
  clear: both;
  color: #999;
}
.noticeListBox .searchResult .searchResultListBox .noticeSearchList:hover {
  background-color: #eee;
}
.noticeListBox .searchResult .searchResultListBox .noticeSearchList.checked {
  background-color: #ddd;
}
.noticeListBox:before {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.courseList {
  position: relative;
  overflow: hidden;
  width: 320px;
  min-height: 102px;
  margin-left: 10px;
  margin-top: 10px;
  background-color: #fefefe;
  border-radius: 3px;
  border: 1px solid #ddd;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.1);
}
.courseList .courseInfo {
  position: relative;
  padding: 15px 15px 15px 90px;
  min-height: 90px;
}
.courseList .courseInfo .courseIcon {
  position: absolute;
  left: 10px;
  top: 50%;
  margin-top: -35px;
  border: 1px solid #ddd;
}
.courseList .courseInfo .courseIcon img {
  width: 68px;
  height: 68px;
}
.courseList .courseInfo .courseIcon .redBallNum {
  margin-top: 3px;
  margin-right: 3px;
}
.courseList .courseInfo .courseTitle {
  margin-top: 12px;
  font-size: 14px;
  line-height: 20px;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.courseList .courseInfo .courseProgress {
  display: table;
  width: 100%;
}
.courseList .courseInfo .courseProgress .name {
  display: table-cell;
  width: 60px;
  line-height: 32px;
}
.courseList .courseInfo .courseProgress .bar {
  position: relative;
  height: 3px;
  top: -3px;
  background-color: #cbcbcb;
}
.courseList .courseInfo .courseProgress .bar .completed {
  width: 50%;
  height: 3px;
  background-color: #71bd6a;
}
.courseList .courseInfo .courseProgress .num {
  display: table-cell;
  width: 50px;
  text-align: right;
}
.courseList .lesson {
  position: relative;
  padding: 10px;
  background-color: #f9f9f9;
  border-top: 1px solid #dcdcdc;
}
.courseList .lesson .lessonInfo {
  margin-right: 150px;
}
.courseList .lesson .lessonInfo .lessonTitle {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.courseList .lesson .lessonInfo .timeRange {
  color: #999;
}
.courseList .lesson .btnBox {
  position: absolute;
  top: 50%;
  right: 12px;
  margin-top: -16px;
}
.courseList .lesson .btnBox .btn {
  display: block;
  width: 140px;
  height: 32px;
  padding: 8px;
  font-size: 14px;
  line-height: 16px;
}
.courseList .lesson .btnBox .btn.btn-link {
  position: relative;
  padding: 0;
  font-size: 12px;
  color: #aaa;
  cursor: default;
}
.courseList .lesson .btnBox .btn.btn-link:hover {
  text-decoration: none;
}
.courseList:hover {
  background-color: #fcfcfc;
}
.redBallNumBox {
  position: relative;
}
.redNum,
.redBallNum {
  position: absolute;
  width: 18px;
  height: 18px;
  right: -9px;
  top: -9px;
  font-size: 12px;
  text-align: center;
  line-height: 18px;
  color: #fff;
  background-color: #f94d30;
  border-radius: 50%;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
}
.requestListBox {
  padding-bottom: 10px;
}
.requestListBox .requestList {
  position: relative;
  min-height: 60px;
  margin: 0px 30px;
  border-bottom: 1px solid #ddd;
  -webkit-animation: showByFade 0.5s 0.2s ease both;
}
.requestListBox .requestList:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.requestListBox .requestList:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.requestListBox .requestList .requestIcon {
  position: absolute;
  left: 8px;
  top: 8px;
}
.requestListBox .requestList .requestIcon img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.requestListBox .requestList .requestIcon .redBallNum {
  margin-top: 3px;
  margin-right: 3px;
}
.requestListBox .requestList .requestInfo {
  margin-left: 56px;
  margin-right: 160px;
  padding-top: 8px;
  padding-right: 8px;
}
.requestListBox .requestList .requestInfo .requestTitle {
  float: left;
  line-height: 20px;
}
.requestListBox .requestList .requestInfo .requestDate {
  float: right;
  color: #aaa;
  line-height: 20px;
}
.requestListBox .requestList .requestInfo .requestTips {
  clear: both;
  color: #999;
}
.requestListBox .requestList .requestConfirm {
  position: absolute;
  width: 160px;
  right: 0;
  top: 0;
  padding: 16px 0px;
  line-height: 24px;
}
.requestListBox .requestList .requestConfirm .btn {
  line-height: 12px;
}
.requestListBox .requestList:hover {
  background-color: #f4f4f4;
}
.close {
  position: absolute;
  top: 0px;
  right: 0px;
  cursor: pointer;
}
.close:before {
  content: '×';
  display: block;
  width: 40px;
  height: 40px;
  color: #999;
  font-size: 22px;
  line-height: 40px;
  text-align: center;
  -webkit-transition: .3s;
}
.close:hover:before {
  color: #4daa63;
}
.close.rect:before {
  content: '×';
  display: block;
  width: 20px;
  height: 20px;
  background-color: #222;
  border-radius: 4px;
  color: #444;
  font-size: 20px;
  line-height: 18px;
  text-align: center;
  -webkit-transition: .3s;
}
.close.rect:hover:before {
  color: #dd3333;
}
.loginBox {
  position: absolute;
  overflow: hidden;
  width: 350px;
  height: 480px;
  left: 50%;
  top: 50%;
  margin-left: -175px;
  margin-top: -265px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0px 4px 9px 0px rgba(0, 0, 0, 0.5);
}
.loginBox .formBox {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
  bottom: 0;
}
.loginBox .formBox.form_login {
  /*
			-webkit-transition: .3s .3s;
			.loginSubmitBtn {
				-webkit-transition: .3s .3s;
			}
			.setMoreBox{
				height: 0;
				-webkit-transition: .3s .3s;
				.setMoreOpition{
					opacity: 0;
					-webkit-transition: .3s;
				}
			}
			&.setLogin{
				-webkit-transition: .3s;
				top: -40px;
				.loginSubmitBtn {
					-webkit-transition: .3s;
					bottom: 100px;
				}
				.setMoreBox{
					-webkit-transition: .3s;
					height: 50px;
					.setMoreOpition{
						-webkit-transition: .3s .3s;
						opacity: 1;
					}
					.setMoreBtn{
						background-position: 0 -50px;
						&:hover{
							background-position: -50px -50px;
						}
					}
				}
			}
			*/
  -webkit-transition: .3s .15s;
}
.loginBox .formBox.form_login .loginSubmitBtn {
  -webkit-transition: .3s .15s;
}
.loginBox .formBox.form_login .setMoreBox {
  height: 0;
  -webkit-transition: .3s .15s;
}
.loginBox .formBox.form_login .setMoreBox .setMoreOpition {
  opacity: 0;
  -webkit-transition: .3s;
}
.loginBox .formBox.form_login.setLogin {
  -webkit-transition: .3s;
  top: -40px;
}
.loginBox .formBox.form_login.setLogin .loginSubmitBtn {
  -webkit-transition: .3s;
  bottom: 100px;
}
.loginBox .formBox.form_login.setLogin .setMoreBox {
  -webkit-transition: .3s;
  height: 50px;
}
.loginBox .formBox.form_login.setLogin .setMoreBox .setMoreBtn {
  background-position: 0 0px;
}
.loginBox .formBox.form_login.setLogin .setMoreBox .setMoreBtn:hover {
  background-position: -50px 0px;
}
.loginBox .formBox.form_login.setLogin .setMoreBox .setMoreOpition {
  -webkit-transition: .3s .15s;
  opacity: 1;
}
.loginBox .formBox.form_login .setMoreBox {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  padding-bottom: 10px;
}
.loginBox .formBox.form_login .setMoreBox .setMoreBtn {
  display: block;
  position: absolute;
  width: 50px;
  height: 50px;
  left: 50%;
  top: -40px;
  margin-left: -25px;
  background-image: url(../images/icon_login_setmore.png);
  background-position: 0 -50px;
}
.loginBox .formBox.form_login .setMoreBox .setMoreBtn:hover {
  background-position: -50px -50px;
}
.loginBox .formBox.form_login .setMoreBox .selectServer {
  position: relative;
  padding-left: 36px;
  line-height: 30px;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox {
  position: absolute;
  overflow: hidden;
  z-index: 4;
  width: 200px;
  left: 120px;
  bottom: 0px;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList {
  cursor: pointer;
  position: relative;
  display: none;
  padding-left: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList:hover {
  background-color: #ddd;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList.disabled,
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList.checked {
  display: block;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList.disabled:hover,
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList.checked:hover {
  background-color: transparent;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList.checked:after {
  content: '';
  cursor: pointer;
  width: 30px;
  height: 30px;
  position: relative;
  top: 10px;
  margin-top: -10px;
  display: inline-block;
  background-image: url(../images/icon_select.png);
  background-position: 0 0;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList.checked:hover {
  background-color: transparent;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList.checked:hover:after {
  background-position: -30px 0;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList:before {
  content: '';
  position: absolute;
  display: block;
  width: 8px;
  height: 8px;
  left: 15px;
  top: 50%;
  margin-top: -4px;
  background-color: transparent;
  border-radius: 50%;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList.status0:before {
  background-color: transparent;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList.status1:before {
  background-color: #0bcf24;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList.status2:before {
  background-color: #ffb10a;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList.status3:before {
  background-color: #fb4b39;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox .serverList.status4:before {
  background-color: #999999;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox.open {
  overflow: auto;
  max-height: 450px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox.open::-webkit-scrollbar-thumb {
  background-color: #91919b;
  border: 1px solid transparent;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox.open .serverList {
  display: block;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox.open .serverList.checked {
  background-color: #eee;
}
.loginBox .formBox.form_login .setMoreBox .selectServer .serverListBox.open .serverList.checked:after {
  background-image: none;
}
.loginBox .texthoverBtn {
  display: inline-block;
  padding: 5px 5px;
  border: 1px solid transparent;
  color: #71bd6a;
  border-radius: 3px;
  text-decoration: none;
  -webkit-transition: .2s;
}
.loginBox .texthoverBtn:hover {
  background-color: #71bd6a;
  color: #fff;
}
.loginBox .topNav {
  height: 70px;
  padding: 15px 15px 0px 15px;
  margin: 0 auto;
  text-align: left;
}
.loginBox .topNav a.text {
  display: inline-block;
  padding: 5px 16px;
  border: 1px solid transparent;
  color: #4daa63;
  border-radius: 3px;
  text-decoration: none;
  -webkit-transition: .2s;
}
.loginBox .topNav a.text:hover {
  border: 1px solid #71bd6a;
}
.loginBox .logoBox {
  height: 125px;
  text-align: center;
}
.loginBox .logoBox .logoImg {
  width: 260px;
  height: 90px;
}
.loginBox .checkbox {
  color: #777;
}
.loginBox .form-group {
  margin: 0 36px;
}
.loginBox .form-group .input-group {
  padding-bottom: 2px;
  margin-bottom: 20px;
  border-bottom: 1px solid #ccc;
}
.loginBox .form-group .input-group input[type="text"].form-ctrl,
.loginBox .form-group .input-group input[type="password"].form-ctrl,
.loginBox .form-group .input-group div.inputTips {
  padding: 5px;
  background-color: transparent;
  border: 0;
  font-size: 14px;
  line-height: 18px;
}
.loginBox .form-group .input-group div.inputTips {
  font-weight: normal;
  border-bottom: 0;
}
.loginBox .form-group .input-group .sendMsg {
  height: 30px;
}
.loginBox .loginSubmitBtn {
  position: absolute;
  width: 280px;
  height: 50px;
  left: 50%;
  margin-left: -140px;
  bottom: 60px;
}
.loginBoxHide {
  -webkit-animation: hideByRotate .3s linear;
}
.loginBoxShow {
  -webkit-animation: showByRotate .2s linear;
}
.selectBox {
  position: relative;
  display: inline-block;
  min-width: 120px;
  padding-right: 16px;
  background-color: #fff;
  border: 1px solid #eee;
}
.selectBox .arrow {
  width: 20px;
  text-align: center;
  height: 30px;
  line-height: 30px;
  position: absolute;
  right: 0;
  top: 0%;
  cursor: pointer;
  color: #999999;
}
.selectBox .arrow:before {
  content: '▼';
}
.selectBox .selectedBox {
  padding: 8px;
  cursor: default;
  line-height: 12px;
  display: block;
}
.optionBoxBg {
  position: fixed;
  z-index: 998;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: #000;
  opacity: 0;
  filter: alpha(opacity=0);
}
.optionBox {
  position: absolute;
  z-index: 999;
  left: -1px;
  border: 1px solid #eee;
  background-color: #fff;
  overflow-x: hidden;
  overflow-y: auto;
}
.optionBox .optionListBox {
  position: relative;
  margin: 0;
  padding: 0;
  list-style: none;
}
.optionBox .optionListBox .optionList {
  cursor: pointer;
  padding: 10px 5px;
  line-height: 1em;
  background-color: #fff;
}
.optionBox .optionListBox .optionList:hover {
  color: #fff;
  background-color: #7cc276;
}
.optionTopWhite {
  z-index: 1000;
  position: absolute;
  height: 1px;
  left: 0px;
  top: -1px;
  font-size: 0;
  background-color: #fff;
}
.popUpBoxBlackBg {
  position: fixed;
  z-index: 9999;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: #000;
  opacity: .7;
  filter: alpha(opacity=70);
}
.pageContent.profileInfo {
  width: 550px;
  margin: 0 auto;
}
.pageContent.profileInfo .userHead {
  position: relative;
  width: 70px;
  height: 70px;
  margin: 35px auto 0px auto;
  border: 1px solid #ddd;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
}
.pageContent.profileInfo .userHead .headImg {
  width: 68px;
  height: 68px;
  border-radius: 50%;
}
.pageContent.profileInfo .userHead .changeBtn {
  display: none;
  position: absolute;
  width: 68px;
  height: 68px;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  line-height: 68px;
  color: #fff;
  text-align: center;
}
.pageContent.profileInfo .userHead:hover .changeBtn {
  display: block;
}
.pageContent.profileInfo .userName {
  height: 30px;
  padding-top: 18px;
  margin-bottom: 5px;
  border-bottom: 1px solid #ddd;
}
.pageContent.profileInfo .userName .textBox {
  width: 210px;
  height: 20px;
  margin: 0 auto;
  background-color: #fefefe;
  font-size: 14px;
  text-align: center;
  font-weight: bold;
}
.pageContent.profileInfo .userTips {
  width: 400px;
  margin: 0 auto;
}
.pageContent.profileInfo .userTips .userTipsInput {
  display: block;
  width: 100%;
  padding: 5px;
  background-color: #fefefe;
  border: 1px solid transparent;
  font-size: 12px;
  color: #999;
  text-align: center;
}
.pageContent.profileInfo .userInfoListBox {
  position: relative;
  padding: 0 36px;
  margin-top: 50px;
  background-color: #fff;
}
.pageContent.profileInfo .userInfoListBox .row {
  padding: 4px 0px;
  line-height: 30px;
}
.pageContent.profileInfo .userInfoListBox .row .th {
  color: #888;
}
.pageContent.profileInfo .userInfoListBox .editInfoBtn,
.pageContent.profileInfo .userInfoListBox .saveInfoBtn {
  position: absolute;
  right: 0;
  top: -30px;
}
.pageContent.profileInfo .userInfoListBox .selectBox {
  min-width: 50px;
  border-radius: 3px;
}
.pageContent.profileInfo .userInfoListBox input[type="text"].form-ctrl,
.pageContent.profileInfo .userInfoListBox input[type="password"].form-ctrl,
.pageContent.profileInfo .userInfoListBox div.inputTips {
  background-color: #fff;
  border-radius: 3px;
}
.pageContent.profileInfo .userInfoListBox .btn.df {
  height: 29px;
}
.pageContent.profileInfo #conName {
  margin-left: 15px;
}
.popUpWin {
  position: fixed;
  z-index: 9999;
}
.popUpWin .popUpBoxBg {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.8);
}
.popUpWin .popUpBoxFg {
  position: relative;
  padding: 5px;
  background-color: #ddd;
  border-radius: 4px;
}
.popUpWin .popUpBoxFg .close {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
}
.popUpWin .popUpBoxFg .close:before {
  content: '×';
  display: block;
  width: 20px;
  height: 20px;
  color: #999;
  font-size: 20px;
  line-height: 18px;
  text-align: center;
}
.popUpWin .popUpBoxFg .close:hover:before {
  color: #71bd6a;
}
.popUpWin .popUpBoxFg .popUpBoxTitle,
.popUpWin .popUpBoxFg .popUpBoxContent,
.popUpWin .popUpBoxFg .submitBtnBox {
  background-color: rgba(255, 255, 255, 0.8);
}
.popUpWin .popUpBoxFg .popUpBoxTitle {
  padding: 10px;
  font-weight: bold;
}
.popUpWin .popUpBoxFg .popUpBoxContent {
  padding: 40px;
  color: #363636;
}
.popUpWin .popUpBoxFg .submitBtnBox {
  padding: 0px 10px 10px 10px;
  width: 100%;
}
.popUpWin .popUpBoxFg .submitBtnBox .col {
  padding: 5px;
  text-align: center;
}
.popUpWin .popUpBoxFg .submitBtnBox.confirm .col {
  display: inline-block;
  width: 50%;
}
.popUpWin.hidden {
  display: block;
}
.popUpWin.hidden .popUpBoxBg {
  background-color: transparent;
}
.popUpWin.wait .popUpBoxFg {
  background-color: transparent;
  border: 0;
}
.popUpWin.wait .popUpBoxFg .popUpBoxTitle,
.popUpWin.wait .popUpBoxFg .popUpBoxContent,
.popUpWin.wait .popUpBoxFg .submitBtnBox {
  background-color: transparent;
}
.popUpWin.wait.black .popUpBoxFg {
  background-color: rgba(0, 0, 0, 0.5);
  box-shadow: none;
}
.popUpWin.wait.black .popUpBoxFg .popUpBoxContent {
  padding: 0;
}
.popUpWin.fullScreen {
  width: 100%;
  height: 100%;
}
.popUpWin.fullScreen .popUpBoxFg {
  position: absolute;
  left: 10px;
  top: 10px;
  right: 10px;
  bottom: 10px;
}
.popUpWin.menu {
  border: 0;
  width: 0;
  height: 0;
}
.popUpWin.menu .popUpBoxFg {
  position: absolute;
  padding: 0;
  left: 0;
  top: 0;
  border: 0;
}
.popUpWin.menu .popUpBoxFg .popUpBoxContent {
  position: absolute;
  padding: 0;
  background-color: transparent;
  border-radius: 3px;
}
.popUpWin.menu .popUpBoxFg .popUpBoxContent .menuBtnBox {
  padding: 0;
  border-radius: 3px;
  box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.4);
  overflow: hidden;
}
.popUpWin.menu .popUpBoxFg .popUpBoxContent .menuBtnBox .menuBtn {
  padding: 8px 8px 8px 16px;
  cursor: default;
  background-image: url(../images/icon_point_green.png);
  background-repeat: no-repeat;
  background-position: -100px center;
  border-top: 1px solid #eee;
  white-space: nowrap;
}
.popUpWin.menu .popUpBoxFg .popUpBoxContent .menuBtnBox .menuBtn:first-child {
  border-top-width: 0px;
}
.popUpWin.menu .popUpBoxFg .popUpBoxContent .menuBtnBox .menuBtn:hover {
  background-position: 7px center;
}
.popUpWin.menu .popUpBoxFg .popUpBoxContent .menuArrow {
  position: absolute;
  background-repeat: no-repeat;
}
.popUpWin.menu .popUpBoxFg .popUpBoxContent .menuArrow:before {
  content: '';
  position: absolute;
  display: block;
}
.popUpWin.menu.arrowDown .popUpBoxFg .popUpBoxContent {
  left: 0;
  bottom: 0;
}
.popUpWin.menu.arrowDown .popUpBoxFg .popUpBoxContent .menuArrow {
  width: 30px;
  height: 12px;
  bottom: -12px;
  left: 0;
  background-position: left bottom;
}
.popUpWin.menu.arrowDown .popUpBoxFg .popUpBoxContent .menuArrow:before {
  height: 1px;
  width: 36%;
  left: 32%;
  top: -1px;
}
.popUpWin.menu.arrowTop .popUpBoxFg .popUpBoxContent {
  left: 0;
  top: 0;
}
.popUpWin.menu.arrowTop .popUpBoxFg .popUpBoxContent .menuArrow {
  width: 30px;
  height: 12px;
  top: -12px;
  left: 0;
  background-position: left top;
}
.popUpWin.menu.arrowTop .popUpBoxFg .popUpBoxContent .menuArrow:before {
  height: 1px;
  width: 36%;
  left: 32%;
  bottom: -1px;
}
.popUpWin.menu .popUpBoxFg .popUpBoxContent {
  border: 1px solid #eee;
}
.popUpWin.menu .popUpBoxFg .popUpBoxContent .menuBtnBox {
  background-color: #fff;
}
.popUpWin.menu .popUpBoxFg .popUpBoxContent .menuBtnBox .menuBtn {
  color: #333;
  background-color: #fff;
}
.popUpWin.menu .popUpBoxFg .popUpBoxContent .menuBtnBox .menuBtn:hover {
  background-color: #efefef;
}
.popUpWin.menu .popUpBoxFg .popUpBoxContent .menuArrow {
  background-image: url(../images/icon_menu_arrow_white.png);
}
.popUpWin.menu .popUpBoxFg .popUpBoxContent .menuArrow:before {
  background-color: #fff;
}
.popUpWin.menu.black .popUpBoxFg .popUpBoxContent {
  border: 1px solid #61626a;
}
.popUpWin.menu.black .popUpBoxFg .popUpBoxContent .menuBtnBox {
  background-color: #52535b;
}
.popUpWin.menu.black .popUpBoxFg .popUpBoxContent .menuBtnBox .menuBtn {
  color: #fff;
  background-color: #52535b;
  border-top-color: #61626a;
}
.popUpWin.menu.black .popUpBoxFg .popUpBoxContent .menuBtnBox .menuBtn:hover {
  background-color: #47484f;
}
.popUpWin.menu.black .popUpBoxFg .popUpBoxContent .menuArrow {
  background-image: url(../images/icon_menu_arrow.png);
}
.popUpWin.menu.black .popUpBoxFg .popUpBoxContent .menuArrow:before {
  background-color: #52535b;
}
.popUpWin.tips .popUpBoxFg .popUpBoxContent {
  padding: 20px;
}
.popUpWin.black .popUpBoxFg {
  background-color: rgba(0, 0, 0, 0.7);
  box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.5);
}
.popUpWin.black .popUpBoxFg .popUpBoxContent {
  background-color: transparent;
  border-color: transparent;
  color: #fff;
}
.popUpWin.menu.moodFace .popUpBoxFg .popUpBoxContent .menuBtnBox {
  width: 258px;
  height: 130px;
  border: 5px solid transparent;
  border-right: 0;
  overflow-x: hidden;
  overflow-y: auto;
}
.popUpWin.menu.moodFace .popUpBoxFg .popUpBoxContent .menuBtnBox:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.popUpWin.menu.moodFace .popUpBoxFg .popUpBoxContent .menuBtnBox:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.popUpWin.menu.moodFace .popUpBoxFg .popUpBoxContent .menuBtnBox .menuBtn {
  float: left;
  padding: 5px;
  border-width: 0 ;
  border-left-width: 0;
  border-top-width: 0;
  border-right-width: 1px;
  border-bottom-width: 1px;
  background-image: none;
}
.popUpWin.menu.moodFace .popUpBoxFg .popUpBoxContent .menuBtnBox .menuBtn img {
  display: block;
  width: 20px;
  height: 20px;
}
.popUpWin.menu.moodFace .popUpBoxFg .popUpBoxContent .menuBtnBox .menuBtn:first-child {
  border-top-width: 0px;
  border-radius: 3px 0px 0px 0px;
}
.popUpWin.menu.moodFace .popUpBoxFg .popUpBoxContent .menuBtnBox .menuBtn:nth-child( 8n) {
  border-right-width: 0px;
}
.popUpWin.menu.moodFace .popUpBoxFg .popUpBoxContent .menuBtnBox .clearLine {
  position: absolute;
  width: 100%;
  height: 1px;
  left: 0;
  bottom: 0px;
  background-color: #fcfcfc;
}
.popUpWin.wait.fullscreen.white .popUpBoxBg {
  background-color: #fff;
}
.popUpWin.wait.fullscreen.white .popUpBoxFg {
  box-shadow: none;
}
.popUpWin .popUpBoxFg {
  background-color: #fcfcfc;
  border: 1px solid #aaa;
  border-top: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2);
}
.popUpWin .popUpBoxFg .popUpBoxContent {
  padding: 10px;
}
.popUpWin .popUpBoxFg .close {
  position: absolute;
  display: block;
  width: 20px;
  height: 20px;
  padding: 0;
  margin: 0;
  top: 10px;
  right: 10px;
  background-color: transparent;
  border: 0;
  font-size: 20px;
  line-height: 18px;
  text-align: center;
  color: #999;
  cursor: pointer;
}
.popUpWin .popUpBoxFg .close:hover {
  color: #7cc276;
}
.popUpWin .popUpBoxFg .submitBtnBox {
  padding: 0px 10px 10px 10px;
  width: 100%;
}
.popUpWin .popUpBoxFg .submitBtnBox .col .btn {
  display: block;
}
.popUpWin .popUpBoxFg .submitBtnBox.confirm .col .btn {
  display: block;
}
.popUpWin .popUpBoxFg .submitBtnBox .btn {
  padding: 8px 16px;
  font-size: 14px;
  line-height: 14px;
}
.popUpWin .popUpBoxFg .submitBtnBox .btn.yes {
  color: #fff;
  background-color: #71bd6a;
  border-color: #71bd6a;
}
.popUpWin .popUpBoxFg .submitBtnBox .btn.yes:hover {
  background-color: #7cc276;
}
.popUpWin .popUpBoxFg .submitBtnBox .btn.no {
  color: #fff;
  background-color: #cccccc;
  border-color: #cccccc;
}
.popUpWin .popUpBoxFg .submitBtnBox .btn.no:hover {
  background-color: #bbbbbb;
}
.popUpWin.black_alpha80 .popUpBoxFg .popUpBoxContent {
  background-color: rgba(0, 0, 0, 0.8);
}
.popUpWin.black_alpha80 .popUpBoxFg .popUpBoxContent .menuTipsBox {
  padding: 6px 12px;
}
.popUpWin.black_alpha80 .popUpBoxFg .popUpBoxContent .menuTipsBox .menuTips {
  white-space: nowrap;
  color: #fff;
}
.popUpWin.black_alpha80 .popUpBoxFg .popUpBoxContent .menuArrow {
  background-image: url(../images/icon_menu_arrow_black_alpha80.png);
}
.popUpWin.black_alpha80 .popUpBoxFg .popUpBoxContent .menuArrow:before {
  background-color: rgba(0, 0, 0, 0.8);
}
.popUpWin .userIntroBox {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 15px;
}
.popUpWin .userIntroBox .headImg {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
  margin-bottom: 5px;
}
.popUpWin .userIntroBox .headImg img {
  width: 70px;
  height: 70px;
}
.popUpWin .userIntroBox .userName {
  font-size: 14px;
  font-weight: bold;
}
.popUpWin .userIntroBox .userSign {
  color: #999;
  padding: 0 30px;
}
.popUpWin .userIntroBox.default {
  margin-left: -10px;
  margin-right: -10px;
  text-align: center;
}
.popUpWin .userIntroBox.style2 {
  padding-top: 10px;
  border-bottom: 0;
}
.popUpWin .userIntroBox.style2 table {
  line-height: 22px;
}
.popUpWin .userIntroBox.style2 table tr th {
  width: 60px;
  color: #999;
  font-weight: normal;
  text-align: left;
}
.popUpWin .userInfoBox .userTipsInput {
  border: 0;
  background-color: transparent;
}
.popUpWin .userInfoBox table {
  width: 100%;
  padding: 10px 0px;
}
.popUpWin .userInfoBox table tr th,
.popUpWin .userInfoBox table tr td {
  padding: 5px;
  line-height: 1;
}
.popUpWin .userInfoBox table tr th {
  width: 40%;
  color: #999;
  font-weight: normal;
  text-align: right;
}
.popUpWin.centerInFullscreen .popUpBoxFg {
  background-color: transparent;
  border-color: transparent;
  box-shadow: none;
}
.popUpWin.centerInFullscreen .popUpBoxFg .close {
  display: none;
}
.questionAlertBox {
  position: absolute;
  left: 20px;
  top: 20px;
  right: 20px;
  bottom: 20px;
  background-color: #4a4b53;
  border: 1px solid #323237;
  border-radius: 8px;
  box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.3);
}
.questionAlertBox .content {
  position: absolute;
  left: 15px;
  top: 45px;
  right: 15px;
  bottom: 15px;
  overflow: auto;
  color: #eee;
}
.questionAlertBox .content img {
  max-width: 100%;
}
.questionAlertBox .info {
  position: absolute;
  width: 100%;
  height: 30px;
  left: 0;
  top: 0;
  border-bottom: 1px solid #404148;
  color: #787a83;
  line-height: 30px;
}
.questionAlertBox .info .left {
  float: left;
  padding-left: 15px;
}
.questionAlertBox .info .right {
  float: right;
  padding-right: 15px;
}
.evaluationTitle {
  font-size: 16px;
  padding-bottom: 20px;
}
.prizeRank {
  padding: 15px;
  margin-bottom: 20px;
  background-color: #eee;
  border: 1px solid #ccc;
}
.prizeRank .table {
  display: block;
}
.prizeRank .table:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.prizeRank .table:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.prizeRank .table .tr {
  display: block;
}
.prizeRank .table .tr .td {
  display: block;
  float: left;
  text-align: center;
}
.prizeRank .nickname {
  width: 100%;
  padding: 0 2px;
  border-bottom: 1px solid #ddd;
  color: #bbb;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.prizeRank .prizeNum {
  width: 100%;
}
.prizeRank .prizeNum .num {
  font-size: 18px;
}
.lessonComment {
  position: absolute;
  width: 100%;
  left: 0px;
  top: 230px;
  bottom: 0px;
  border: 1px solid #ccc;
}
.studentEvaluationBox {
  position: absolute;
  width: 100%;
  left: 0;
  top: 45px;
  bottom: 60px;
  overflow: auto;
}
.studentEvaluation {
  padding-top: 15px;
}
.studentEvaluation:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.studentEvaluation:after {
  content: '';
  display: block;
  clear: both;
  height: 0px;
  overflow: hidden;
}
.studentEvaluation .userInfo {
  float: left;
  width: 80px;
  text-align: center;
}
.studentEvaluation .userInfo .headImg img {
  width: 40px;
  height: 40px;
  border: 1px solid #ccc;
  border-radius: 50%;
  box-shadow: -1px 1px 2px rgba(0, 0, 0, 0.1);
}
.studentEvaluation .userInfo .nickname {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.studentEvaluation .evaluationInfo {
  margin-left: 90px;
}
.studentEvaluation .evaluationInfo .scoreStar {
  display: block;
}
.studentEvaluation .evaluationInfo textarea.studentComment {
  display: block;
  width: 100%;
  height: 50px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.scoreStar {
  position: relative;
  display: inline-block;
  width: 125px;
  height: 25px;
  top: 7px;
  margin-top: -7px;
  background-image: url(../images/icon_scorestar.png);
  background-position: -125px 0;
}
.scoreStar.check1 {
  background-position: -100px 0;
}
.scoreStar.check2 {
  background-position: -75px 0;
}
.scoreStar.check3 {
  background-position: -50px 0;
}
.scoreStar.check4 {
  background-position: -25px 0;
}
.scoreStar.check5 {
  background-position: 0 0;
}
.scoreStar li.star {
  float: left;
  width: 25px;
  height: 25px;
}
.scoreStar li.val {
  display: none;
}
.scoreStar.edit li.star {
  cursor: pointer;
}
body.mobileFrame {
  overflow: hidden;
}
body.mobileFrame .loginBox {
  width: auto;
  height: auto;
  left: 0px;
  right: 0px;
  top: 0px;
  bottom: 0px;
  margin: auto;
}
/* model class  */
.diceBox {
  font-size: 100px;
  height: 1em;
  width: 1em;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -0.5em;
  margin-top: -0.5em;
  z-index: 1;
  -webkit-transform-style: preserve-3d;
  -webkit-transform: perspective(6em) rotateX(0deg) rotateY(0deg);
  -webkit-transition: 1.5s -webkit-transform ease-out;
}
.diceBox.rolling {
  -webkit-transition: .5s all linear;
}
.diceBox .faceBox {
  position: absolute;
  width: 1em;
  height: 1em;
  left: 0;
  top: 0;
  background-color: #fff;
  border-radius: .2em;
  box-shadow: 0px 0px 0.4em rgba(0, 0, 0, 0.1) inset;
}
.diceBox .faceBox:after {
  content: '';
  position: absolute;
  display: block;
  width: 0.2em;
  height: 0.2em;
  left: 50%;
  top: 50%;
  margin-left: -0.1em;
  margin-top: -0.1em;
  background-color: #ee0000;
  border-radius: 50%;
  z-index: 100;
}
.diceBox .faceBox[data-num="x"],
.diceBox .faceBox[data-num="y"],
.diceBox .faceBox[data-num="z"],
.diceBox .faceBox[data-num="x1"],
.diceBox .faceBox[data-num="x2"],
.diceBox .faceBox[data-num="y1"],
.diceBox .faceBox[data-num="y2"],
.diceBox .faceBox[data-num="z1"],
.diceBox .faceBox[data-num="z2"] {
  background-color: #f2f2f2;
  border: 2px solid transparent;
  border-radius: .1em;
  box-shadow: none;
}
.diceBox .faceBox[data-num="x"]:after,
.diceBox .faceBox[data-num="y"]:after,
.diceBox .faceBox[data-num="z"]:after,
.diceBox .faceBox[data-num="x1"]:after,
.diceBox .faceBox[data-num="x2"]:after,
.diceBox .faceBox[data-num="y1"]:after,
.diceBox .faceBox[data-num="y2"]:after,
.diceBox .faceBox[data-num="z1"]:after,
.diceBox .faceBox[data-num="z2"]:after {
  background-color: transparent;
}
.diceBox .faceBox[data-num="x"] {
  border-radius: 0;
  -webkit-transform: rotateX(90deg) translateZ(0) scaleX(0.6) scaleY(0.6);
}
.diceBox .faceBox[data-num="x1"] {
  -webkit-transform: rotateX(90deg) translateZ(0.28em) scaleX(0.6) scaleY(0.6);
}
.diceBox .faceBox[data-num="x2"] {
  -webkit-transform: rotateX(90deg) translateZ(-0.28em) scaleX(0.6) scaleY(0.6);
}
.diceBox .faceBox[data-num="y"] {
  border-radius: 0;
  -webkit-transform: rotateY(90deg) translateZ(0) scaleX(0.6) scaleY(0.6);
}
.diceBox .faceBox[data-num="y1"] {
  -webkit-transform: rotateY(90deg) translateZ(0.28em) scaleX(0.6) scaleY(0.6);
}
.diceBox .faceBox[data-num="y2"] {
  -webkit-transform: rotateY(90deg) translateZ(-0.28em) scaleX(0.6) scaleY(0.6);
}
.diceBox .faceBox[data-num="z"] {
  border-radius: 0;
  -webkit-transform: rotateZ(90deg) translateZ(0) scaleX(0.6) scaleY(0.6);
}
.diceBox .faceBox[data-num="z1"] {
  -webkit-transform: rotateZ(90deg) translateZ(0.28em) scaleX(0.6) scaleY(0.6);
}
.diceBox .faceBox[data-num="z2"] {
  -webkit-transform: rotateZ(90deg) translateZ(-0.28em) scaleX(0.6) scaleY(0.6);
}
.diceBox .faceBox[data-num="1"] {
  -webkit-transform: rotateX(0deg) rotateY(0deg) translateZ(0.3em) scaleX(0.6) scaleY(0.6);
}
.diceBox .faceBox[data-num="1"]:after {
  width: 30%;
  height: 30%;
  margin-left: -15%;
  margin-top: -15%;
}
.diceBox .faceBox[data-num="2"] {
  -webkit-transform: rotateX(0deg) rotateY(90deg) translateZ(0.3em) scaleX(0.6) scaleY(0.6);
  opacity: 1;
}
.diceBox .faceBox[data-num="2"]:after {
  background-color: transparent;
  box-shadow: -0.25em -0.25em 0 #cc0000, 0.25em 0.25em 0 #cc0000;
}
.diceBox .faceBox[data-num="3"] {
  -webkit-transform: rotateX(0deg) rotateY(180deg) translateZ(0.3em) scaleX(0.6) scaleY(0.6);
  opacity: 1;
}
.diceBox .faceBox[data-num="3"]:after {
  background-color: #c00;
  box-shadow: -0.25em -0.25em 0 #cc0000, 0.25em 0.25em 0 #cc0000;
}
.diceBox .faceBox[data-num="4"] {
  -webkit-transform: rotateX(0deg) rotateY(270deg) translateZ(0.3em) scaleX(0.6) scaleY(0.6);
  opacity: 1;
}
.diceBox .faceBox[data-num="4"]:after {
  background-color: transparent;
  box-shadow: -0.25em -0.25em 0 #cc0000, 0.25em -0.25em 0 #cc0000, 0.25em 0.25em 0 #cc0000, -0.25em 0.25em 0 #cc0000;
}
.diceBox .faceBox[data-num="5"] {
  -webkit-transform: rotateX(-90deg) rotateY(0deg) translateZ(0.3em) scaleX(0.6) scaleY(0.6);
  opacity: 1;
}
.diceBox .faceBox[data-num="5"]:after {
  background-color: #c00;
  box-shadow: -0.25em -0.25em 0 #cc0000, 0.25em -0.25em 0 #cc0000, 0.25em 0.25em 0 #cc0000, -0.25em 0.25em 0 #cc0000;
}
.diceBox .faceBox[data-num="6"] {
  -webkit-transform: rotateX(90deg) rotateY(0deg) translateZ(0.3em) scaleX(0.6) scaleY(0.6);
  opacity: 1;
}
.diceBox .faceBox[data-num="6"]:after {
  background-color: transparent;
  box-shadow: -0.25em -0.25em 0 #cc0000, 0.25em -0.25em 0 #cc0000, -0.25em 0px 0 #cc0000, 0.25em 0px 0 #cc0000, 0.25em 0.25em 0 #cc0000, -0.25em 0.25em 0 #cc0000;
}
.diceBox.roll1 {
  transform: perspective(6em) rotateX(0deg) rotateY(0deg);
}
.diceBox.roll2 {
  transform: perspective(6em) rotateX(0deg) rotateY(-90deg);
}
.diceBox.roll3 {
  transform: perspective(6em) rotateX(0deg) rotateY(-180deg);
}
.diceBox.roll4 {
  transform: perspective(6em) rotateX(0deg) rotateY(90deg);
}
.diceBox.roll5 {
  transform: perspective(6em) rotateX(90deg) rotateY(0deg);
}
.diceBox.roll6 {
  transform: perspective(6em) rotateX(-90deg) rotateY(0deg);
}
@-webkit-keyframes diceRolling {
  0% {
    -webkit-transform: perspective(6em) rotateX(0deg) rotateY(0deg);
  }
  100% {
    -webkit-transform: perspective(6em) rotateX(360deg) rotateY(360deg);
  }
}
.clockTimerBox {
  position: relative;
  display: inline-block;
  top: 50%;
  padding: .2em 0 .2em 0;
  margin-top: -0.9em;
  font-size: 100px;
  white-space: nowrap;
  text-align: center;
  vertical-align: middle;
}
.clockTimerBox .wordBox {
  display: inline-block;
}
.clockTimerBox .charBox {
  position: relative;
  display: inline-block;
  margin: .1em;
  font-family: arial;
}
.clockTimerBox .charBox:after {
  content: '';
  position: absolute;
  display: block;
  width: 100%;
  height: 2px;
  left: 0;
  top: 50%;
  margin-top: -1px;
  background-color: rgba(100, 100, 100, 0.5);
  z-index: 2;
}
.clockTimerBox .charBox .charPrev,
.clockTimerBox .charBox .charNow,
.clockTimerBox .charBox .charNowTop,
.clockTimerBox .charBox .charPrevTop {
  height: 1.2em;
  padding: .1em;
  background-color: #eee;
  border-radius: .05em;
  box-shadow: 0.02em 0.02em 0.04em rgba(0, 0, 0, 0.3);
  line-height: 1em;
  font-family: arial;
  color: #333;
  text-shadow: 0.02em 0.02em 0.04em rgba(0, 0, 0, 0.3);
}
.clockTimerBox .charBox .charPrev {
  position: relative;
}
.clockTimerBox .charBox .charNow,
.clockTimerBox .charBox .charNowTop,
.clockTimerBox .charBox .charPrevTop {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}
.clockTimerBox .charBox .charNowTop,
.clockTimerBox .charBox .charPrevTop {
  overflow: hidden;
  height: 0.6em;
  -webkit-transform-origin: center bottom;
}
.clockTimerBox .charBox .charNowTop {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: none;
}
.clockTimerBox .charBox .charPrevTop {
  box-shadow: none;
}
.clockTimerBox .charBox.showAnimate .charPrevTop {
  -webkit-animation: wordCharPrevTopHide .5s linear both ;
}
.clockTimerBox .charBox.showAnimate .charNow {
  -webkit-transform: perspective(6em) rotateX(270deg);
  -webkit-animation: wordCharNowShow .5s .5s linear both ;
}
.clockTimerBox .wordBox.colon .charBox,
.clockTimerBox .charBox.colon {
  margin-top: 0;
  margin-bottom: 0;
}
.clockTimerBox .wordBox.colon .charBox:after,
.clockTimerBox .charBox.colon:after {
  display: none;
}
.clockTimerBox .wordBox.colon .charBox .charPrev,
.clockTimerBox .charBox.colon .charPrev,
.clockTimerBox .wordBox.colon .charBox .charNowTop,
.clockTimerBox .charBox.colon .charNowTop,
.clockTimerBox .wordBox.colon .charBox .charPrevTop,
.clockTimerBox .charBox.colon .charPrevTop {
  opacity: 0;
  padding: 0 0;
}
.clockTimerBox .wordBox.colon .charBox .charNow,
.clockTimerBox .charBox.colon .charNow {
  top: -0.12em;
  padding: 0 0;
  background-color: transparent;
  box-shadow: none;
  font-family: "微软雅黑";
  color: #eee;
  -webkit-animation: none;
  -webkit-transform: perspective(6em) rotateX(0deg);
}
.clockTimerBox .replayBtn {
  display: none;
  position: absolute;
  z-index: 9;
  width: 64%;
  height: 50%;
  left: 18%;
  top: 25%;
  background-color: #1cba77;
  border-radius: 0.2em;
}
.clockTimerBox .replayBtn .btnText {
  position: absolute;
  width: 100%;
  height: 1em;
  left: 0;
  top: 50%;
  margin-top: -0.5em;
  font-size: .5em;
  line-height: 1;
  text-align: center;
  color: #fff;
  cursor: pointer;
}
.clockTimerBox.timeUp .charBox.colon .charPrev,
.clockTimerBox.timeUp .wordBox.colon .charBox .charPrev,
.clockTimerBox.timeUp .wordBox .charBox .charPrev,
.clockTimerBox.timeUp .charBox.colon .charPrevTop,
.clockTimerBox.timeUp .wordBox.colon .charBox .charPrevTop,
.clockTimerBox.timeUp .wordBox .charBox .charPrevTop,
.clockTimerBox.timeUp .charBox.colon .charNowTop,
.clockTimerBox.timeUp .wordBox.colon .charBox .charNowTop,
.clockTimerBox.timeUp .wordBox .charBox .charNowTop {
  color: #c00;
}
.clockTimerBox.timeUp .charBox.colon .charNow,
.clockTimerBox.timeUp .wordBox.colon .charBox .charNow,
.clockTimerBox.timeUp .wordBox .charBox .charNow {
  color: #c00;
  text-shadow: none;
  -webkit-transform: perspective(6em) rotateX(0deg);
}
.clockTimerBox.timeUp:hover .replayBtn {
  display: block;
}
.clockTimerBox.clockSet {
  position: relative;
  padding: .4em 0 .4em 0;
  padding-right: 1.2em;
  margin-top: -1.1em;
}
.clockTimerBox.clockSet .charBox .charPrev {
  -webkit-animation: none;
}
.clockTimerBox.clockSet .charBox .charNow {
  -webkit-animation: none;
  position: relative;
}
.clockTimerBox.clockSet .charBox .setTopBtn,
.clockTimerBox.clockSet .charBox .setBottomBtn {
  position: absolute;
  width: 0px;
  height: 0px;
  left: 50%;
  margin-left: -0.2em;
  border-left: .2em solid transparent;
  border-right: .2em solid transparent;
  cursor: pointer;
}
.clockTimerBox.clockSet .charBox .setTopBtn {
  top: -0.3em;
  border-bottom: .2em solid #fff;
}
.clockTimerBox.clockSet .charBox .setTopBtn:hover {
  border-bottom: .2em solid #7ac970;
}
.clockTimerBox.clockSet .charBox .setBottomBtn {
  bottom: -0.3em;
  border-top: .2em solid #fff;
}
.clockTimerBox.clockSet .charBox .setBottomBtn:hover {
  border-top: .2em solid #7ac970;
}
.clockTimerBox.clockSet .setSubmitBtn {
  position: absolute;
  height: 1.3em;
  right: 0.5em;
  top: 50%;
  padding: 0.5em;
  margin-top: -0.65em;
  background-color: #7cc276;
  border-radius: 0.04em;
  font-size: .3em;
  line-height: .3em;
  text-align: center;
  color: #fff;
  cursor: pointer;
}
@-webkit-keyframes wordCharPrevTopHide {
  0% {
    -webkit-transform: perspective(6em) rotateX(360deg);
  }
  100% {
    -webkit-transform: perspective(6em) rotateX(270deg);
  }
}
@-webkit-keyframes wordCharNowShow {
  0% {
    -webkit-transform: perspective(6em) rotateX(90deg);
  }
  100% {
    -webkit-transform: perspective(6em) rotateX(0deg);
  }
}
@-webkit-keyframes wordCharTimeup {
  0% {
    color: #000;
  }
  100% {
    color: #c00;
  }
}
.clocker {
  font-size: 100px;
  position: absolute;
  width: 4.3em;
  height: 1.8em;
  left: 50%;
  top: 50%;
  padding: .2em .1em;
  -webkit-transform: translate(-50%, -50%) translateZ(0);
  white-space: nowrap;
  text-align: center;
}
.clocker .numGroup {
  display: inline-block;
}
.clocker .numBox {
  position: relative;
  display: inline-block;
  margin: .1em;
  font-family: arial;
}
.clocker .numBox .numPaper {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  box-shadow: 0.02em 0.02em 0.04em rgba(0, 0, 0, 0.3);
}
.clocker .numBox .numPaper:after {
  content: "";
  display: none;
  position: absolute;
  width: 100%;
  height: 2px;
  left: 0;
  top: .6em;
  margin-top: -1px;
  z-index: 999999;
  background-color: #54555d;
}
.clocker .numBox .numPaper .pageNowTop,
.clocker .numBox .numPaper .pageNowBottom,
.clocker .numBox .numPaper .pageNextTop,
.clocker .numBox .numPaper .pageNextBottom {
  position: relative;
  position: absolute;
  left: 0;
  height: 0.6em;
  background-color: #eee;
  line-height: 1em;
  font-family: arial;
  color: #333;
  text-shadow: 0.02em 0.02em 0.04em rgba(0, 0, 0, 0.3);
  overflow: hidden;
}
.clocker .numBox .numPaper .pageNowTop .v,
.clocker .numBox .numPaper .pageNowBottom .v,
.clocker .numBox .numPaper .pageNextTop .v,
.clocker .numBox .numPaper .pageNextBottom .v {
  position: absolute;
  top: 0;
  height: 1.2em;
  left: 0;
  padding: 0 0.1em;
  line-height: 1.2em;
}
.clocker .numBox .numPaper .pageNowTop .space,
.clocker .numBox .numPaper .pageNowBottom .space,
.clocker .numBox .numPaper .pageNextTop .space,
.clocker .numBox .numPaper .pageNextBottom .space {
  visibility: hidden;
  padding: 0 0.1em;
  line-height: 1.2em;
}
.clocker .numBox .numPaper .pageNowTop,
.clocker .numBox .numPaper .pageNextTop {
  top: 0;
  -webkit-transform-origin: center bottom;
  border-radius: .05em .05em 0em 0em;
}
.clocker .numBox .numPaper .pageNowTop .v,
.clocker .numBox .numPaper .pageNextTop .v {
  top: 0;
}
.clocker .numBox .numPaper .pageNowBottom,
.clocker .numBox .numPaper .pageNextBottom {
  top: .6em;
  -webkit-transform-origin: center top;
  border-radius: 0em 0em .05em .05em;
  box-shadow: 0.02em 0.02em 0.04em rgba(0, 0, 0, 0.3);
}
.clocker .numBox .numPaper .pageNowBottom:after,
.clocker .numBox .numPaper .pageNextBottom:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 10%;
  left: 0;
  top: 0;
  margin-top: -1px;
  background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0)));
}
.clocker .numBox .numPaper .pageNowBottom .v,
.clocker .numBox .numPaper .pageNextBottom .v {
  top: -0.6em;
}
.clocker .numBox .numPaper .pageNowBottom.flipUp {
  z-index: 1;
  -webkit-animation: clockerPageBottomflipUp .4s linear both ;
}
.clocker .numBox .numPaper .pageNextTop.flipUp {
  z-index: 1;
  -webkit-transform: perspective(6em) rotateX(270deg);
  -webkit-animation: clockerPageTopflipUp .4s .4s linear both ;
}
.clocker .numBox .numPaper .pageNowTop.flipDown {
  z-index: 1;
  -webkit-animation: clockerPageTopflipDown .4s linear both ;
}
.clocker .numBox .numPaper .pageNextBottom.flipDown {
  z-index: 1;
  -webkit-transform: perspective(6em) rotateX(90deg);
  -webkit-animation: clockerPageTopflipUp .4s .4s linear both ;
}
.clocker .numBox .addBtn,
.clocker .numBox .lessBtn {
  position: absolute;
  width: 0px;
  height: 0px;
  overflow: hidden;
  left: 50%;
  margin-left: -0.2em;
  border-left: .2em solid transparent;
  border-right: .2em solid transparent;
  cursor: pointer;
}
.clocker .numBox .addBtn {
  top: -0.3em;
  border-bottom: .2em solid #fff;
}
.clocker .numBox .addBtn:hover {
  border-bottom: .2em solid #7ac970;
}
.clocker .numBox .lessBtn {
  bottom: -0.3em;
  border-top: .2em solid #fff;
}
.clocker .numBox .lessBtn:hover {
  border-top: .2em solid #7ac970;
}
.clocker .numBox .numText {
  padding: 0 0.1em;
  height: 1.2em;
}
.clocker .point {
  position: relative;
  display: inline-block;
  top: -0.2em;
  line-height: 1.2em;
  background-color: transparent;
  font-family: "微软雅黑";
  color: #eee;
  text-shadow: 0.02em 0.02em 0.04em rgba(0, 0, 0, 0.3);
}
.clocker .startBtn {
  position: absolute;
  z-index: 9;
  width: 90%;
  height: .6em;
  left: 5%;
  bottom: .1em;
  background-color: #1cba77;
  border-radius: 0.05em;
}
.clocker .startBtn .label {
  position: absolute;
  width: 100%;
  height: 1em;
  left: 0;
  top: 50%;
  margin-top: -0.5em;
  font-size: .4em;
  text-transform: uppercase;
  line-height: 1;
  text-align: center;
  color: #fff;
  cursor: pointer;
}
.clocker .resetBtn {
  display: none;
  position: absolute;
  z-index: 9;
  width: 80%;
  height: .6em;
  left: 10%;
  top: 50%;
  -webkit-transform: translateY(-50%) translateZ(0);
  background-color: #1cba77;
  border-radius: 0.05em;
}
.clocker .resetBtn .label {
  position: absolute;
  width: 100%;
  height: 1em;
  left: 0;
  top: 50%;
  margin-top: -0.5em;
  font-size: .4em;
  line-height: 1;
  text-align: center;
  color: #fff;
  cursor: pointer;
}
.clocker:hover .resetBtn {
  display: block;
}
.clocker.setting {
  height: 2.8em;
  padding-top: .3em;
  padding-bottom: 1.2em;
}
.clocker.timeup .numGroup .numBox .numPaper .v {
  color: #c00;
}
@-webkit-keyframes clockerPageBottomflipUp {
  0% {
    -webkit-transform: perspective(6em) rotateX(0deg);
  }
  100% {
    -webkit-transform: perspective(6em) rotateX(90deg);
  }
}
@-webkit-keyframes clockerPageTopflipUp {
  0% {
    -webkit-transform: perspective(6em) rotateX(270deg);
  }
  100% {
    -webkit-transform: perspective(6em) rotateX(360deg);
  }
}
@-webkit-keyframes clockerPageTopflipDown {
  0% {
    -webkit-transform: perspective(6em) rotateX(360deg);
  }
  100% {
    -webkit-transform: perspective(6em) rotateX(270deg);
  }
}
@-webkit-keyframes clockerPageBottomflipDown {
  0% {
    -webkit-transform: perspective(6em) rotateX(90deg);
  }
  100% {
    -webkit-transform: perspective(6em) rotateX(0deg);
  }
}
.screenshotImgMenu {
  position: absolute;
  padding: 5px;
  color: #fff;
}
.screenshotImgMenu .menuList.addtoboard {
  width: 20px;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  background-image: url(../images/icon_add.png);
  background-repeat: no-repeat;
  background-position: center center;
  border-radius: 4px;
  cursor: pointer;
}
.accountListBox {
  position: absolute;
  z-index: 1;
  left: 0;
  right: 62px;
  top: 31px;
}
.accountListBox .slideBtn {
  position: absolute;
  z-index: 3;
  display: none;
  width: 30px;
  height: 30px;
  right: 10px;
  top: -32px;
  background-image: url(../images/icon_select.png);
  background-repeat: no-repeat;
  background-position: 0px -30px;
  cursor: pointer;
}
.accountListBox .slideBtn:hover {
  background-position: -30px -30px;
}
.accountListBox .accountList {
  display: none;
  overflow: auto;
  max-height: 84px;
  background-color: #fff;
  box-shadow: 3px 3px 3px 0px rgba(0, 0, 0, 0.2);
}
.accountListBox .accountList .account {
  position: relative;
  padding: 4px 8px;
  font-size: 14px;
  line-height: 20px;
  -webkit-transition: font-size 0.1s linear, background-color 0.1s linear;
}
.accountListBox .accountList .account .removeBtn {
  position: absolute;
  z-index: 3;
  display: none;
  width: 16px;
  height: 16px;
  right: 6px;
  top: 50%;
  margin-top: -8px;
  font-size: 16px;
  text-align: center;
  line-height: 16px;
  color: #999;
  cursor: pointer;
}
.accountListBox .accountList .account .removeBtn:before {
  content: "×";
}
.accountListBox .accountList .account:hover {
  background-color: #3879d9;
  color: #fff;
  font-size: 18px;
}
.accountListBox .accountList .account:hover .removeBtn {
  display: block;
  color: #fff;
}
