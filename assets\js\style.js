'use strict';

var globalVal={
	selfObj:{},//登录当前页面的操作用户ID
	contacts:{},//联系人列表
	chat:{},//聊天列表
	clickTimer:null//单双击时间判断
};
//$(input).value()
(function($){
    $.fn.extend(
    {
        value:function()
        {
        	var $this=$(this);
			var myVal=$this.val();
			var deVal=$this.attr('data-tips');
			if(deVal==undefined){
				deVal='';
			}
			if(myVal==deVal){
				myVal='';
			}
			return myVal;
        }
    });
})(jQuery);

var pinyinCode = {
"a":"\u5391\u5416\u554A\u55C4\u9312\u9515\u963F",
"ai":"\u4F0C\u50FE\u51D2\u53C6\u54C0\u54CE\u5509\u5540\u55CC\u55F3\u560A\u566F\u57C3\u5867\u58D2\u5A3E\u5AD2\u5B21\u5D66\u611B\u61D3\u61DD\u6328\u6371\u6571\u6573\u6639\u66A7\u66D6\u6B38\u6BD0\u6EB0\u6EBE\u6FED\u7231\u7477\u74A6\u764C\u7691\u769A\u76A7\u77B9\u77EE\u7839\u784B\u788D\u7919\u827E\u853C\u8586\u85F9\u8B6A\u8B7A\u8CF9\u8EB7\u9384\u9440\u953F\u9698\u972D\u9744\u9749\u9932\u99A4\u9C6B\u9D31",
"an":"\u4F92\u4FFA\u5111\u5535\u557D\u57B5\u57EF\u5813\u5A69\u5A95\u5B89\u5CB8\u5CD6\u5EB5\u6309\u63DE\u667B\u6697\u6848\u6849\u6C28\u6D1D\u72B4\u73B5\u75F7\u76E6\u76EB\u7F6F\u80FA\u8164\u834C\u83F4\u843B\u844A\u84ED\u8A9D\u8AF3\u8C19\u8C7B\u8C8B\u92A8\u930C\u94F5\u95C7\u968C\u96F8\u978C\u978D\u97FD\u99A3\u9B9F\u9D6A\u9D95\u9E4C\u9EEF",
"ang":"\u5C87\u6602\u663B\u678A\u76CE\u80AE\u91A0\u9AAF",
"ao":"\u50B2\u51F9\u53AB\u55F7\u55F8\u5773\u5787\u58BA\u5961\u5965\u5967\u5AAA\u5ABC\u5AEF\u5C99\u5CB0\u5D85\u5DB4\u5ED2\u6160\u61CA\u6277\u629D\u62D7\u646E\u64D9\u6556\u67EA\u6EF6\u6F9A\u6FB3\u71AC\u720A\u7352\u7353\u7488\u78DD\u7FF1\u7FF6\u7FFA\u8071\u82BA\u851C\u87AF\u8884\u8956\u8B37\u8B38\u8EEA\u9068\u93CA\u93D6\u957A\u969E\u9A41\u9A9C\u9C32\u9CCC\u9DD4\u9F07",
"ba":"\u4EC8\u516B\u53D0\u53ED\u5427\u54F5\u575D\u577A\u57BB\u58A2\u58E9\u593F\u59AD\u5C9C\u5DF4\u5F1D\u6252\u628A\u629C\u62D4\u634C\u6733\u6B1B\u705E\u70A6\u7238\u72AE\u7390\u75A4\u7679\u77F2\u7B06\u7C91\u7D26\u7F62\u7F77\u7F93\u80C8\u82AD\u8307\u83DD\u8686\u8987\u8A59\u8C5D\u8DC1\u8DCB\u8EF7\u91DB\u91DF\u9200\u94AF\u9738\u9776\u98B0\u9B43\u9B5E\u9B81\u9C83\u9C85\u9F25",
"bai":"\u4F70\u5161\u5457\u5504\u5E8D\u62DC\u62DD\u6300\u636D\u63B0\u6446\u64FA\u6557\u67CF\u6822\u7308\u74F8\u767D\u767E\u7A17\u7AE1\u7CA8\u7CBA\u7D54\u85AD\u896C\u8D01\u8D25\u9781\u97DB",
"ban":"\u4F34\u529E\u534A\u5742\u59C5\u5C85\u6011\u626E\u6273\u62CC\u642C\u653D\u6591\u6592\u6604\u670C\u677F\u6E74\u7248\u73ED\u74E3\u74EA\u7622\u764D\u79DA\u7C84\u7D46\u7ECA\u8228\u822C\u8742\u8781\u878C\u8929\u8FA6\u8FAC\u9211\u9261\u94A3\u95C6\u962A\u977D\u9812\u9881\u9B6C",
"bang":"\u508D\u57B9\u585D\u5CC0\u5E2E\u5E47\u5E5A\u5E6B\u5FAC\u6360\u6886\u68D2\u68D3\u699C\u6D5C\u7253\u73A4\u7865\u78C5\u7A16\u7D81\u7E0D\u7ED1\u8180\u8255\u84A1\u868C\u872F\u8B17\u8C24\u90A6\u90AB\u938A\u9551\u97A4",
"bao":"\u4F68\u4FDD\u5124\u525D\u52F9\u52FD\u5305\u5821\u5822\u5831\u5AAC\u5AD1\u5B62\u5B9D\u5BDA\u5BF3\u5BF6\u5FC1\u6009\u62A5\u62B1\u66B4\u66D3\u7172\u7206\u73E4\u7A87\u7B23\u7DE5\u80DE\u82DE\u83E2\u8421\u8446\u8554\u85F5\u8663\u888C\u8912\u8913\u8943\u8C79\u8CF2\u8DB5\u924B\u9464\u94C7\u95C1\u96F9\u974C\u9764\u98F9\u98FD\u9971\u99C2\u9AB2\u9AF1\u9B91\u9C8D\u9CF5\u9D07\u9E28\u9F59\u9F85",
"bia":"\u9adf",
"bo":"\u4EB3\u4EE2\u4F2F\u4FBC\u50E0\u50F0\u5265\u52C3\u535A\u5575\u5697\u58C6\u5B5B\u5B79\u5D93\u5E1B\u613D\u61EA\u62E8\u632C\u640F\u64A5\u64AD\u64D8\u67ED\u6872\u6A97\u6B02\u6CCA\u6CE2\u6D61\u6DFF\u6E24\u6E50\u717F\u7254\u72A6\u72BB\u72DB\u733C\u73BB\u74DD\u74DF\u7676\u7677\u76CB\u7835\u7886\u78FB\u7921\u7934\u79E1\u7B94\u7BA5\u7C19\u7C38\u7CEA\u7D34\u7F3D\u80C9\u8116\u818A\u8236\u824A\u82E9\u83E0\u8467\u8514\u8584\u8617\u86BE\u889A\u88AF\u88B0\u894F\u896E\u8B52\u8C70\u8DDB\u8E23\u8E73\u90E3\u9238\u9251\u9262\u92CD\u939B\u946E\u94B5\u94B9\u94C2\u9548\u9911\u993A\u997D\u998E\u999B\u999E\u99C1\u99EE\u9A4B\u9A73\u9AC6\u9AC9\u9B8A\u9C4D\u9C8C\u9D53\u9E41",
"bei":"\u4FFB\u500D\u505D\u5079\u5099\u50C3\u5317\u5351\u55BA\u5907\u6096\u60B2\u60EB\u6102\u618A\u63F9\u6601\u676F\u686E\u6896\u7119\u726C\u7295\u72C8\u72FD\u73FC\u7432\u76C3\u7891\u789A\u7999\u7CD2\u80CC\u82DD\u84D3\u85E3\u86FD\u88AB\u8919\u8A96\u8C9D\u8D1D\u8EF0\u8F29\u8F88\u90B6\u9101\u9273\u92C7\u943E\u94A1\u9642\u97B4\u9AB3\u9D6F\u9E4E",
"ben":"\u5034\u574C\u5954\u5959\u6379\u64AA\u672C\u6873\u694D\u6CCD\u6E00\u7287\u7356\u755A\u7B28\u82EF\u8CC1\u8D32\u8F3D\u9029\u931B\u951B",
"beng":"\u4F3B\u5623\u57C4\u57F2\u5874\u595F\u5D29\u5D6D\u6CF5\u7423\u742B\u750F\u752D\u75ED\u794A\u7D63\u7DB3\u7E43\u7EF7\u83F6\u8DF0\u8E66\u8FF8\u902C\u93F0\u955A\u958D\u979B",
"bi":"\u4F4A\u4F56\u4FFE\u506A\u5302\u5315\u5421\u54D4\u555A\u55F6\u5752\u581B\u58C1\u5936\u5970\u59A3\u59BC\u5A62\u5B16\u5B36\u5C44\u5E01\u5E63\u5E64\u5E87\u5EB3\u5EE6\u5F0A\u5F3B\u5F3C\u5F43\u5F7C\u5FC5\u602D\u610A\u610E\u655D\u6583\u673C\u6788\u67C0\u67F2\u6890\u6945\u6BD4\u6BD5\u6BD6\u6BD9\u6BF4\u6C98\u6E62\u6ED7\u6EED\u6F77\u6FDE\u714F\u719A\u72F4\u7358\u7359\u73CC\u74A7\u7540\u7541\u7562\u7595\u75AA\u75F9\u75FA\u7680\u7695\u78A7\u7986\u79D5\u7A2B\u7B14\u7B46\u7B5A\u7B85\u7B86\u7BE6\u7BF3\u7C83\u7C8A\u7DBC\u7E2A\u7E74\u7F7C\u805B\u80C7\u8177\u81C2\u822D\u82FE\u835C\u8378\u8406\u841E\u84D6\u84FD\u853D\u859C\u870C\u8795\u8890\u88E8\u8945\u895E\u8963\u89F1\u8A56\u8BD0\u8C4D\u8C8F\u8CB1\u8D14\u8D51\u8DF8\u8E55\u8E83\u8E84\u8F9F\u903C\u907F\u90B2\u9119\u9128\u912A\u924D\u939E\u93CE\u9434\u94CB\u9587\u9589\u959F\u95ED\u965B\u979E\u97B8\u97E0\u98F6\u9946\u999D\u99DC\u9A46\u9AC0\u9AF2\u9B53\u9B85\u9C0F\u9CBE\u9D56\u9DDD\u9DE9\u9F0A\u9F3B",
"bian":"\u4FBF\u533E\u535E\u53D8\u5909\u5CC5\u5F01\u5FA7\u5FED\u60FC\u6241\u6283\u62DA\u63D9\u662A\u6C73\u6C74\u709E\u7178\u7251\u7335\u7371\u7502\u782D\u78A5\u7A28\u7A86\u7B3E\u7BAF\u7C69\u7CC4\u7DE8\u7DF6\u7F0F\u7F16\u8251\u82C4\u8439\u85CA\u8759\u890A\u898D\u8B8A\u8CB6\u8D2C\u8FA1\u8FA7\u8FA8\u8FA9\u8FAA\u8FAB\u8FAE\u8FAF\u8FB9\u904D\u9089\u908A\u91C6\u937D\u959E\u97AD\u9828\u9BFE\u9BFF\u9CCA\u9D18",
"biao":"\u4FF5\u5126\u5882\u5A4A\u5E56\u5F6A\u647D\u6753\u6807\u6A19\u6AA6\u6DF2\u6EEE\u700C\u706C\u719B\u7202\u730B\u762D\u7A6E\u813F\u8198\u81D5\u8508\u85E8\u8868\u88F1\u893E\u8AD8\u8B24\u8D06\u9336\u93E2\u9463\u9556\u9573\u98AE\u98B7\u98C6\u98C7\u98C8\u98CA\u98D1\u98D9\u98DA\u9A43\u9A6B\u9A89\u9AA0\u9ADF\u9C3E\u9CD4\u9E83",
"bie":"\u5225\u522B\u5487\u5F46\u5FB6\u618B\u762A\u765F\u8382\u864C\u86C2\u87DE\u8952\u8E69\u9C49\u9CD6\u9F08\u9F9E",
"bin":"\u50A7\u5110\u5BBE\u5F6C\u6448\u64EF\u658C\u6915\u69DF\u6BA1\u6BAF\u6C1E\u6C43\u6EE8\u6FD2\u6FF1\u6FF5\u7015\u7478\u74B8\u780F\u7E7D\u7F24\u8191\u81CF\u8668\u8819\u8C69\u8C73\u8CD3\u8CD4\u90A0\u944C\u9554\u9726\u986E\u9ACC\u9AD5\u9AE9\u9B02\u9B13\u9B22",
"bing":"\u4E19\u4E26\u4ECC\u4F75\u5002\u504B\u50A1\u5175\u51AB\u51B0\u57AA\u5BCE\u5E76\u5E77\u5EB0\u6032\u62A6\u63A4\u6452\u661E\u663A\u67C4\u681F\u6824\u68B9\u68C5\u6AB3\u6C37\u70B3\u71F7\u75C5\u772A\u7980\u79C9\u7A89\u7ADD\u82EA\u86C3\u8A81\u90B4\u9235\u927C\u92F2\u9643\u9750\u9786\u9905\u9920\u997C\u9BA9",
"bu":"\u4E0D\u4F48\u52CF\u535C\u535F\u5425\u5498\u54FA\u57D7\u57E0\u5CEC\u5E03\u5EAF\u5ECD\u6016\u6091\u6355\u6357\u6661\u6B65\u6B68\u6B69\u735B\u74FF\u7BF0\u7C3F\u8379\u8500\u8865\u88DC\u8AA7\u8E04\u8F50\u900B\u90E8\u90F6\u91AD\u9208\u923D\u949A\u94B8\u9914\u9922\u9CEA\u9D4F\u9E14",
"ca":"\u5693\u56C3\u64E6\u6503\u7924\u7938\u906A",
"cai":"\u4E72\u5038\u5072\u57F0\u5A47\u5BC0\u5F69\u621D\u624D\u63A1\u6750\u68CC\u731C\u776C\u7DB5\u7E29\u7E94\u83DC\u8521\u88C1\u8CA1\u8D22\u8DF4\u8E29\u91C7",
"can":"\u50AA\u510F\u53C2\u53C3\u53C4\u53C5\u55B0\u5646\u5B20\u60E8\u60ED\u6158\u6159\u615A\u61AF\u6701\u6B8B\u6B98\u6E4C\u6FAF\u707F\u71E6\u7218\u74A8\u7A47\u7CB2\u8592\u8695\u8745\u8836\u883A\u8B32\u98E1\u9910\u9A42\u9A96\u9EEA\u9EF2",
"cang":"\u4ED3\u4EFA\u4F27\u5009\u5096\u51D4\u5328\u5D62\u6B0C\u6CA7\u6EC4\u6FF8\u734A\u7F49\u8231\u8259\u82CD\u84BC\u8535\u85CF\u87A5\u8CF6\u9476\u9DAC\u9E27",
"cao":"\u5608\u5D86\u613A\u61C6\u64A1\u64CD\u66F9\u66FA\u69FD\u6F15\u7CD9\u808F\u825A\u8278\u8279\u8349\u84F8\u87AC\u893F\u8959\u9135\u93EA\u9A32\u9F1C",
"ce":"\u4FA7\u5074\u518A\u518C\u5395\u53A0\u5884\u5EC1\u607B\u60FB\u61A1\u62FA\u6547\u6D4B\u6E2C\u755F\u7B27\u7B56\u7B5E\u7B74\u7BA3\u7C0E\u7CA3\u835D\u8417\u8434\u84DB",
"cen":"\u5C91\u5D7E\u68A3\u6D94\u7B12\u81A5",
"ceng":"\u4E7D\u564C\u5C42\u5C64\u5CBE\u5D92\u7320\u785B\u7873\u7AF2\u8E6D\u9A53",
"cha":"\u4F98\u505B\u524E\u53C9\u55CF\u579E\u597C\u59F9\u5BDF\u5C94\u5D56\u5DEE\u6260\u6271\u62C6\u633F\u63D2\u63F7\u643D\u6748\u67E5\u67FB\u69CE\u6AAB\u6C4A\u7339\u7580\u78B4\u79C5\u7D01\u809E\u81FF\u8256\u8286\u832C\u8336\u8869\u8928\u8A0D\u8A67\u8A6B\u8BE7\u8E45\u91F5\u929F\u9364\u9388\u9454\u9497\u9538\u9572\u976B\u9937\u9987",
"chai":"\u4FAA\u5115\u52D1\u558D\u56C6\u67F4\u72B2\u7625\u7961\u831D\u867F\u8806\u8883\u8C7A",
"chan":"\u4E33\u4EA7\u50DD\u5103\u5133\u5181\u522C\u5257\u5277\u5296\u56B5\u56C5\u58E5\u5A75\u5B0B\u5B71\u5D7C\u5DC9\u5E5D\u5E68\u5EDB\u5FCF\u61F4\u61FA\u63BA\u6400\u644C\u6472\u647B\u6519\u65F5\u68B4\u68CE\u6B03\u6BDA\u6D50\u6E79\u6EFB\u6F79\u6F7A\u700D\u703A\u705B\u7158\u71C0\u7351\u7522\u7523\u785F\u78DB\u7985\u79AA\u7C05\u7DFE\u7E5F\u7E75\u7E8F\u7E92\u7F20\u7FBC\u826C\u8487\u8546\u8749\u874A\u87EC\u87FE\u88E7\u895C\u8998\u89C7\u8A97\u8AC2\u8B42\u8B87\u8B92\u8C04\u8C17\u8E94\u8FBF\u913D\u9141\u91A6\u92CB\u92D3\u93DF\u9471\u94F2\u9561\u9575\u95B3\u95E1\u9610\u97C2\u986B\u98A4\u995E\u998B",
"chang":"\u4EE7\u4F25\u5000\u5021\u507F\u50D8\u511F\u514F\u5382\u53B0\u5531\u5617\u5690\u573A\u5834\u5872\u5A3C\u5AE6\u5C1D\u5E38\u5EE0\u5F9C\u6005\u60B5\u60DD\u655E\u660C\u6636\u667F\u66A2\u6919\u6C05\u6DD0\u7316\u739A\u7429\u7452\u747A\u74FA\u751E\u7545\u757C\u80A0\u8178\u8193\u82CC\u83D6\u8407\u87D0\u88EE\u8AAF\u92F9\u92FF\u9329\u93DB\u9520\u9577\u9578\u957F\u95B6\u960A\u97D4\u9B2F\u9BE7\u9C68\u9CB3\u9CBF\u9F1A",
"chao":"\u4EE6\u4EEF\u5435\u5632\u5DD0\u5DE2\u5DE3\u5F28\u600A\u6284\u6641\u671D\u6A14\u6B29\u6F05\u6F6E\u7092\u712F\u717C\u724A\u7727\u7AB2\u7E5B\u7F7A\u8016\u89D8\u8A2C\u8B3F\u8D85\u8F48\u911B\u9214\u949E\u9EA8\u9F02\u9F0C",
"che":"\u4F21\u4FE5\u5056\u52F6\u5513\u577C\u5972\u5C6E\u5F7B\u5FB9\u626F\u63A3\u64A4\u64A6\u6F88\u70E2\u70F2\u7221\u77AE\u7817\u7868\u7869\u8045\u8397\u86FC\u8ECA\u8F66\u8FE0\u9819",
"chen":"\u512D\u55D4\u56AB\u5875\u588B\u5926\u5BB8\u5C18\u5FF1\u6116\u62BB\u63E8\u6550\u6668\u66DF\u68FD\u6987\u6A04\u6AEC\u6C89\u70E5\u7141\u741B\u75A2\u760E\u778B\u7876\u789C\u78E3\u79F0\u7A31\u7D9D\u81E3\u831E\u8380\u8390\u852F\u85BC\u87B4\u886C\u896F\u8A26\u8AC3\u8AF6\u8B13\u8B96\u8C0C\u8C36\u8CDD\u8D02\u8D81\u8D82\u8DBB\u8E38\u8ED9\u8FB0\u8FE7\u90F4\u9202\u95EF\u9648\u9673\u9703\u9DD0\u9E8E\u9F53\u9F54\u9F80",
"cheng":"\u4E1E\u4E57\u4E58\u4FB1\u5041\u5448\u57CE\u57D5\u5818\u584D\u5856\u5A0D\u5BAC\u5CF8\u5EB1\u5F8E\u609C\u60E9\u6186\u6195\u61F2\u6210\u627F\u6330\u6381\u645A\u6490\u6491\u673E\u67A8\u67FD\u68D6\u68E6\u6909\u6A55\u6A59\u6A89\u6A99\u6CDF\u6D06\u6D7E\u6E97\u6F82\u6F84\u7013\u722F\u725A\u73F5\u73F9\u7424\u757B\u7748\u77A0\u7880\u79E4\u7A0B\u7A6A\u7A9A\u7AC0\u7B6C\u7D7E\u7DFD\u8100\u812D\u837F\u8670\u86CF\u87F6\u88CE\u8AA0\u8BDA\u8D6A\u8D6C\u901E\u90D5\u9172\u92EE\u93F3\u93FF\u943A\u94D6\u94DB\u9637\u9757\u9833\u9953\u9A01\u9A2C\u9A8B\u9BCE",
"chi":"\u4F88\u4F99\u4FFF\u50BA\u52C5\u5319\u5376\u53F1\u53FA\u5403\u544E\u54E7\u557B\u55AB\u55E4\u5644\u5758\u5791\u5880\u599B\u5AB8\u5C3A\u5CBB\u5F1B\u5F68\u5F72\u5F73\u605C\u6065\u6157\u618F\u61D8\u62B6\u62F8\u6301\u645B\u6521\u6555\u65A5\u6758\u6B3C\u6B6D\u6B6F\u6C60\u6CDC\u6DD4\u6E41\u6F26\u707B\u70BD\u70FE\u71BE\u74FB\u75D3\u75F4\u75F8\u761B\u7661\u7735\u779D\u7AFE\u7B1E\u7B42\u7BEA\u7C9A\u7CE6\u7D7A\u7FC4\u7FC5\u7FE4\u7FE8\u801B\u803B\u8094\u80E3\u80F5\u815F\u830C\u834E\u8687\u86A9\u86B3\u87AD\u88B2\u88B3\u88ED\u892B\u8A35\u8ABA\u8B18\u8C49\u8CBE\u8D64\u8D7F\u8D8D\u8DA9\u8DEE\u8E1F\u8FDF\u8FE3\u9045\u905F\u906B\u9072\u9253\u9279\u9290\u96F4\u98ED\u994E\u996C\u99B3\u9A70\u9B51\u9D1F\u9D44\u9D92\u9DD8\u9E31\u9EB6\u9ED0\u9F52\u9F5D\u9F7F",
"chong":"\u5145\u51B2\u5603\u57EB\u5BA0\u5BF5\u5D07\u5D08\u5FB8\u5FE1\u6183\u61A7\u63F0\u644F\u6C96\u6D7A\u6F34\u721E\u73EB\u7DDF\u7F7F\u7FC0\u8202\u825F\u833A\u866B\u8769\u87F2\u885D\u8908\u8E56\u9283\u94F3\u9680",
"chou":"\u4E11\u4E12\u4EC7\u4FB4\u4FE6\u5062\u5114\u541C\u5B26\u5E31\u5E6C\u60C6\u6101\u61E4\u62BD\u640A\u677D\u6826\u6906\u6BA0\u71FD\u72A8\u72AB\u7574\u7587\u7633\u7697\u7785\u77C1\u7A20\u7B79\u7BD8\u7C4C\u7D2C\u7D52\u7DA2\u7EF8\u81ED\u81F0\u83D7\u85B5\u88EF\u8A76\u8B8E\u8B90\u8E0C\u8E8A\u905A\u9167\u916C\u919C\u91BB\u96D4\u96E0\u9B57",
"chu":"\u4E8D\u4FF6\u5097\u50A8\u510A\u5132\u51E6\u51FA\u520D\u521D\u53A8\u563C\u57F1\u5904\u5C80\u5E6E\u5EDA\u6035\u61B7\u61E8\u62C0\u640B\u6410\u6474\u654A\u65B6\u6775\u6918\u695A\u696E\u698B\u6A17\u6A71\u6A7B\u6A9A\u6AC9\u6AE5\u6B2A\u6B5C\u6EC0\u6EC1\u6FCB\u7293\u73FF\u7421\u74B4\u77D7\u7840\u790E\u7987\u7ACC\u7AD0\u7BE8\u7D40\u7ECC\u8021\u81C5\u82BB\u84A2\u84AD\u854F\u8655\u870D\u87F5\u891A\u89E6\u89F8\u8AD4\u8C56\u8C60\u8C99\u8D8E\u8E00\u8E70\u8E87\u8E95\u9110\u924F\u92E4\u9504\u95A6\u9664\u96CF\u96DB\u9DB5\u9EDC\u9F63\u9F6D\u9F7C",
"chuai":"\u555C\u562C\u63E3\u8197\u8E39",
"chuan":"\u4E32\u4F20\u50B3\u50E2\u5276\u5598\u570C\u5DDB\u5DDD\u66B7\u693D\u6B42\u6C1A\u6C4C\u732D\u7394\u744F\u7A7F\u7BC5\u821B\u8221\u8229\u8239\u8348\u8CD7\u8F32\u9044\u91E7\u948F\u9569\u9DA8",
"chuang":"\u50B8\u5205\u521B\u5231\u524F\u5259\u5275\u5647\u56F1\u5E8A\u6006\u6134\u6450\u6F3A\u7240\u724E\u7255\u75AE\u7621\u78E2\u7A93\u7A97\u7ABB\u95D6",
"chui":"\u5015\u5439\u5782\u57C0\u6376\u6425\u6858\u68F0\u69CC\u708A\u7BA0\u8144\u83D9\u9318\u939A\u9524\u9672\u9840\u9FA1",
"chun":"\u5046\u5507\u583E\u5A8B\u60F7\u65FE\u6625\u6699\u6776\u693F\u69C6\u6A41\u6AC4\u6D71\u6DF3\u6E7B\u6EE3\u6F18\u7289\u7443\u7776\u7BBA\u7D14\u7EAF\u8123\u83BC\u8405\u8436\u8493\u84F4\u877D\u8822\u8CF0\u8E33\u8F34\u9187\u9195\u931E\u9659\u9BD9\u9C06\u9D89\u9D9E\u9E51",
"chuo":"\u56BD\u5A15\u5A16\u60D9\u6233\u64C9\u6B60\u6DB0\u78ED\u7DBD\u7EF0\u814F\u8DA0\u8E14\u8F1F\u8F8D\u8FB5\u8FB6\u9034\u916B\u9461\u9F6A\u9F71\u9F8A",
"ci":"\u4F4C\u4F7D\u5068\u523A\u523E\u5472\u55ED\u5790\u5832\u5B28\u5E9B\u6148\u673F\u67CC\u6828\u6B21\u6B64\u6CDA\u6FE8\u73BC\u73C1\u74F7\u7506\u75B5\u7689\u78C1\u7920\u7960\u7CCD\u7D58\u7E12\u8308\u8326\u8328\u83BF\u858B\u86D3\u8786\u8800\u8A5E\u8BCD\u8CDC\u8D50\u8D80\u8DD0\u8F9D\u8F9E\u8FA4\u8FAD\u9236\u96CC\u98FA\u9908\u9AB4\u9D1C\u9DBF\u9DC0\u9E5A",
"cong":"\u4E1B\u4ECE\u5306\u53E2\u56EA\u5A43\u5B6E\u5F93\u5F96\u5F9E\u5FE9\u6031\u60A4\u60B0\u6152\u6181\u66B0\u679E\u68C7\u6964\u6A05\u6A2C\u6A37\u6B09\u6DD9\u6F0E\u6F17\u6F40\u6F48\u6F68\u7047\u7127\u719C\u721C\u742E\u747D\u7481\u779B\u7BF5\u7DEB\u7E71\u8061\u8066\u806A\u8070\u82C1\u8310\u8471\u84EF\u8525\u85C2\u87CC\u8AB4\u8B25\u8CE8\u8CE9\u93E6\u9A18\u9A44\u9AA2",
"cou":"\u51D1\u6971\u6E4A\u8160\u8F33\u8F8F",
"cu":"\u4FC3\u5648\u5AA8\u5F82\u61B1\u6B82\u731D\u7604\u762F\u7C07\u7C97\u7E2C\u851F\u89D5\u8A8E\u8D97\u8E27\u8E59\u8E74\u8E75\u9162\u918B\u9863\u9E81\u9E84\u9EA4\u9F00",
"cuan":"\u5DD1\u64BA\u6505\u651B\u6AD5\u6B11\u6BA9\u6C46\u71B6\u7228\u7A73\u7A9C\u7AC4\u7BE1\u7BF9\u7C12\u8E7F\u8EA5\u9479",
"cui":"\u4E7C\u4F1C\u5005\u50AC\u51D7\u5550\u555B\u5894\u5D14\u5D89\u5FF0\u60B4\u615B\u6467\u69B1\u69EF\u6BF3\u6DEC\u6F3C\u7120\u7355\u7480\u75A9\u7601\u76A0\u78EA\u7AC1\u7CB9\u7D23\u7DB7\u7E17\u7F1E\u7FC6\u7FE0\u8103\u8106\u81AC\u81B5\u81CE\u8403\u894A\u8DA1\u93D9\u9847",
"cun":"\u520C\u540B\u58AB\u5B58\u5BF8\u5FD6\u62F5\u6751\u6F8A\u76B4\u7AF4\u7C7F\u8E06\u90A8",
"cuo":"\u5249\u5252\u539D\u590E\u5D6F\u5D73\u632B\u63AA\u6413\u64AE\u68E4\u7473\u75E4\u7749\u77EC\u78CB\u811E\u839D\u83A1\u84AB\u84CC\u8516\u8658\u8E49\u902A\u9073\u919D\u92BC\u932F\u9509\u9519\u9ACA\u9E7A\u9E7E\u9F79",
"da":"\u4EA3\u5273\u5312\u547E\u5491\u54D2\u55D2\u5660\u57AF\u58B6\u5927\u59B2\u601B\u6253\u642D\u6498\u6A7D\u6C93\u6E9A\u709F\u71F5\u7563\u7629\u7714\u7B2A\u7B54\u7E68\u7F8D\u8037\u8345\u8359\u8598\u87FD\u8921\u8A5A\u8DF6\u8E82\u8FBE\u8FCF\u8FD6\u9039\u9054\u9389\u939D\u943D\u977C\u9791\u97C3\u9F96\u9F98",
"dai":"\u4EE3\u4FA2\u50A3\u53C7\u5446\u5454\u5788\u57ED\u5CB1\u5E12\u5E26\u5E2F\u5E36\u5ED7\u5F85\u6020\u61DB\u6234\u66C3\u67CB\u6B79\u6B86\u6C4F\u703B\u7343\u73B3\u7447\u7519\u7C24\u7D3F\u7DFF\u7ED0\u825C\u86AE\u888B\u8976\u8CB8\u8D37\u8E5B\u8ED1\u8EDA\u8EE9\u8F6A\u8FE8\u902E\u9734\u9746\u9B98\u9D0F\u9EDB\u9EF1",
"dan":"\u4E39\u4EB6\u4F14\u4F46\u50E4\u510B\u5210\u52EF\u5330\u5355\u5358\u5556\u5557\u557F\u55AE\u563E\u5649\u56AA\u5989\u5A85\u5E0E\u5F39\u5F48\u60EE\u619A\u61BA\u62C5\u63B8\u64A3\u64D4\u65E6\u67E6\u6B9A\u6BAB\u6C2E\u6C8A\u6CF9\u6DE1\u6FB8\u6FB9\u72DA\u73AC\u74ED\u7514\u758D\u75B8\u7605\u7649\u765A\u7708\u7803\u79AB\u7A9E\u7BAA\u7C1E\u7D1E\u803C\u803D\u8043\u8078\u80C6\u8145\u81BD\u840F\u86CB\u8711\u8874\u891D\u894C\u89DB\u8A11\u8A95\u8BDE\u8D09\u8EAD\u90F8\u9132\u9156\u972E\u9815\u9924\u994F\u99BE\u99F3\u9AE7\u9D20\u9ED5",
"dang":"\u5105\u515A\u51FC\u5679\u5735\u57B1\u58CB\u5A78\u5B95\u5F53\u6321\u64CB\u6529\u6863\u6A94\u6B13\u6C39\u6F52\u6FA2\u7059\u73F0\u7497\u74AB\u74FD\u7576\u76EA\u778A\u7800\u78AD\u7911\u7B5C\u7C1C\u7C39\u8261\u8361\u83EA\u8569\u862F\u87F7\u88C6\u8960\u8B61\u8B9C\u8C20\u8DA4\u903F\u95E3\u96FC\u9EE8",
"dao":"\u5012\u5200\u5202\u5230\u53E8\u5675\u58D4\u5BB2\u5BFC\u5C0E\u5C76\u5C9B\u5CF6\u5D8B\u5D8C\u5DB9\u5FC9\u60BC\u6363\u636F\u6417\u64E3\u6737\u6921\u69DD\u6AA4\u6C18\u7118\u71FE\u74D9\u76D7\u76DC\u7977\u7982\u79B1\u7A32\u7A3B\u7E9B\u7FE2\u7FFF\u8220\u83FF\u885C\u885F\u8E48\u8EC7\u9053\u91D6\u9666\u969D\u96AF\u9B5B\u9C7D",
"de":"\u561A\u5F97\u5FB3\u5FB7\u6074\u60B3\u60EA\u6DC2\u7684\u9340\u951D",
"deng":"\u50DC\u51F3\u5654\u58B1\u5B01\u5D9D\u6225\u6AC8\u706F\u71C8\u7492\u767B\u77AA\u78F4\u7AF3\u7B49\u7C26\u8260\u89B4\u8C4B\u8E6C\u9093\u9127\u9419\u956B\u96A5",
"di":"\u4EFE\u4F4E\u4FE4\u5059\u50C0\u538E\u5467\u5519\u5547\u5572\u5600\u5681\u5730\u5754\u577B\u57CA\u57DE\u5824\u5891\u58AC\u5943\u5A23\u5A82\u5AE1\u5DB3\u5E1D\u5E95\u5EF8\u5F1A\u5F1F\u5F24\u5F7D\u601F\u6178\u62B5\u62DE\u638B\u6455\u654C\u6575\u65F3\u6755\u67A4\u67E2\u688A\u6891\u68E3\u6C10\u6DA4\u6ECC\u6EF4\u710D\u7274\u72C4\u7393\u750B\u7731\u7747\u7825\u78B2\u78FE\u7976\u7998\u7B1B\u7B2C\u7BF4\u7C74\u7CF4\u7DE0\u7F14\u7F9D\u805C\u8091\u8163\u82D0\u82D6\u837B\u83C2\u83E7\u8482\u850B\u8510\u8515\u85E1\u8743\u87AE\u889B\u89BF\u89CC\u89DD\u8A46\u8AE6\u8BCB\u8C1B\u8C74\u8D86\u8E36\u8EE7\u8FEA\u9012\u9013\u905E\u9070\u90B8\u91F1\u926A\u9349\u93D1\u955D\u963A\u9684\u976E\u97AE\u9814\u99B0\u9AB6\u9AE2\u9B61\u9BF3\u9E10",
"dia":"\u55F2",
"dian":"\u4F43\u508E\u5178\u53A7\u5538\u576B\u57AB\u588A\u58C2\u594C\u5960\u5A5D\u5A70\u5D6E\u5DC5\u5DD3\u5DD4\u5E97\u60E6\u6242\u6382\u6527\u6541\u655F\u6923\u69D9\u6A42\u6BBF\u6DC0\u6EC7\u6FB1\u70B9\u73B7\u7414\u7535\u7538\u7628\u765C\u766B\u7672\u7898\u78F9\u7C1F\u84A7\u8547\u8714\u8E2E\u8E4E\u923F\u94BF\u963D\u96FB\u975B\u985A\u985B\u98A0\u9A54\u9EDE\u9F7B",
"diao":"\u4F04\u51CB\u5201\u521F\u53FC\u540A\u595D\u5C4C\u5F14\u5F34\u5F6B\u625A\u6389\u6BA6\u6C48\u7431\u7639\u7797\u7889\u7A8E\u7AB5\u7AE8\u7C13\u84E7\u85CB\u866D\u86C1\u8A0B\u8ABF\u8C03\u8C82\u91E3\u921F\u92B1\u92FD\u9443\u9493\u94DE\u96D5\u96FF\u9B89\u9BDB\u9CB7\u9CED\u9D70\u9F26",
"die":"\u53E0\u54CB\u5551\u558B\u56B8\u57A4\u581E\u5CCC\u5D7D\u5E49\u604E\u60F5\u621C\u6315\u63F2\u6633\u66E1\u66E2\u6B9C\u6C0E\u7239\u7243\u7252\u74DE\u7573\u7582\u7589\u758A\u7723\u7730\u789F\u7D70\u7ED6\u800A\u800B\u80C5\u81F7\u8253\u82F5\u8728\u8776\u890B\u893A\u8A44\u8ADC\u8C0D\u8D83\u8DCC\u8E40\u8FED\u957B\u9C08\u9CBD",
"ding":"\u4E01\u4EC3\u53EE\u5576\u5975\u5B9A\u5D7F\u5E04\u5FCA\u6917\u738E\u753C\u7594\u76EF\u77F4\u7887\u78A0\u78F8\u8035\u8062\u8063\u815A\u8423\u85A1\u8A02\u8BA2\u914A\u91D8\u9320\u9424\u9489\u952D\u976A\u9802\u9841\u9876\u98E3\u9964\u9F0E\u9F11",
"diu":"\u4E1F\u4E22\u92A9\u94E5\u98A9",
"dong":"\u4E1C\u4F97\u5032\u50CD\u51AC\u51BB\u51CD\u52A8\u52D5\u549A\u578C\u57EC\u58A5\u59DB\u5A3B\u5B1E\u5CBD\u5CD2\u5CDD\u5D20\u5D2C\u606B\u61C2\u6219\u630F\u6638\u6771\u680B\u68DF\u6C21\u6C2D\u6D1E\u6DB7\u6E69\u7850\u7B17\u7BBD\u80E8\u80F4\u8156\u82F3\u83C4\u8463\u856B\u8740\u8A77\u8ACC\u8FF5\u9718\u99E7\u9B97\u9BDF\u9D87\u9DAB\u9E2B\u9F15",
"dou":"\u4E67\u515C\u5160\u5245\u543A\u5517\u6296\u6597\u65A3\u6793\u68AA\u6A77\u6BED\u6D62\u75D8\u7AA6\u7AC7\u7BFC\u8130\u8254\u8373\u8538\u86AA\u8C46\u9017\u90D6\u90FD\u9158\u9204\u92C0\u94AD\u9597\u95D8\u9627\u9661\u9916\u997E\u9B25\u9B26\u9B2A\u9B2C\u9B2D",
"du":"\u51DF\u5262\u5335\u53BE\u561F\u5835\u5992\u59AC\u5B3B\u5E3E\u5EA6\u675C\u691F\u6ADD\u6BAC\u6BB0\u6BD2\u6D9C\u6E0E\u6E21\u7006\u724D\u7258\u728A\u72A2\u72EC\u7368\u743D\u74C4\u76BE\u7763\u7779\u79FA\u7B03\u7BE4\u809A\u828F\u8370\u8773\u8799\u8827\u8839\u88FB\u89A9\u8AAD\u8B80\u8B9F\u8BFB\u8C44\u8CED\u8D15\u8D4C\u918F\u9316\u934D\u945F\u9540\u95CD\u9607\u976F\u97C7\u97E3\u97E5\u9A33\u9AD1\u9EE9\u9EF7",
"duan":"\u5073\u5845\u5A8F\u65AD\u65B7\u6934\u6BB5\u6BC8\u7145\u7456\u77ED\u78AB\u7AEF\u7C16\u7C6A\u7DDE\u7F0E\u8011\u8176\u846E\u890D\u8E96\u935B\u9374\u953B",
"dui":"\u514A\u514C\u5151\u53FE\u5796\u5806\u5860\u5BF9\u5BFE\u5C0D\u5D5F\u603C\u619D\u61DF\u6FE7\u7029\u75FD\u7893\u78D3\u794B\u7D90\u85B1\u8B48\u8B75\u9413\u941C\u9566\u961F\u966E\u968A\u9827\u9D2D",
"dun":"\u4F05\u5428\u5678\u56E4\u58A9\u58AA\u58FF\u5E89\u60C7\u619E\u6489\u64B4\u6566\u6A54\u6C8C\u6F61\u7096\u71C9\u729C\u7364\u76F9\u76FE\u7818\u78B7\u7905\u815E\u8733\u8DB8\u8E32\u8E72\u8E7E\u8E89\u9007\u9041\u906F\u920D\u949D\u9813\u987F\u9A50",
"duo":"\u4EB8\u4EDB\u51D9\u5234\u5241\u525F\u526B\u5484\u54C6\u54DA\u55A5\u5689\u56B2\u579B\u579C\u57F5\u5815\u58AE\u58AF\u591A\u591B\u593A\u596A\u5C2E\u5D1C\u5D9E\u60F0\u619C\u6305\u6306\u6387\u6553\u655A\u6560\u656A\u6735\u6736\u67EE\u6857\u692F\u6BF2\u6CB2\u75E5\u7D9E\u7F0D\u8235\u8324\u88F0\u8D93\u8DE2\u8DE5\u8DFA\u8E31\u8EB1\u8EB2\u8EC3\u922C\u9438\u94CE\u964A\u964F\u98FF\u9973\u9B0C\u9BB5\u9D7D",
"e":"\u4FC4\u5054\u50EB\u530E\u537E\u5384\u542A\u5443\u545D\u54A2\u54B9\u5641\u5669\u56EE\u57A9\u580A\u582E\u59B8\u59BF\u59F6\u5A25\u5A3F\u5A40\u5C59\u5C75\u5C8B\u5CC9\u5CE8\u5CE9\u5D3F\u5EC5\u6076\u60AA\u60E1\u6115\u6239\u627C\u6424\u6439\u64DC\u6799\u6AEE\u6B5E\u6B7A\u6D90\u6E42\u73F4\u7427\u7692\u774B\u7808\u7810\u7828\u7846\u78C0\u816D\u82CA\u83AA\u843C\u855A\u8685\u86FE\u8741\u89A8\u8A1B\u8A7B\u8A90\u8AE4\u8B4C\u8B8D\u8BB9\u8C14\u8C5F\u8EDB\u8EF6\u8F6D\u8FD7\u904C\u904F\u907B\u9102\u920B\u92E8\u9354\u9469\u9507\u9537\u95BC\u960F\u9628\u9638\u981E\u981F\u984D\u984E\u989A\u989D\u9913\u9929\u997F\u9A00\u9B64\u9C10\u9C77\u9CC4\u9D48\u9D5D\u9D5E\u9D9A\u9E45\u9E57\u9F76",
"en":"\u5940\u5CCE\u6069\u6441\u717E\u84BD",
"er":"\u4E7B\u4E8C\u4ED2\u4F74\u4F95\u513F\u5150\u5152\u5235\u54A1\u5532\u5C12\u5C13\u5C14\u5CCF\u5F0D\u5F10\u65D5\u682D\u682E\u6A32\u6BE6\u6D0F\u6D31\u723E\u73E5\u7CAB\u800C\u800F\u8033\u804F\u80F9\u834B\u85BE\u8848\u88BB\u8A80\u8CAE\u8CB3\u8D30\u8DB0\u8F00\u8F5C\u8FE9\u9087\u927A\u94D2\u9651\u96AD\u990C\u9975\u99EC\u9AF5\u9B9E\u9C95\u9D2F\u9E38",
"fa":"\u4E4F\u4F10\u4F71\u50A0\u53D1\u57A1\u59C2\u5F42\u6830\u6A43\u6CB7\u6CD5\u704B\u73D0\u743A\u75BA\u767A\u767C\u7782\u781D\u7B4F\u7F5A\u7F70\u7F78\u8337\u85C5\u9197\u9345\u95A5\u9600\u9AEA\u9AEE",
"fan":"\u4EEE\u51E1\u51E2\u51E3\u52EB\u5325\u53CD\u5643\u58A6\u597F\u5B0E\u5B0F\u5B14\u5E06\u5E61\u5FDB\u61A3\u6255\u65D9\u65DB\u674B\u67C9\u68B5\u68E5\u6A0A\u6A4E\u6C3E\u6C4E\u6CDB\u6EFC\u702A\u703F\u70E6\u7169\u71D4\u72AF\u72BF\u74A0\u7548\u756A\u76D5\u77FE\u792C\u7B32\u7B35\u7BC4\u7C53\u7C75\u7DD0\u7E41\u7E59\u7FB3\u7FFB\u81B0\u8224\u8227\u8303\u8543\u85A0\u85E9\u8629\u881C\u894E\u8A09\u8CA9\u8D29\u8E6F\u8ED3\u8EEC\u8F53\u8FBA\u8FD4\u91E9\u9407\u9492\u98BF\u98DC\u98EF\u98F0\u996D\u9C55\u9DED",
"fang":"\u4EFF\u5023\u531A\u574A\u57C5\u580F\u59A8\u5F77\u623F\u653E\u65B9\u65CA\u6609\u6618\u678B\u6DD3\u7265\u74EC\u7706\u7D21\u7EBA\u80AA\u822B\u82B3\u8684\u8A2A\u8BBF\u8DBD\u90A1\u9201\u933A\u94AB\u9632\u9AE3\u9B74\u9C82\u9D0B\u9DAD",
"fei":"\u4FF7\u5255\u532A\u539E\u5420\u5561\u595C\u5983\u5A53\u5A54\u5C5D\u5E9F\u5EC3\u5EE2\u60B1\u6249\u6590\u6632\u6683\u66CA\u670F\u676E\u68D0\u69A7\u6AE0\u6CB8\u6DDD\u6E04\u6FF7\u72D2\u7306\u75BF\u75F1\u7648\u7BDA\u7DCB\u7EEF\u7FE1\u80A5\u80BA\u80D0\u8153\u83F2\u8409\u855C\u855F\u871A\u8730\u87E6\u88F6\u8AB9\u8BFD\u8CBB\u8D39\u9428\u9544\u970F\u9745\u975E\u975F\u98DB\u98DD\u98DE\u9925\u99A1\u9A11\u9A1B\u9BE1\u9CB1\u9F23",
"fen":"\u4EFD\u507E\u50E8\u5206\u5429\u5746\u574B\u575F\u58B3\u594B\u596E\u59A2\u5C8E\u5E09\u5E69\u5F05\u5FFF\u6124\u61A4\u6610\u6706\u678C\u68A4\u68FB\u68FC\u6A68\u6C1B\u6C7E\u7035\u7083\u711A\u71CC\u71D3\u73A2\u7793\u79CE\u7AD5\u7C89\u7CAA\u7CDE\u7D1B\u7EB7\u7F92\u7FB5\u7FC2\u80A6\u81B9\u82AC\u84B6\u8561\u86A0\u86A1\u886F\u8A1C\u8C6E\u8C76\u8EAE\u8F52\u915A\u9216\u943C\u96AB\u96F0\u9934\u9959\u999A\u99A9\u9B75\u9C5D\u9CBC\u9CFB\u9EC2\u9EFA\u9F16\u9F22",
"feng":"\u4E30\u4EF9\u4FF8\u5051\u50FC\u51AF\u51E4\u51E8\u51EC\u51EE\u552A\u5838\u5906\u5949\u59A6\u5BF7\u5C01\u5CEF\u5CF0\u5D36\u6340\u6453\u67AB\u687B\u6953\u6A92\u6CA3\u6CA8\u6D72\u6E22\u6E57\u6E84\u7043\u70FD\u7128\u7148\u728E\u7326\u7412\u74F0\u752E\u75AF\u760B\u76FD\u781C\u78B8\u7BC8\u7D98\u7E2B\u7F1D\u8242\u8451\u8615\u8634\u8702\u8982\u8AF7\u8BBD\u8C50\u8CF5\u8D57\u9022\u9137\u9146\u92D2\u93BD\u93E0\u950B\u974A\u98A8\u98CC\u98CE\u99AE\u9CEF\u9CF3\u9D0C\u9EB7",
"fou":"\u5426\u57BA\u599A\u7D11\u7F36\u7F39\u7F3B\u96EC\u9D00",
"fu":"\u4E40\u4ED8\u4F0F\u4F15\u4FCC\u4FD8\u4FEF\u5069\u5085\u51A8\u51B9\u51EB\u521C\u526F\u5310\u544B\u5452\u5488\u5490\u54F9\u577F\u5798\u590D\u592B\u5987\u598B\u59C7\u5A10\u5A4F\u5A66\u5A8D\u5B5A\u5B75\u5BCC\u5C03\u5CAA\u5CCA\u5DFF\u5E17\u5E45\u5E5E\u5E9C\u5F17\u5F23\u5F7F\u5FA9\u6024\u602B\u61EF\u6276\u629A\u62C2\u62CA\u636C\u64AB\u6577\u65A7\u65C9\u670D\u678E\u67B9\u67CE\u67EB\u683F\u6874\u68F4\u6928\u6931\u6991\u6C1F\u6CED\u6D11\u6D6E\u6DAA\u6ECF\u6F93\u70A5\u70F0\u7124\u7236\u7324\u739E\u73B8\u7408\u752B\u7536\u7549\u7550\u7557\u7641\u76D9\u7806\u7829\u7953\u7954\u798F\u79A3\u79FF\u7A03\u7A2A\u7ACE\u7B26\u7B30\u7B5F\u7B99\u7C20\u7CB0\u7CD0\u7D28\u7D31\u7D3C\u7D65\u7D8D\u7D92\u7DEE\u7E1B\u7EC2\u7ECB\u7F1A\u7F58\u7F66\u7FC7\u80A4\u80D5\u812F\u8150\u8151\u8179\u819A\u8240\u8274\u8299\u82A3\u82BE\u82FB\u8300\u832F\u8342\u8374\u83A9\u83D4\u842F\u844D\u8567\u8659\u86A5\u86A8\u86B9\u86D7\u8705\u8709\u875C\u8760\u876E\u886D\u889D\u88B1\u8907\u8914\u8946\u8965\u8984\u8986\u8A03\u8A42\u8AE8\u8BA3\u8C67\u8CA0\u8CE6\u8CFB\u8D1F\u8D4B\u8D59\u8D74\u8DBA\u8DD7\u8E3E\u8F14\u8F39\u8F3B\u8F85\u8F90\u909E\u90D9\u90DB\u911C\u915C\u91DC\u91E1\u9207\u9258\u925C\u9351\u9362\u961C\u961D\u9644\u965A\u97CD\u97E8\u98AB\u99A5\u99D9\u9A78\u9AF4\u9B34\u9B84\u9B92\u9BB2\u9C12\u9C8B\u9CC6\u9CE7\u9CEC\u9CFA\u9D14\u9D69\u9D9D\u9EA9\u9EAC\u9EB1\u9EB8\u9EFB\u9EFC",
"ga":"\u4F3D\u560E\u5620\u5676\u5C15\u5C1C\u5C2C\u65EE\u738D\u91D3\u9337\u9486\u9B40",
"gai":"\u4E10\u4E62\u4F85\u5303\u5304\u5793\u59DF\u5CD0\u5FCB\u6224\u6461\u6539\u6650\u675A\u6982\u69E9\u69EA\u6E89\u6F11\u74C2\u7561\u76D6\u7974\u7D60\u7D6F\u8344\u8462\u84CB\u8A72\u8BE5\u8C65\u8CC5\u8CCC\u8D45\u90C2\u9223\u9385\u9499\u9654\u9691",
"gan":"\u4E81\u4EE0\u501D\u51CE\u51F2\u5769\u5C32\u5C34\u5C36\u5C37\u5E72\u5E79\u5FD3\u611F\u64C0\u653C\u6562\u65F0\u6746\u67D1\u687F\u69A6\u6A44\u6A8A\u6C75\u6CD4\u6DE6\u6F27\u6F89\u7068\u7395\u7518\u75B3\u76AF\u76F0\u77F8\u79C6\u7A08\u7AFF\u7B34\u7B78\u7C33\u7C93\u7D3A\u7EC0\u809D\u8289\u82F7\u8866\u8A4C\u8D1B\u8D63\u8D76\u8D95\u8FC0\u9150\u9AAD\u9B50\u9C64\u9CE1\u9CF1",
"gang":"\u5188\u51AE\u521A\u525B\u5808\u583D\u5C97\u5CA1\u5D17\u6206\u6207\u6386\u6760\u68E1\u69D3\u6E2F\u7135\u7268\u7285\u7598\u77FC\u7B7B\u7DB1\u7EB2\u7F38\u7F41\u7F53\u7F61\u809B\u91ED\u92FC\u93A0\u94A2\u962C",
"gong":"\u4F9B\u515D\u5163\u516C\u5171\u529F\u5311\u5314\u53B7\u551D\u5868\u5BAB\u5BAE\u5DE5\u5DE9\u5E4A\u5EFE\u5F13\u606D\u6129\u6150\u62F1\u62F2\u653B\u675B\u6831\u6C5E\u7195\u73D9\u78BD\u7BE2\u7CFC\u7FBE\u80B1\u86A3\u89E5\u89F5\u8CA2\u8D11\u8D21\u8EAC\u8EB3\u8F01\u978F\u9AF8\u9F8F\u9F94\u9F9A",
"gao":"\u52C2\u543F\u544A\u5930\u5CFC\u641E\u66A0\u6772\u69C0\u69C1\u69D4\u69F9\u6A70\u6ABA\u6ADC\u6EDC\u7354\u768B\u7690\u776A\u777E\u796E\u7970\u799E\u7A01\u7A3E\u7A3F\u7B76\u7BD9\u7CD5\u7E1E\u7F1F\u7F94\u7F99\u818F\u81EF\u83D2\u85C1\u85F3\u8AA5\u8BF0\u90DC\u92EF\u93AC\u9506\u9550\u97DF\u993B\u9AD8\u9AD9\u9DCE\u9DF1\u9F1B",
"ge":"\u4E2A\u4F6E\u500B\u5272\u530C\u5404\u5444\u54E5\u54FF\u55DD\u572A\u5865\u5F41\u6105\u6208\u6213\u6228\u630C\u6401\u643F\u64F1\u654B\u683C\u69C5\u6ACA\u6B4C\u6EC6\u6ED2\u726B\u7271\u72B5\u7366\u7599\u784C\u7B87\u7D07\u7EA5\u8090\u80F3\u8188\u81F5\u8238\u8316\u845B\u867C\u86D2\u86E4\u88BC\u88D3\u89E1\u8AFD\u8B0C\u8F35\u8F55\u927B\u9398\u93B6\u94EC\u9549\u95A3\u95A4\u9601\u9694\u9769\u9788\u97B7\u97D0\u97DA\u9A14\u9ABC\u9B32\u9BAF\u9C2A\u9D10\u9D1A\u9D3F\u9E3D",
"gei":"\u7D66\u7ED9",
"gen":"\u4E98\u54CF\u63EF\u6404\u6839\u826E\u831B\u8DDF",
"geng":"\u4E99\u522F\u54FD\u5579\u55BC\u55F0\u57C2\u5829\u5CFA\u5E9A\u632D\u63B6\u66F4\u6897\u6929\u6D6D\u713F\u754A\u7D5A\u7D86\u7DEA\u7E06\u7EE0\u7FAE\u7FB9\u8015\u803F\u8384\u83EE\u8CE1\u8D53\u90E0\u9ABE\u9BC1\u9CA0\u9D8A\u9E52",
"gou":"\u4F5D\u5193\u52FE\u5778\u57A2\u591F\u5920\u59E4\u5ABE\u5CA3\u5F40\u6406\u6480\u6784\u67B8\u69CB\u6C9F\u6E9D\u7179\u72D7\u73BD\u7B31\u7BDD\u7C3C\u7DF1\u7F11\u8007\u8008\u8009\u82DF\u8329\u86BC\u88A7\u8920\u89AF\u89CF\u8A3D\u8A6C\u8BDF\u8C7F\u8CFC\u8D2D\u9058\u920E\u9264\u94A9\u96CA\u97B2\u97DD",
"gu":"\u4F30\u50A6\u50F1\u51C5\u53E4\u5495\u5502\u5503\u5552\u560F\u56FA\u580C\u5903\u59D1\u5AF4\u5B64\u5C33\u5CE0\u5D13\u5D2E\u6018\u6132\u6262\u6545\u67E7\u688F\u68DD\u6996\u69BE\u6A6D\u6BC2\u6C69\u6CBD\u6CD2\u6DC8\u6FF2\u7014\u7138\u726F\u727F\u75FC\u76B7\u76EC\u77BD\u7872\u797B\u7A12\u7A40\u7B1F\u7B8D\u7B9B\u7BD0\u7CD3\u7E0E\u7F5B\u7F5F\u7F96\u80A1\u8135\u81CC\u83C7\u83F0\u84C7\u85A3\u86C4\u86CA\u86CC\u8831\u89DA\u8A41\u8BC2\u8C37\u8EF1\u8EF2\u8F42\u8F71\u8F9C\u9027\u9164\u9232\u9237\u932E\u94B4\u9522\u96C7\u980B\u9867\u987E\u9936\u9989\u9AA8\u9B95\u9BDD\u9CB4\u9D23\u9D60\u9DBB\u9E2A\u9E44\u9E58\u9F13\u9F14",
"gua":"\u518E\u522E\u5250\u526E\u5280\u5366\u53E7\u5471\u5569\u576C\u5BE1\u6302\u639B\u6B44\u713B\u7171\u74DC\u7D53\u7DFA\u7F63\u7F6B\u80CD\u82FD\u8902\u8A7F\u8BD6\u8D8F\u92BD\u98AA\u98B3\u9A27\u9D30\u9E39",
"guai":"\u4E56\u53CF\u592C\u602A\u6060\u62D0\u67B4\u67FA\u7B89",
"guan":"\u4E31\u500C\u5173\u51A0\u535D\u5B98\u60B9\u60BA\u60EF\u6163\u63BC\u645C\u68FA\u6A0C\u6BCC\u6CF4\u6DAB\u6F45\u704C\u721F\u742F\u74D8\u75EF\u761D\u764F\u76E5\u77D4\u7936\u797C\u7AA4\u7B66\u7BA1\u7F46\u7F50\u8218\u8416\u8484\u898C\u89B3\u89C0\u89C2\u8CAB\u8D2F\u8E80\u8F28\u9066\u9327\u93C6\u9475\u95A2\u95D7\u95DC\u96DA\u9928\u9986\u9C25\u9C5E\u9C79\u9CCF\u9CE4\u9E1B\u9E73",
"guang":"\u4F8A\u4FC7\u50D9\u5149\u54A3\u5799\u59EF\u5E7F\u5E83\u5EE3\u6497\u6844\u6B1F\u6D38\u706E\u7097\u709A\u709B\u70E1\u72B7\u7377\u73D6\u7844\u80F1\u81E6\u81E9\u832A\u8F04\u901B\u92A7\u9EC6",
"gui":"\u4E80\u4F79\u523D\u523F\u528A\u528C\u5326\u532D\u53AC\u572D\u579D\u59AB\u59FD\u5AAF\u5AE2\u5B00\u5B84\u5DB2\u5DC2\u5E30\u5E8B\u5EAA\u5F52\u6051\u646B\u648C\u6530\u6531\u660B\u6677\u67DC\u6842\u691D\u6922\u69F6\u69FB\u69FC\u6AC3\u6AF7\u6B78\u6C3F\u6E40\u6E8E\u7085\u73EA\u7470\u749D\u74CC\u7678\u7688\u7786\u77A1\u77B6\u7845\u796A\u79AC\u7A90\u7B40\u7C02\u7C0B\u80FF\u81AD\u8325\u84D5\u86EB\u87E1\u88BF\u8958\u898F\u89C4\u89E4\u8A6D\u8BE1\u8CB4\u8D35\u8DEA\u8ECC\u8F68\u90BD\u90CC\u95A8\u95FA\u9652\u97BC\u9A29\u9B36\u9B39\u9B3C\u9BAD\u9C56\u9C65\u9C91\u9CDC\u9F9C\u9F9F",
"gun":"\u4E28\u60C3\u68CD\u68DE\u6EDA\u6EFE\u74AD\u7754\u7774\u78D9\u7DC4\u7DF7\u7EF2\u84D8\u8509\u886E\u889E\u88F7\u8B34\u8F25\u8F8A\u9B8C\u9BC0\u9CA7",
"guo":"\u5459\u54BC\u556F\u5613\u56EF\u56F6\u56FB\u56FD\u5700\u570B\u57DA\u581D\u588E\u5D1E\u5E3C\u5E57\u5F49\u5F4D\u60C8\u6156\u63B4\u6451\u679C\u6901\u6947\u69E8\u6DC9\u6F0D\u6FC4\u7313\u7611\u7CBF\u7DB6\u8052\u805D\u8142\u8158\u8195\u83D3\u852E\u8662\u873E\u8748\u87C8\u88F9\u8901\u8F20\u8FC7\u904E\u90ED\u921B\u934B\u9439\u9505\u991C\u9983\u9998",
"ha":"\u4E37\u54C8\u598E\u927F\u94EA",
"hai":"\u4E64\u4EA5\u548D\u55D0\u55E8\u56A1\u5870\u5B69\u5BB3\u6C26\u6D77\u70F8\u80F2\u917C\u91A2\u9900\u995A\u99ED\u99F4\u9A87\u9AB8",
"han":"\u4F44\u50BC\u516F\u51FD\u51FE\u5388\u542B\u5481\u54FB\u5505\u558A\u5705\u57BE\u5A22\u5AE8\u5BD2\u5C7D\u5D21\u5D45\u608D\u61A8\u61BE\u625E\u634D\u6496\u64BC\u65F1\u6657\u6658\u6665\u66B5\u6892\u6C49\u6C57\u6D5B\u6D6B\u6D86\u6DB5\u6DCA\u6F22\u6F8F\u701A\u710A\u7113\u71AF\u7233\u7302\u7400\u751D\u7694\u7745\u7B68\u7F55\u7FF0\u80A3\u839F\u83E1\u850A\u862B\u8677\u86B6\u86FF\u872C\u872D\u8792\u8B40\u8C3D\u8C43\u9097\u90AF\u9163\u91EC\u92B2\u92CE\u92E1\u9588\u95EC\u96D7\u97D3\u97E9\u9807\u9837\u9844\u9878\u9894\u99A0\u99AF\u99FB\u9B2B\u9B7D\u9DBE\u9F3E",
"hen":"\u4F77\u5F88\u6068\u62EB\u72E0\u75D5\u8A6A\u978E",
"hang":"\u57B3\u592F\u5994\u65BB\u676D\u6C86\u7B10\u7B55\u7D4E\u7ED7\u822A\u82C0\u86A2\u8CA5\u8FD2\u980F\u9883\u9B67",
"hao":"\u5090\u512B\u515E\u53F7\u54E0\u55E5\u5637\u5651\u5686\u568E\u58D5\u597D\u604F\u608E\u660A\u6626\u6667\u66A4\u66AD\u66CD\u6903\u6BEB\u6D69\u6DCF\u6EC8\u6F94\u6FE0\u704F\u705D\u7346\u734B\u7693\u769C\u769E\u76A1\u76A5\u79CF\u7AD3\u7C47\u8017\u8055\u8320\u84BF\u8583\u8585\u85A7\u865F\u869D\u8814\u8B79\u8C6A\u90DD\u9865\u98A2\u9C1D",
"he":"\u4F55\u4F6B\u52BE\u5408\u5475\u548A\u548C\u54EC\u555D\u559D\u55C3\u55EC\u578E\u58D1\u59C0\u5BC9\u5CC6\u60D2\u62B2\u6546\u66F7\u67C7\u6838\u6941\u6B31\u6BFC\u6CB3\u6DB8\u6E2E\u6E7C\u6F95\u7103\u7142\u7186\u7187\u71FA\u7200\u72E2\u764B\u76AC\u76C7\u76C9\u76CD\u76D2\u788B\u7909\u79BE\u79F4\u7BD5\u7C7A\u7CAD\u7FEE\u7FEF\u8377\u83CF\u8402\u86B5\u879B\u881A\u8894\u8910\u8988\u8A36\u8A38\u8A65\u8BC3\u8C88\u8C89\u8CC0\u8D3A\u8D6B\u90C3\u924C\u9449\u95A1\u95D4\u9602\u9616\u96BA\u974D\u974E\u974F\u97A8\u981C\u988C\u9978\u9B7A\u9C84\u9DA1\u9DB4\u9E16\u9E56\u9E64\u9EA7\u9F43\u9F55\u9F81\u9FA2",
"hei":"\u563F\u5B12\u6F76\u9ED1\u9ED2",
"heng":"\u4EA8\u54FC\u5548\u56CD\u583C\u59EE\u6046\u6052\u6099\u6841\u6A2A\u6A6B\u6DA5\u70C6\u73E9\u80FB\u811D\u8605\u8861\u9445\u9D34\u9D46\u9E3B",
"hong":"\u4EDC\u53FF\u5430\u54C4\u55CA\u569D\u57AC\u5985\u5A02\u5B8F\u5B96\u5F18\u5F4B\u63C8\u6494\u664E\u6C6F\u6CD3\u6D2A\u6D64\u6E31\u6E39\u6F42\u6F8B\u6F92\u7074\u70D8\u7122\u7392\u739C\u7854\u7861\u7AD1\u7AE4\u7BCA\u7CA0\u7D05\u7D18\u7D2D\u7D8B\u7EA2\u7EAE\u7FC3\u7FDD\u803E\u82F0\u836D\u8452\u8453\u857B\u85A8\u8679\u8A07\u8A0C\u8BA7\u8C39\u8C3C\u8C3E\u8EE3\u8F37\u8F5F\u8F70\u921C\u9277\u92BE\u92D0\u9367\u958E\u95A7\u95C0\u95C2\u95F3\u9710\u971F\u9783\u9B28\u9B5F\u9D3B\u9E3F\u9EC9\u9ECC",
"hou":"\u4FAF\u5019\u539A\u540E\u543C\u543D\u5589\u5795\u5820\u5E3F\u5F8C\u6D09\u72BC\u7334\u760A\u777A\u77E6\u7BCC\u7CC7\u7FED\u8454\u8C5E\u9005\u90C8\u9107\u9297\u936D\u9931\u9ABA\u9B9C\u9BF8\u9C5F\u9C8E\u9C98\u9F41",
"hu":"\u4E4E\u4E55\u4E92\u51B1\u51B4\u5322\u532B\u547C\u552C\u553F\u5596\u5611\u561D\u569B\u56EB\u5780\u58F6\u58F7\u58FA\u5A5F\u5AA9\u5AED\u5AEE\u5BE3\u5CB5\u5E0D\u5E60\u5F16\u5F27\u5FFD\u6019\u6057\u60DA\u6236\u6237\u6238\u623D\u6248\u6287\u62A4\u6430\u6462\u659B\u6608\u6612\u66F6\u6791\u695C\u69F2\u69F4\u6B51\u6C7B\u6C8D\u6CAA\u6CD8\u6D52\u6DF4\u6E56\u6EEC\u6EF8\u6EF9\u702B\u70C0\u7100\u7173\u71A9\u72D0\u7322\u7425\u745A\u74E0\u74F3\u795C\u7B0F\u7B8E\u7BB6\u7C04\u7C90\u7CCA\u7D57\u7D94\u7E20\u80E1\u81B4\u8290\u82F8\u8400\u846B\u851B\u8530\u864D\u864E\u8656\u865D\u8774\u879C\u885A\u89F3\u8B3C\u8B77\u8EE4\u8F77\u9120\u9190\u933F\u9359\u9378\u96D0\u96FD\u97C4\u9800\u9836\u992C\u9B0D\u9B71\u9BF1\u9C17\u9C6F\u9CE0\u9CF8\u9D98\u9DA6\u9DAE\u9E0C\u9E55\u9E71",
"hua":"\u5212\u5283\u5316\u534E\u54D7\u5629\u57D6\u59E1\u5A72\u5A73\u5AFF\u5B05\u5D0B\u6466\u64B6\u6779\u6866\u691B\u69EC\u6A3A\u6ED1\u6F85\u733E\u748D\u753B\u756B\u7575\u7874\u78C6\u7CC0\u7E63\u8219\u82B1\u82B2\u83EF\u8550\u8624\u8633\u8796\u89DF\u8A71\u8AAE\u8AD9\u8AE3\u8B41\u8BDD\u92D8\u9335\u93F5\u94E7\u9A4A\u9A85\u9DE8\u9ECA",
"huai":"\u54B6\u574F\u58CA\u58DE\u5F8A\u6000\u61D0\u61F7\u69D0\u6AF0\u6DEE\u7024\u8032\u8639\u863E\u8922\u8931\u8E1D",
"huan":"\u5524\u559A\u559B\u56BE\u581A\u5942\u5950\u5BA6\u5BCF\u5BF0\u5CD8\u5D48\u5E7B\u60A3\u610C\u61C1\u61FD\u6362\u63DB\u64D0\u650C\u6853\u6899\u69F5\u6B22\u6B25\u6B53\u6B61\u6D39\u6D63\u6DA3\u6E19\u6F36\u6FA3\u6FB4\u70C9\u7115\u7165\u72DF\u737E\u73AF\u744D\u74B0\u74DB\u75EA\u7613\u7746\u77A3\u7CEB\u7D59\u7D84\u7DE9\u7E6F\u7F13\u7F33\u7FA6\u8092\u8341\u8408\u8411\u85E7\u8B99\u8C62\u8C72\u8C86\u8C9B\u8F10\u8F58\u8FD8\u902D\u9084\u90C7\u9144\u9370\u9436\u953E\u956E\u95E4\u961B\u96C8\u9A69\u9B1F\u9BC7\u9BF6\u9C00\u9CA9\u9D05\u9D4D\u9E6E",
"huang":"\u505F\u5164\u51F0\u55A4\u582D\u5843\u58B4\u595B\u5A93\u5BBA\u5D32\u5DDF\u5E4C\u5FA8\u6033\u604D\u60F6\u6130\u614C\u63D8\u6643\u6644\u66C2\u671A\u697B\u69A5\u6ACE\u6E5F\u6EC9\u6F62\u70BE\u714C\u7180\u71BF\u735A\u745D\u749C\u7640\u7687\u769D\u76A9\u78FA\u7A54\u7BC1\u7C27\u7E28\u8093\u824E\u8352\u845F\u8757\u87E5\u8841\u8A64\u8AFB\u8B0A\u8C0E\u8DAA\u9051\u9360\u93A4\u9404\u953D\u968D\u97F9\u992D\u9A1C\u9C09\u9C51\u9CC7\u9DEC\u9EC3\u9EC4",
"hui":"\u4F1A\u4F6A\u50E1\u5136\u532F\u5349\u54B4\u54D5\u5599\u5612\u5645\u5655\u5666\u5696\u56D8\u56DE\u56EC\u571A\u5A4E\u5A88\u5B48\u5BED\u5C77\u5E51\u5EFB\u5EFD\u5F57\u5F59\u5F5A\u5FBB\u5FBD\u605A\u605B\u6062\u6075\u6094\u60E0\u6167\u6193\u61F3\u62FB\u6325\u63EE\u649D\u6656\u6666\u6689\u66B3\u6703\u6867\u694E\u69E5\u6A5E\u6A85\u6A93\u6A9C\u6AD8\u6BC0\u6BC1\u6BC7\u6C47\u6CCB\u6D03\u6D04\u6D4D\u6E4F\u6ED9\u6F53\u6FAE\u7008\u7070\u7073\u70E0\u70E3\u70E9\u70EA\u7147\u71EC\u71F4\u7369\u73F2\u743F\u74A4\u74AF\u75D0\u7623\u7773\u77BA\u7988\u79FD\u7A62\u7BF2\u7D75\u7E62\u7E6A\u7ED8\u7F0B\u7FD9\u7FDA\u7FEC\u7FFD\u8294\u8334\u835F\u8527\u8559\u8588\u8589\u85F1\u867A\u8698\u86D4\u86D5\u8716\u879D\u87EA\u8886\u8918\u8A6F\u8A7C\u8AA8\u8AF1\u8B53\u8B6D\u8B6E\u8B7F\u8BB3\u8BD9\u8BF2\u8C57\u8CC4\u8D3F\u8F1D\u8F89\u8FF4\u9025\u93F8\u942C\u95E0\u9613\u9693\u96B3\u9767\u97E2\u982E\u986A\u992F\u9BB0\u9C34\u9EBE",
"hun":"\u4FD2\u5031\u5702\u5A5A\u5FF6\u60DB\u60FD\u6141\u638D\u660F\u662C\u68D4\u6B99\u6D51\u6DBD\u6DF7\u6E3E\u6EB7\u711D\u7767\u776F\u7E49\u8364\u8477\u89E8\u8AE2\u8BE8\u8F4B\u95BD\u960D\u991B\u9984\u9B42\u9F32",
"huo":"\u4F19\u4F78\u4FF0\u5268\u5290\u5419\u549F\u55C0\u5684\u56AF\u56BF\u5925\u593B\u596F\u60D1\u6216\u6347\u639D\u64ED\u6509\u65E4\u66E4\u6AB4\u6C8E\u6D3B\u6E71\u6F37\u6FE9\u7016\u706B\u7372\u7668\u7713\u77C6\u77D0\u7978\u798D\u79EE\u79F3\u7A6B\u8020\u802F\u81DB\u8267\u83B7\u84A6\u85FF\u8816\u8B0B\u8C41\u8CA8\u8D27\u90A9\u9225\u9343\u944A\u94AC\u952A\u956C\u9584\u96D8\u970D\u9743\u9A1E",
"ji":"\u4E0C\u4E2E\u4E69\u4E9F\u4EBC\u4F0B\u4F0E\u4F76\u5048\u506E\u50DF\u517E\u5180\u51E0\u51FB\u5209\u520F\u5242\u525E\u5264\u5291\u52E3\u5359\u5373\u537D\u53CA\u53DD\u53FD\u5409\u54AD\u54DC\u5527\u559E\u55D8\u5630\u568C\u573E\u5756\u578D\u57FA\u5849\u588D\u58BC\u5980\u5993\u59DE\u59EC\u5AC9\u5B63\u5BC2\u5BC4\u5C50\u5C8C\u5CDC\u5D46\u5D47\u5D74\u5DAF\u5DF1\u5E7E\u5EB4\u5EED\u5F50\u5F51\u5F76\u5F9B\u5FCC\u5FE3\u6025\u60B8\u60CE\u6131\u61BF\u61FB\u621F\u6222\u6280\u6324\u638E\u63E4\u6483\u64A0\u64CA\u64E0\u6532\u6567\u65E1\u65E2\u65E3\u66A8\u66A9\u66C1\u673A\u6781\u6785\u689E\u68D8\u6956\u696B\u6975\u69C9\u69E3\u6A2D\u6A5F\u6A76\u6A95\u6A9D\u6AB5\u6AC5\u6B9B\u6BC4\u6C72\u6CF2\u6D0E\u6D4E\u6E08\u6E52\u6F03\u6F08\u6F57\u6FC0\u6FC8\u6FDF\u7031\u710F\u7284\u72B1\u72E4\u7391\u74A3\u74BE\u7578\u757F\u75BE\u75F5\u7620\u7660\u766A\u768D\u77F6\u78EF\u796D\u799D\u79A8\u79EF\u7A18\u7A29\u7A37\u7A3D\u7A44\u7A4A\u7A4D\u7A56\u7A67\u7B04\u7B08\u7B53\u7B95\u7BBF\u7C0A\u7C4D\u7CED\u7D00\u7D12\u7D1A\u7D99\u7DDD\u7E18\u7E3E\u7E4B\u7E6B\u7E7C\u7EA7\u7EAA\u7EE7\u7EE9\u7F09\u7F7D\u7F81\u7F87\u7F88\u8024\u802D\u808C\u810A\u8128\u818C\u81EE\u8265\u82A8\u82B0\u82B6\u830D\u8415\u846A\u84BA\u84DF\u8507\u8540\u857A\u858A\u860E\u862E\u863B\u8640\u866E\u874D\u878F\u87E3\u88DA\u8940\u894B\u8989\u898A\u89AC\u89CA\u89D9\u89ED\u8A08\u8A18\u8A8B\u8AC5\u8B4F\u8B64\u8BA1\u8BA5\u8BB0\u8C3B\u8CEB\u8CF7\u8D4D\u8D8C\u8DE1\u8DFB\u8DFD\u8E16\u8E50\u8E5F\u8E8B\u8EA4\u8EB8\u8F2F\u8F5A\u8F91\u8FF9\u90C6\u913F\u9288\u92A1\u9324\u9353\u93F6\u9416\u9447\u9459\u9645\u969B\u96AE\u96C6\u96DE\u96E6\u96E7\u9701\u9735\u973D\u978A\u97BF\u97F2\u98E2\u9951\u9965\u9A65\u9AA5\u9AFB\u9B62\u9B86\u9BDA\u9BFD\u9C36\u9C3F\u9C40\u9C6D\u9C7E\u9C9A\u9CAB\u9CEE\u9D4B\u9D8F\u9DBA\u9DC4\u9DD1\u9E04\u9E21\u9E61\u9E82\u9F4C\u9F4E\u9F4F\u9F51",
"jia":"\u4EF7\u4F73\u5047\u50A2\u50F9\u52A0\u53DA\u550A\u55E7\u5609\u573F\u57C9\u5939\u593E\u5A7D\u5AC1\u5B8A\u5BB6\u5CAC\u5E4F\u5FA6\u605D\u621B\u621E\u6274\u62B8\u62C1\u659A\u659D\u67B6\u67B7\u689C\u6935\u698E\u69A2\u69DA\u6A9F\u6BE0\u6CC7\u6D43\u6D79\u728C\u7333\u73BE\u73C8\u7532\u75C2\u7615\u7A3C\u7B33\u7CD8\u801E\u80DB\u8125\u8175\u835A\u83A2\u846D\u86F1\u86FA\u8888\u88B7\u88CC\u8C6D\u8C91\u8CC8\u8D3E\u8DCF\u8DF2\u8FE6\u90CF\u90DF\u9240\u926B\u92CF\u93B5\u94BE\u94D7\u9553\u982C\u9830\u988A\u9904\u99D5\u9A7E\u9D36\u9D4A\u9E9A",
"jian":"\u4EF6\u4F9F\u4FED\u4FF4\u5039\u5065\u50ED\u5109\u517C\u51BF\u51CF\u5251\u5263\u526A\u5271\u528D\u528E\u5292\u5294\u56CF\u56DD\u575A\u5805\u58B9\u5978\u59E6\u59E7\u5BCB\u5C16\u5E34\u5E75\u5EFA\u5F3F\u5F45\u5FA4\u60E4\u620B\u6214\u6229\u622C\u62E3\u6338\u6361\u63C0\u63C3\u641B\u64BF\u64F6\u65D4\u6695\u67A7\u67EC\u682B\u6898\u68C0\u691C\u6937\u693E\u6957\u6997\u69DB\u6A2B\u6A7A\u6AA2\u6ABB\u6AFC\u6B7C\u6BB1\u6BB2\u6BFD\u6D0A\u6DA7\u6E10\u6E1B\u6E54\u6E55\u6E85\u6F38\u6F97\u6FFA\u7010\u7033\u7038\u703D\u714E\u719E\u71B8\u724B\u726E\u728D\u730F\u73AA\u73D4\u744A\u7450\u76D1\u76E3\u7751\u7777\u77AF\u77B7\u77BC\u7877\u788A\u78B1\u78F5\u7900\u7906\u791B\u7B15\u7B3A\u7B67\u7B80\u7B8B\u7BAD\u7BEF\u7C21\u7C5B\u7CCB\u7D78\u7DD8\u7E11\u7E5D\u7E6D\u7F04\u7F23\u7FE6\u807B\u80A9\u8171\u81F6\u8230\u8266\u8270\u8271\u8327\u8350\u83C5\u83FA\u844C\u844F\u8465\u84B9\u8551\u8573\u85A6\u85C6\u8643\u87B9\u8812\u88B8\u88E5\u8947\u8949\u897A\u898B\u89B5\u89B8\u89C1\u8A43\u8AD3\u8AEB\u8B07\u8B2D\u8B7C\u8B7E\u8C0F\u8C2B\u8C5C\u8C63\u8CCE\u8CE4\u8D31\u8D9D\u8DBC\u8DC8\u8DF5\u8E10\u8E3A\u8E47\u8F5E\u91FC\u9274\u92FB\u9373\u9375\u93E9\u9417\u9427\u9451\u9452\u946C\u946F\u9473\u950F\u952E\u9592\u9593\u95F4\u976C\u97AC\u97AF\u97C0\u97C9\u991E\u9930\u996F\u99A2\u9B0B\u9C0E\u9C14\u9C1C\u9C39\u9CA3\u9CD2\u9CFD\u9D73\u9DBC\u9E63\u9E78\u9E7B\u9E7C\u9E89",
"jiang":"\u508B\u50F5\u52E5\u531E\u5320\u58C3\u5905\u5956\u5968\u596C\u59DC\u5C06\u5C07\u5D79\u5F1C\u5F36\u5F4A\u646A\u647E\u6762\u6868\u69F3\u6A7F\u6AE4\u6BAD\u6C5F\u6D1A\u6D46\u6EF0\u6F3F\u729F\u734E\u7555\u757A\u7585\u7586\u7913\u7CE1\u7CE8\u7D73\u7E6E\u7EDB\u7F30\u7FDE\u8029\u8199\u8333\u8441\u848B\u8523\u8591\u8780\u87BF\u88B6\u8B1B\u8B3D\u8BB2\u8C47\u9171\u91A4\u91AC\u964D\u97C1\u985C\u9C42\u9CC9",
"jiao":"\u4EA4\u4F7C\u4FA5\u50E5\u50EC\u510C\u527F\u528B\u52E6\u53EB\u544C\u5602\u5604\u5626\u564D\u566D\u59E3\u5A07\u5B0C\u5B13\u5B42\u5CE4\u5CE7\u5D95\u5DA0\u5DA3\u5FBC\u618D\u630D\u6322\u6341\u6405\u6477\u649F\u64B9\u652A\u654E\u6559\u656B\u657D\u657F\u65A0\u6648\u669E\u66D2\u6912\u6AF5\u6D47\u6E6B\u6E6C\u6ED8\u6F16\u6F50\u6F86\u705A\u70C4\u7126\u7133\u714D\u71CB\u72E1\u7365\u73D3\u74AC\u768E\u76A6\u76AD\u77EB\u77EF\u7901\u7A5A\u7A8C\u7A96\u7B05\u7B4A\u7C25\u7D5E\u7E73\u7E90\u7EDE\u7F34\u80F6\u811A\u8173\u81A0\u81B2\u81EB\u827D\u8281\u832D\u832E\u8549\u85E0\u8660\u86DF\u87DC\u87ED\u89D2\u8A06\u8B51\u8B65\u8CCB\u8DAD\u8DE4\u8E0B\u8F03\u8F47\u8F4E\u8F7F\u8F83\u90CA\u9175\u91AE\u91C2\u9278\u940E\u94F0\u9903\u997A\u9A55\u9A84\u9BAB\u9C4E\u9C9B\u9D41\u9D64\u9DE6\u9DEE\u9E6A",
"jie":"\u4E2F\u4ECB\u501F\u5022\u507C\u5091\u5226\u5227\u523C\u52AB\u52BC\u5369\u536A\u5424\u5588\u55DF\u5826\u583A\u5979\u59D0\u5A55\u5A8E\u5A98\u5AAB\u5AC5\u5B51\u5C10\u5C46\u5C4A\u5C8A\u5C95\u5D28\u5D65\u5DBB\u5DC0\u5E6F\u5E8E\u5FA3\u5FE6\u6088\u6212\u622A\u62EE\u6377\u63A5\u63B2\u63ED\u64D1\u64EE\u64F3\u65BA\u6605\u6770\u6840\u685D\u6904\u6950\u696C\u6976\u69A4\u6A9E\u6AED\u6BD1\u6D01\u6E5D\u6ED0\u6F54\u716F\u7297\u73A0\u743E\u754C\u754D\u758C\u7596\u75A5\u75CE\u7664\u7686\u776B\u780E\u78A3\u790D\u79F8\u7A2D\u7AED\u7BC0\u7D50\u7E72\u7ED3\u7FAF\u813B\u8282\u82A5\u83AD\u83E8\u84F5\u85C9\u86A7\u86E3\u86F6\u8710\u8754\u8818\u881E\u883D\u8857\u8871\u8878\u88BA\u892F\u89E3\u89E7\u8A10\u8A70\u8AA1\u8AB1\u8BA6\u8BD8\u8BEB\u8E15\u8FFC\u9263\u937B\u9636\u968E\u9782\u9821\u9889\u98F7\u9AB1\u9B5D\u9B6A\u9B9A\u9C92\u9D9B",
"jin":"\u4EC5\u4ECA\u4F12\u4FAD\u50C5\u50F8\u5118\u5153\u51DA\u52A4\u52B2\u52C1\u537A\u53AA\u5664\u568D\u57D0\u5807\u583B\u5890\u58D7\u5997\u5AE4\u5B27\u5BD6\u5C3D\u5D9C\u5DF9\u5DFE\u5ED1\u60CD\u616C\u6422\u65A4\u6649\u664B\u6783\u69FF\u6B4F\u6BA3\u6D25\u6D55\u6D78\u6E8D\u6F0C\u6FC5\u6FDC\u70EC\u7161\u71FC\u73D2\u740E\u743B\u7468\u747E\u74A1\u74B6\u76E1\u77DC\u781B\u7972\u7981\u7B4B\u7D1F\u7D27\u7DCA\u7E09\u7F19\u8355\u8369\u83EB\u84F3\u85CE\u887F\u895F\u89B2\u89D0\u89D4\u8B39\u8C28\u8CEE\u8D10\u8D46\u8FD1\u8FDB\u9032\u91D1\u91D2\u9326\u9485\u9526\u9773\u9949\u9991\u9E76\u9EC5\u9F7D",
"jing":"\u4E3C\u4E95\u4EAC\u4EB0\u4FD3\u501E\u50B9\u5106\u5162\u51C0\u51C8\u522D\u5244\u5753\u5755\u5759\u5883\u598C\u5A59\u5A5B\u5A67\u5B91\u5DE0\u5E5C\u5F2A\u5F33\u5F84\u5F91\u60CA\u61AC\u61BC\u656C\u65CC\u65CD\u666F\u6676\u66BB\u66D4\u6871\u68B7\u6A78\u6C6B\u6C6C\u6CFE\u6D44\u6D87\u6DE8\u701E\u71DD\u71DE\u7304\u734D\u7484\u749F\u74A5\u75C9\u75D9\u775B\u79D4\u7A09\u7A7D\u7ADE\u7ADF\u7AE7\u7AEB\u7AF6\u7AF8\u7CB3\u7CBE\u7D4C\u7D93\u7ECF\u8059\u80BC\u80EB\u811B\u8148\u830E\u8346\u834A\u8396\u83C1\u87FC\u8AA9\u8B66\u8E01\u8FF3\u9015\u93E1\u955C\u9631\u9753\u9756\u9759\u975A\u975C\u981A\u9838\u9888\u9A5A\u9BE8\u9CB8\u9D5B\u9D81\u9D84\u9E96\u9EA0\u9F31",
"jiong":"\u4FB0\u50D2\u5182\u518B\u518F\u56E7\u5770\u57DB\u6243\u6CC2\u6D7B\u6F83\u70AF\u70F1\u715A\u715B\u71B2\u71D1\u71DB\u7A98\u7D45\u7D97\u860F\u8614\u8927\u8FE5\u9008\u9848\u988E\u99C9\u99EB",
"jiu":"\u4E29\u4E45\u4E46\u4E5D\u4E63\u5003\u50E6\u52FC\u5313\u531B\u5336\u53A9\u548E\u557E\u597A\u5C31\u5EC4\u5ECF\u5ED0\u6166\u63C2\u63EA\u63EB\u644E\u6551\u65E7\u673B\u6766\u67E9\u67FE\u6855\u6A1B\u6B0D\u6BA7\u6C63\u7078\u725E\u7396\u7542\u759A\u7A76\u7CFA\u7CFE\u7D24\u7EA0\u81FC\u8205\u820A\u820F\u841B\u8D73\u9152\u9579\u9604\u97ED\u97EE\u9B0F\u9B2E\u9BE6\u9CE9\u9DF2\u9E20\u9E6B\u9E94\u9F68",
"ju":"\u4E3E\u4FB7\u4FF1\u5028\u5036\u50EA\u5177\u51A3\u51E5\u5267\u5287\u52EE\u530A\u53E5\u5480\u5727\u57E7\u57FE\u58C9\u59D6\u5A35\u5A45\u5A6E\u5BE0\u5C40\u5C45\u5C66\u5C68\u5CA0\u5D0C\u5DC8\u5DE8\u5F06\u6007\u601A\u60E7\u6133\u61C5\u61FC\u6285\u62D2\u62D8\u62E0\u6319\u6336\u6344\u636E\u63AC\u64DA\u64E7\u661B\u6854\u68AE\u6907\u6908\u6910\u6989\u6998\u6A58\u6A8B\u6AF8\u6B05\u6B6B\u6BE9\u6BF1\u6CAE\u6CC3\u6CE6\u6D30\u6DBA\u6DD7\u6E68\u6FBD\u70AC\u7117\u7123\u7220\u728B\u7291\u72CA\u72D9\u741A\u75BD\u75C0\u7717\u77BF\u77E9\u7820\u79EC\u7AAD\u7AB6\u7B65\u7C34\u7C94\u7CB7\u7F5D\u801F\u805A\u8065\u8152\u8209\u824D\u82E3\u82F4\u8392\u83CA\u849F\u84FB\u861C\u8661\u86B7\u871B\u8893\u88FE\u8977\u8A4E\u8ACA\u8BB5\u8C66\u8C97\u8D84\u8D9C\u8DD4\u8DD9\u8DDD\u8DFC\u8E18\u8E1E\u8E3D\u8E6B\u8E86\u8EB9\u8F02\u907D\u90AD\u90F9\u91B5\u9245\u92E6\u92F8\u943B\u949C\u9514\u952F\u95B0\u9671\u96CE\u97A0\u97AB\u98B6\u98D3\u99CF\u99D2\u99F6\u9A67\u9A79\u9B88\u9B94\u9D21\u9D59\u9D74\u9D8B\u9DAA\u9F30\u9F33\u9F5F\u9F83",
"juan":"\u5026\u52B5\u52CC\u52EC\u5377\u545F\u57CD\u5946\u59E2\u5A1F\u5E23\u5F2E\u617B\u6350\u6372\u684A\u6D93\u6DC3\u72F7\u7367\u74F9\u7737\u774A\u7760\u7D6D\u7D79\u7EE2\u7F65\u7F82\u8127\u81C7\u83E4\u8528\u8832\u88D0\u9104\u92D1\u92D7\u9308\u93B8\u942B\u9529\u954C\u96BD\u96CB\u98EC\u990B\u9D51\u9E43",
"jue":"\u4E85\u5014\u5095\u51B3\u5214\u5282\u52EA\u53A5\u5658\u5671\u56BC\u599C\u5B52\u5B53\u5C69\u5C6B\u5D1B\u5DA1\u5DA5\u5F4F\u61A0\u61B0\u6204\u6289\u6317\u6354\u6398\u6485\u64A7\u652B\u658D\u6877\u6A5B\u6A5C\u6B14\u6B2E\u6B8C\u6C12\u6C7A\u6CEC\u6F4F\u704D\u7106\u71A6\u7211\u721D\u7234\u7235\u7357\u7383\u73A6\u73A8\u73CF\u7474\u761A\u77CD\u77E1\u7804\u7D55\u7D76\u7EDD\u81C4\u82B5\u855D\u8568\u8673\u8697\u87E8\u87E9\u8990\u899A\u89BA\u89C9\u89D6\u89FC\u8A23\u8B4E\u8BC0\u8C32\u8C9C\u8D7D\u8D89\u8DB9\u8E76\u8E77\u8EA9\u902B\u920C\u940D\u941D\u9481\u9562\u957C\u99C3\u9D02\u9D03\u9D8C\u9DE2\u9FA3",
"jun":"\u4FCA\u5101\u519B\u541B\u5441\u5747\u57C8\u59F0\u5BEF\u5CFB\u61CF\u6343\u6508\u6659\u687E\u6C6E\u6D5A\u6FEC\u710C\u71C7\u73FA\u756F\u76B2\u76B8\u76B9\u7885\u7AE3\u7B60\u7B98\u7B9F\u8399\u83CC\u8690\u8720\u8880\u89A0\u8ECD\u90E1\u921E\u9281\u929E\u9355\u94A7\u9656\u9915\u9982\u99FF\u9A8F\u9BB6\u9CAA\u9D54\u9D55\u9D58\u9E87\u9E8F\u9E95",
"ka":"\u4F67\u5361\u5494\u5496\u5580\u57B0\u80E9\u88C3\u9272",
"ke":"\u514B\u523B\u524B\u52C0\u52CA\u533C\u53EF\u54B3\u55D1\u5777\u5801\u58F3\u5A14\u5BA2\u5C05\u5CA2\u5CC7\u5D51\u5D59\u5DB1\u606A\u6119\u63E2\u6415\u6564\u67EF\u68F5\u69BC\u6A16\u6BBB\u6C2A\u6E07\u6E34\u6E98\u70A3\u7241\u7290\u73C2\u75B4\u75FE\u778C\u78A6\u78D5\u790A\u791A\u79D1\u7A1E\u7AA0\u7DD9\u7F02\u7FD7\u80E2\u82DB\u842A\u8596\u874C\u8AB2\u8BFE\u8DB7\u8EFB\u8F72\u9198\u9233\u9301\u94B6\u951E\u9826\u9846\u988F\u9897\u9A0D\u9A92\u9AC1",
"kai":"\u4E6B\u51EF\u51F1\u5240\u5274\u52D3\u5605\u57B2\u584F\u5952\u5F00\u5FFE\u607A\u6137\u613E\u6168\u63E9\u669F\u6977\u6B2C\u708C\u708F\u70D7\u8488\u8849\u8F06\u9347\u938E\u93A7\u9426\u94E0\u950E\u9534\u958B\u95D3\u95FF\u98BD",
"kan":"\u4F83\u5058\u519A\u520A\u52D8\u574E\u57F3\u582A\u583F\u586A\u5888\u5D01\u5D41\u60C2\u6221\u681E\u6B3F\u6B41\u770B\u77B0\u77D9\u780D\u78E1\u7AF7\u83B0\u884E\u8F21\u8F41\u8F57\u95DE\u961A\u9851\u9F95\u9F9B",
"kang":"\u4EA2\u4F09\u531F\u56E5\u5ADD\u5D7B\u5EB7\u5FFC\u6177\u625B\u6297\u6443\u69FA\u6F2E\u7095\u72BA\u780A\u7A45\u7C87\u7CE0\u8EBF\u909F\u9227\u93EE\u94AA\u958C\u95F6\u9C47",
"kao":"\u4E02\u5C3B\u62F7\u6537\u6832\u6D18\u70E4\u7292\u8003\u92AC\u94D0\u9760\u9ADB\u9BB3\u9BCC\u9C93",
"ken":"\u5543\u57A6\u58BE\u6073\u61C7\u63AF\u808E\u80AF\u80BB\u88C9\u8903\u8C64\u8C87\u9339",
"keng":"\u4E6C\u52A5\u53BC\u542D\u551F\u5748\u5751\u5DEA\u603E\u6333\u727C\u7841\u785C\u787B\u8A99\u92B5\u935E\u93D7\u94FF",
"kong":"\u5025\u57EA\u5B54\u5D06\u6050\u60BE\u63A7\u6DB3\u787F\u7A7A\u7B9C\u8EBB\u8EBC\u9313\u979A\u9D7C",
"kou":"\u51A6\u527E\u52B6\u53E3\u53E9\u5BBC\u5BC7\u5EE4\u5F44\u6010\u6263\u62A0\u6473\u6542\u6EF1\u770D\u7789\u7798\u7A9B\u7B58\u7C06\u82A4\u8532\u853B\u91E6\u9DC7",
"ku":"\u4FC8\u5233\u54ED\u55BE\u56B3\u5710\u5800\u5D2B\u5E93\u5EAB\u625D\u67AF\u684D\u695B\u7105\u72DC\u7614\u77FB\u79D9\u7A9F\u7D5D\u7ED4\u82E6\u88B4\u88E4\u8932\u8DCD\u90C0\u9177\u9AB7\u9BAC",
"kua":"\u4F89\u54B5\u57AE\u5938\u59F1\u630E\u6647\u80EF\u823F\u8A87\u8DE8\u9299\u9ABB",
"kuai":"\u4FA9\u5108\u51F7\u54D9\u5672\u5726\u5757\u584A\u58A4\u5DDC\u5EE5\u5FEB\u64D3\u65DD\u72EF\u736A\u7B77\u7CE9\u810D\u81BE\u84AF\u90D0\u9136\u9C60\u9C99",
"kuan":"\u5BBD\u5BDB\u5BEC\u68A1\u6B35\u6B3E\u6B40\u7ABD\u7ABE\u9467\u9ACB\u9AD6",
"kuang":"\u5123\u51B5\u52BB\u5321\u5329\u54D0\u5739\u58D9\u593C\u5CB2\u6047\u61EC\u61ED\u6282\u65F7\u663F\u66E0\u6846\u6CC1\u6D2D\u720C\u72C2\u72C5\u7716\u7736\u77CC\u77FF\u783F\u7926\u7A6C\u7B50\u7B7A\u7D4B\u7D56\u7E8A\u7EA9\u8A86\u8A91\u8BD3\u8BF3\u8CBA\u8D36\u8ED6\u8EE0\u8EE6\u8EED\u909D\u90BC\u913A\u9271\u92DB\u945B\u9D5F\u9ECB",
"kui":"\u4E8F\u5080\u5232\u532E\u5331\u537C\u559F\u55B9\u5633\u5914\u594E\u5ABF\u5B07\u5C2F\u5CBF\u5DCB\u5DD9\u609D\u6126\u6127\u6192\u6223\u63C6\u6646\u668C\u694F\u6951\u6A3B\u6AC6\u6B33\u6BA8\u6E83\u6F70\u7143\u76D4\u777D\u78C8\u7AA5\u7ABA\u7BD1\u7C23\u7C44\u8067\u8069\u806D\u8075\u8475\u8489\u848A\u8562\u85C8\u862C\u8637\u8641\u8667\u8770\u8B09\u8DEC\u8E5E\u8EA8\u9035\u9108\u9368\u9377\u9400\u944E\u95DA\u9804\u980D\u982F\u985D\u993D\u994B\u9988\u9997\u9A24\u9A99\u9B41",
"kun":"\u56F0\u5764\u5803\u5812\u58F8\u58FC\u5A6B\u5C21\u5D10\u5D11\u6083\u6346\u6606\u665C\u68B1\u6D83\u6F49\u711C\u71B4\u7311\u7428\u747B\u774F\u7871\u7975\u7A07\u7A1B\u7D91\u81D7\u83CE\u872B\u88C8\u88CD\u88E9\u890C\u918C\u9315\u951F\u95AB\u95B8\u9603\u9A09\u9AE0\u9AE1\u9AE8\u9BE4\u9CB2\u9D7E\u9DA4\u9E4D",
"kuo":"\u564B\u5ED3\u61D6\u6269\u62E1\u62EC\u6304\u64F4\u681D\u6870\u6FF6\u7A52\u7B48\u843F\u8440\u86DE\u95CA\u9614\u9729\u979F\u97B9\u97D5\u9822\u9AFA\u9B20",
"la":"\u524C\u5566\u5587\u56B9\u5783\u62C9\u63E6\u63E7\u641A\u650B\u65EF\u67C6\u694B\u6AF4\u6E82\u7209\u74CE\u760C\u782C\u78D6\u7FCB\u814A\u81C8\u81D8\u83C8\u85DE\u8721\u874B\u8772\u881F\u8FA2\u8FA3\u908B\u945E\u9574\u97A1\u9B0E\u9BFB",
"lai":"\u4F86\u4FEB\u5008\u553B\u5A61\u5D03\u5D0D\u5EB2\u5F95\u5FA0\u6765\u68BE\u68F6\u6D9E\u6DF6\u6FD1\u7028\u702C\u730D\u741C\u765E\u7669\u7750\u775E\u7B59\u7B82\u7C41\u7C5F\u83B1\u840A\u85FE\u8970\u8CDA\u8CF4\u8D49\u8D56\u9028\u90F2\u9338\u94FC\u983C\u9842\u9A0B\u9BE0\u9D63\u9D86\u9EB3",
"lan":"\u5116\u5170\u53B1\u5682\u56D2\u58C8\u58CF\u5A6A\u5B3E\u5B44\u5B4F\u5C9A\u5D50\u5E71\u61D2\u61E2\u61F6\u62E6\u63FD\u64E5\u6514\u652C\u6593\u6595\u680F\u6984\u6B04\u6B16\u6B17\u6D68\u6EE5\u6F24\u6F9C\u6FEB\u703E\u7046\u7060\u7061\u70C2\u71D7\u71E3\u7201\u721B\u7224\u7226\u74BC\u74D3\u7937\u7BEE\u7C43\u7C63\u7CF7\u7E7F\u7E9C\u7F06\u7F71\u847B\u84DD\u84DE\u85CD\u862D\u8934\u8955\u8964\u8974\u897D\u89A7\u89BD\u89C8\u8B4B\u8B95\u8C30\u8E9D\u9182\u946D\u9484\u9567\u95CC\u9611\u97CA",
"lang":"\u52C6\u5525\u5577\u57CC\u5871\u5ACF\u5D00\u5ECA\u60A2\u6716\u6717\u6724\u6879\u6994\u6A03\u6B34\u6D6A\u70FA\u72FC\u7405\u746F\u7860\u7A02\u7B64\u8246\u83A8\u8497\u84C8\u84E2\u870B\u8782\u8A8F\u8EB4\u90CE\u90D2\u90DE\u92C3\u93AF\u9512\u95AC\u9606\u99FA",
"lao":"\u4F6C\u50D7\u52B3\u52B4\u52DE\u54BE\u54F0\u5520\u55E0\u562E\u59E5\u5AEA\u5D02\u5D97\u6045\u61A5\u61A6\u635E\u6488\u6725\u6833\u6A51\u6A6F\u6D76\u6D9D\u6F66\u6F87\u70D9\u7262\u72EB\u73EF\u75E8\u7646\u7853\u78F1\u7A82\u7C29\u7CA9\u8001\u8002\u8022\u802E\u8356\u86EF\u87E7\u8EC2\u8F51\u916A\u91AA\u92A0\u9412\u94D1\u94F9\u985F\u9ADD\u9BB1",
"le":"\u4E50\u4EC2\u52D2\u53FB\u5FC7\u6250\u697D\u6A02\u6C3B\u6CD0\u738F\u7833\u7AFB\u7C15\u827B\u961E\u97F7\u990E\u9979\u9C33\u9CD3",
"lei":"\u50AB\u5121\u513D\u53BD\u561E\u5792\u5841\u58D8\u58E8\u5AD8\u64C2\u6502\u6A0F\u6A91\u6AD0\u6AD1\u6B19\u6CEA\u6D21\u6D99\u6DDA\u7045\u74C3\u757E\u7657\u77CB\u78CA\u78E5\u790C\u7927\u7928\u79B7\u7C7B\u7D2F\u7D6B\u7E32\u7E87\u7E8D\u7E9D\u7F27\u7F4D\u7FB8\u8012\u808B\u8137\u8502\u854C\u857E\u85DF\u8631\u8632\u863D\u8646\u881D\u8A84\u8B84\u8BD4\u8F60\u9179\u9287\u9311\u9433\u9458\u9478\u956D\u96F7\u9741\u981B\u982A\u985E\u98A3\u9C69\u9E13\u9F3A",
"ling":"\u4EE4\u4F36\u51CC\u5222\u53E6\u5464\u56F9\u577D\u590C\u59C8\u5A48\u5B41\u5CAD\u5CBA\u5D1A\u5DBA\u5F7E\u6395\u6624\u670E\u67C3\u68C2\u6AFA\u6B1E\u6CE0\u6DE9\u6FAA\u702E\u7075\u70A9\u71EF\u7227\u72D1\u73B2\u740C\u74F4\u768A\u7831\u797E\u79E2\u7ADB\u7B2D\u7D37\u7DBE\u7EEB\u7F9A\u7FCE\u8046\u8232\u82D3\u83F1\u84E4\u8506\u8576\u8626\u86C9\u8851\u888A\u88EC\u8A45\u8DC9\u8EE8\u8F18\u9143\u91BD\u9234\u9302\u94C3\u959D\u963E\u9675\u96F6\u970A\u9717\u971B\u971D\u9748\u9818\u9886\u99D6\u9B7F\u9BEA\u9CAE\u9D12\u9E30\u9E77\u9EA2\u9F61\u9F62\u9F84\u9F97",
"leng":"\u5030\u51B7\u580E\u5844\u6123\u68F1\u695E\u7756\u7890\u7A1C\u8590\u8E1C",
"li":"\u4E3D\u4F8B\u4FD0\u4FDA\u4FEA\u5088\u512E\u5137\u51D3\u5215\u5229\u5253\u527A\u5299\u529B\u52B1\u52F5\u5386\u5389\u5398\u53A4\u53AF\u53B2\u540F\u5456\u54E9\u550E\u5533\u55B1\u569F\u56A6\u56C4\u56C7\u575C\u585B\u58E2\u5A0C\u5A33\u5A6F\u5AE0\u5B4B\u5B77\u5C74\u5CA6\u5CDB\u5CF2\u5DC1\u5EF2\u60A1\u60A7\u60B7\u6144\u623E\u642E\u650A\u6526\u652D\u6584\u66A6\u66C6\u66DE\u6738\u674E\u67A5\u6803\u680E\u6817\u681B\u6835\u68A8\u68B8\u68C3\u68D9\u6A06\u6AAA\u6AD4\u6ADF\u6AEA\u6B10\u6B1A\u6B74\u6B77\u6C02\u6CA5\u6CB4\u6D6C\u6D96\u6EA7\u6F13\u6FA7\u6FFF\u701D\u7055\u7204\u720F\u7281\u7282\u729B\u72A1\u72F8\u7301\u73D5\u7406\u740D\u746E\u7483\u74C5\u74C8\u74D1\u74E5\u75A0\u75AC\u75E2\u7658\u7667\u76AA\u76E0\u76ED\u775D\u7805\u783A\u783E\u78FF\u792A\u792B\u7930\u793C\u79AE\u79B2\u79BB\u79DD\u7A72\u7ACB\u7AF0\u7B20\u7B63\u7BE5\u7BF1\u7C6C\u7C92\u7C9D\u7CB4\u7CCE\u7CF2\u7D9F\u7E2D\u7F21\u7F79\u8243\u82C8\u82D9\u8318\u8354\u8372\u8385\u8389\u83DE\u849A\u849E\u84E0\u853E\u85DC\u85F6\u863A\u86B8\u86CE\u86E0\u870A\u8727\u87CD\u87F8\u8807\u8821\u8823\u882B\u88CF\u88E1\u8935\u8A48\u8B27\u8B88\u8C4A\u8C8D\u8D72\u8DDE\u8E92\u8F62\u8F63\u8F79\u9026\u908C\u9090\u90E6\u9148\u91A8\u91B4\u91CC\u91D0\u925D\u92EB\u92F0\u9305\u93EB\u9457\u9502\u96B6\u96B7\u96B8\u96E2\u96F3\u9742\u974B\u9A6A\u9A8A\u9B01\u9BC9\u9BCF\u9BEC\u9C67\u9C71\u9C7A\u9CA1\u9CA4\u9CE2\u9CE8\u9D17\u9D79\u9DC5\u9E1D\u9E42\u9E97\u9E9C\u9ECE\u9EE7",
"lian":"\u4EB7\u50C6\u5286\u5332\u5333\u55F9\u5652\u581C\u5941\u5969\u5A08\u5AA1\u5AFE\u5B1A\u5E18\u5EC9\u601C\u604B\u6169\u6190\u6200\u6459\u655B\u6582\u68BF\u695D\u69E4\u6AE3\u6B5B\u6B93\u6BAE\u6D70\u6D9F\u6E45\u6E93\u6F23\u6F4B\u6FB0\u6FC2\u6FD3\u7032\u70BC\u7149\u7191\u71EB\u740F\u7453\u7489\u78CF\u7A34\u7C3E\u7C62\u7C68\u7DF4\u7E3A\u7E9E\u7EC3\u7FB7\u7FF4\u8054\u8068\u806B\u806E\u806F\u8138\u81C1\u81C9\u83B2\u8430\u84EE\u8539\u8595\u861D\u861E\u878A\u880A\u88E2\u88E3\u8933\u895D\u899D\u8B30\u8B67\u8E65\u8FDE\u9023\u913B\u932C\u934A\u938C\u93C8\u942E\u94FE\u9570\u9B11\u9C0A\u9C31\u9CA2",
"liang":"\u4E21\u4E24\u4EAE\u4FCD\u5169\u51C9\u54F4\u5521\u5562\u55A8\u589A\u639A\u667E\u6881\u690B\u6A11\u6DBC\u6E78\u7177\u7C17\u7CAE\u7CB1\u7CE7\u7DA1\u7DC9\u813C\u826F\u873D\u88F2\u8AD2\u8C05\u8E09\u8F0C\u8F1B\u8F2C\u8F86\u8F8C\u91CF\u9344\u9B49\u9B4E",
"liao":"\u4E86\u50DA\u5639\u5AFD\u5BE5\u5BEE\u5C1E\u5C25\u5C26\u5C6A\u5D7A\u5D9A\u5D9B\u5ED6\u5EEB\u6180\u61AD\u6482\u64A9\u6579\u6599\u66B8\u6F3B\u7093\u71CE\u720E\u7212\u7360\u7499\u7597\u7642\u77AD\u7AB7\u7AC2\u7C1D\u7E5A\u7F2D\u804A\u818B\u81AB\u84FC\u87DF\u8C42\u8CFF\u8E58\u8E7D\u8FBD\u907C\u911D\u91D5\u9410\u948C\u9563\u957D\u98C9\u9ACE\u9DEF\u9E69",
"lie":"\u5120\u51BD\u5217\u52A3\u52BD\u54A7\u57D2\u57D3\u59F4\u5CE2\u5DE4\u6312\u6318\u6369\u64F8\u6BDF\u6D0C\u6D56\u70C8\u70EE\u716D\u72A3\u730E\u731F\u7375\u7759\u8057\u811F\u8322\u86DA\u88C2\u8D94\u8E90\u8FFE\u98B2\u9B1B\u9B23\u9BA4\u9C72\u9D37",
"lin":"\u4E34\u4E83\u50EF\u51DB\u51DC\u53B8\u541D\u5549\u58E3\u5D0A\u5D99\u5EE9\u5EEA\u6061\u608B\u60CF\u61CD\u61D4\u62CE\u649B\u65B4\u667D\u66BD\u6797\u6A49\u6A81\u6AA9\u6DCB\u6F7E\u6F9F\u7036\u711B\u71D0\u735C\u7433\u7498\u7510\u7584\u75F3\u765B\u765D\u77B5\u7884\u78F7\u7A1F\u7B96\u7CA6\u7CBC\u7E57\u7FF7\u81A6\u81E8\u83FB\u853A\u85FA\u8CC3\u8D41\u8E78\u8E8F\u8E99\u8EAA\u8F54\u8F65\u8F9A\u9074\u90BB\u9130\u93FB\u95B5\u96A3\u9716\u9872\u9A4E\u9C57\u9CDE\u9E90\u9E9F",
"liu":"\u516D\u5218\u5289\u56A0\u56D6\u586F\u5AB9\u5B3C\u5D67\u5EC7\u61F0\u65C8\u65D2\u67F3\u6801\u685E\u687A\u69B4\u6A4A\u6A6E\u6CA0\u6D41\u6D4F\u6E9C\u6F91\u700F\u7198\u71AE\u73CB\u7409\u7460\u746C\u74A2\u74FC\u7505\u7544\u7559\u7571\u7581\u7624\u7645\u786B\u78C2\u78DF\u7DB9\u7EFA\u7F76\u7F80\u7FCF\u84A5\u84C5\u85F0\u87C9\u88D7\u8E53\u905B\u92F6\u938F\u93A6\u93D0\u9402\u950D\u954F\u9560\u96E1\u9724\u98C0\u98C2\u98C5\u98D7\u993E\u998F\u99E0\u99F5\u9A2E\u9A51\u9A9D\u9B38\u9C21\u9DB9\u9DDA\u9E60\u9E68\u9E8D",
"long":"\u5131\u5499\u54E2\u56A8\u5784\u5785\u58DF\u58E0\u5C78\u5D90\u5DC3\u5DC4\u5FBF\u62E2\u650F\u663D\u66E8\u6727\u680A\u6887\u69DE\u6AF3\u6CF7\u6E70\u6F0B\u7027\u7216\u73D1\u74CF\u7643\u772C\u77D3\u783B\u7866\u7931\u7932\u7ABF\u7AC9\u7ADC\u7B3C\u7BED\u7C60\u804B\u807E\u80E7\u830F\u856F\u8622\u882A\u882C\u8856\u8971\u8C45\u8D1A\u8E98\u93E7\u9468\u9647\u9686\u96B4\u9733\u9747\u9A61\u9E17\u9F8D\u9F92\u9F93\u9F99",
"lou":"\u507B\u50C2\u55BD\u560D\u587F\u5A04\u5A41\u5C5A\u5D5D\u5D81\u5ED4\u617A\u6402\u645F\u697C\u6A13\u6E87\u6F0A\u6F0F\u71A1\u750A\u7618\u763A\u763B\u779C\u7BD3\u7C0D\u8027\u802C\u825B\u848C\u851E\u877C\u87BB\u8B31\u8EC1\u9071\u93E4\u9542\u964B\u9732\u97BB\u9AC5\u9ACF",
"lu":"\u4F93\u50C7\u5279\u52CE\u52E0\u5362\u5364\u565C\u5695\u56A7\u5725\u5774\u5786\u5876\u5877\u58DA\u5A3D\u5CCD\u5E90\u5ED8\u5EEC\u5F54\u5F55\u622E\u6314\u635B\u63B3\u645D\u64B8\u64C4\u64FC\u650E\u67A6\u680C\u6902\u6A10\u6A1A\u6A79\u6AD3\u6AE8\u6C07\u6C0C\u6CF8\u6DD5\u6DE5\u6E0C\u6EE4\u6EF7\u6F09\u6F5E\u6F9B\u6FFE\u7002\u7018\u7089\u719D\u7210\u7379\u7388\u742D\u7490\u74B7\u74D0\u752A\u76DD\u76E7\u7769\u77D1\u7849\u7875\u788C\u78E0\u797F\u7984\u7A11\u7A4B\u7B93\u7C0F\u7C2C\u7C35\u7C36\u7C59\u7C5A\u7CB6\u7E91\u7F4F\u80EA\u8194\u819F\u81DA\u822E\u823B\u8263\u826A\u826B\u82A6\u83C9\u84FE\u850D\u8557\u8606\u8642\u864F\u865C\u87B0\u8826\u89EE\u89FB\u8CC2\u8D42\u8DA2\u8DEF\u8E1B\u8E57\u8F05\u8F46\u8F64\u8F73\u8F82\u8F98\u902F\u9181\u9229\u9304\u9332\u9334\u93C0\u93D5\u93F4\u942A\u9465\u946A\u9565\u9646\u9678\u9871\u9885\u9A04\u9A3C\u9AD7\u9B6F\u9B72\u9BE5\u9C78\u9C81\u9C88\u9D3C\u9D66\u9D71\u9DFA\u9E15\u9E2C\u9E6D\u9E75\u9E7F\u9E93\u9EF8",
"lv":"\u4FA3\u4FB6\u5122\u52F4\u5415\u5442\u54F7\u578F\u5BFD\u5C61\u5C62\u5C65\u5D42\u5F8B\u616E\u65C5\u66E5\u68A0\u6988\u6AD6\u6ADA\u6C00\u6C2F\u7112\u7208\u7387\u7963\u7A06\u7A5E\u7A6D\u7BBB\u7D7D\u7DA0\u7DD1\u7E37\u7E42\u7EFF\u7F15\u8182\u8190\u81A2\u844E\u85D8\u8651\u891B\u8938\u90D8\u92C1\u9462\u94DD\u95AD\u95FE\u99BF\u9A62\u9A74\u9DDC",
"luan":"\u4E71\u4E82\u5375\u571D\u571E\u5971\u5B4C\u5B6A\u5B7F\u5CE6\u5DD2\u631B\u6523\u66EB\u683E\u6B12\u6EE6\u7053\u7064\u7674\u7675\u7F89\u8114\u81E0\u864A\u91E0\u92AE\u947E\u9D49\u9E1E\u9E3E",
"lue":"\u63a0\u7565\u950a",
"lun":"\u4ED1\u4F26\u4F96\u502B\u56F5\u5707\u57E8\u5A68\u5D18\u5D19\u60C0\u62A1\u6384\u68C6\u6CA6\u6DEA\u6EA3\u7896\u78EE\u7A10\u7DB8\u7EB6\u8023\u8140\u83D5\u8726\u8AD6\u8BBA\u8E1A\u8F2A\u8F6E\u9300\u966F\u9BE9",
"luo":"\u502E\u5138\u5246\u5570\u56C9\u5CC8\u634B\u645E\u651E\u66EA\u6924\u6B0F\u6CFA\u6D1B\u6D1C\u6F2F\u6FFC\u7296\u7321\u7380\u73DE\u7630\u7673\u7822\u7B3F\u7BA9\u7C6E\u7D61\u7E99\u7EDC\u7F57\u7F85\u8136\u8161\u81DD\u8366\u841D\u843D\u84CF\u863F\u87BA\u8803\u88F8\u89B6\u89BC\u8EB6\u903B\u908F\u93CD\u947C\u9523\u9559\u96D2\u9831\u9960\u99F1\u9A3E\u9A58\u9A86\u9AA1\u9BA5\u9C73\u9D45\u9E01",
"ma":"\u4E87\u508C\u5417\u551B\u55CE\u561B\u561C\u5988\u5ABD\u5AF2\u5B24\u5B37\u6769\u69AA\u6EA4\u7298\u72B8\u7341\u739B\u746A\u75F2\u7770\u7801\u78BC\u7923\u7943\u79A1\u7F75\u8534\u8682\u879E\u87C6\u87C7\u9064\u93B7\u9581\u99AC\u99E1\u9A6C\u9A82\u9B15\u9C22\u9DCC\u9EBB",
"mai":"\u4E70\u4F45\u52A2\u52F1\u5356\u562A\u57CB\u58F2\u8108\u8109\u836C\u8552\u85B6\u8847\u8CB7\u8CE3\u8FC8\u9081\u9721\u9722\u973E\u9DF6\u9EA5\u9EA6",
"man":"\u50C8\u5881\u59CF\u5ADA\u5C58\u5E54\u6097\u6162\u6172\u6471\u66FC\u69FE\u6A20\u6E80\u6EE1\u6EFF\u6F2B\u6FAB\u6FB7\u71B3\u734C\u774C\u7792\u779E\u77D5\u7E35\u7F26\u8504\u8513\u8630\u86EE\u87A8\u87C3\u87CE\u883B\u8954\u8B3E\u8C29\u9124\u93CB\u93DD\u9558\u9794\u9862\u989F\u9945\u9992\u9B17\u9B18\u9C3B\u9CD7",
"mang":"\u5301\u5396\u5402\u54E4\u58FE\u5A0F\u5C28\u5FD9\u607E\u6757\u6767\u6C52\u6D5D\u6F2D\u7264\u727B\u72F5\u75DD\u76F2\u786D\u7B00\u8292\u832B\u833B\u83BD\u83BE\u8609\u86D6\u87D2\u880E\u9099\u91EF\u92E9\u94D3\u99F9",
"meng":"\u511A\u51A1\u52D0\u5922\u5923\u5B5F\u5E6A\u5EAC\u61DC\u61DE\u61F5\u63B9\u64DD\u66DA\u6726\u68A6\u6A57\u6AAC\u6C0B\u6C13\u6E95\u6FDB\u731B\u7374\u74FE\u750D\u753F\u76DF\u77A2\u77C7\u77D2\u791E\u7F5E\u824B\u8268\u8394\u840C\u8420\u8499\u8544\u867B\u8722\u8771\u8813\u9133\u9138\u9333\u9530\u96FA\u9725\u973F\u9740\u986D\u995B\u9BCD\u9BED\u9E0F\u9E72\u9F06",
"miao":"\u55B5\u5999\u5A8C\u5AF9\u5E99\u5EBF\u5EDF\u63CF\u676A\u6DFC\u6E3A\u7385\u7707\u7784\u79D2\u7AD7\u7BCE\u7DF2\u7F08\u82D7\u85D0\u9088\u9C59\u9D93\u9E4B",
"mao":"\u5183\u5187\u5190\u5192\u536F\u551C\u5825\u5918\u5AA2\u5CC1\u5E3D\u6117\u61CB\u623C\u65C4\u6634\u6693\u6786\u6959\u6BDB\u6BDC\u6BDD\u6BF7\u6CD6\u6E35\u7266\u732B\u7441\u7683\u770A\u7780\u77DB\u7B37\u7DE2\u8004\u82BC\u8302\u8305\u8306\u84E9\u86D1\u8750\u8765\u87CA\u88A4\u8992\u8C8C\u8C93\u8CBF\u8D38\u8EDE\u911A\u912E\u9155\u925A\u9328\u94C6\u951A\u9AE6\u9AF3\u9D9C",
"me":"\u4E48\u5692\u56B0\u5E85\u6FF9\u7666",
"mei":"\u51C2\u5445\u569C\u5833\u587A\u59B9\u5A84\u5A92\u5A9A\u5ABA\u5B0D\u5BD0\u5D44\u5D4B\u5FBE\u62BA\u6334\u6517\u651F\u6627\u679A\u67D0\u6802\u6885\u6963\u6973\u69D1\u6BCE\u6BCF\u6C92\u6CA1\u6CAC\u6D7C\u6E3C\u6E44\u6E48\u715D\u7164\u71D8\u7338\u73AB\u73FB\u7442\u75D7\u7709\u771B\u7742\u7778\u77C0\u7959\u7996\u7BC3\u7F8E\u8104\u8122\u815C\u82FA\u8393\u847F\u862A\u875E\u8882\u8DCA\u8EBE\u90FF\u9176\u92C2\u9382\u9387\u9541\u9545\u9709\u97CE\u9B3D\u9B45\u9DA5\u9E5B\u9EE3\u9EF4",
"men":"\u4EEC\u5011\u60B6\u61D1\u61E3\u626A\u636B\u66AA\u691A\u7116\u71DC\u73A3\u748A\u7A48\u83DB\u864B\u9346\u9494\u9580\u9585\u95E8\u95F7",
"mi":"\u4F8E\u5196\u519E\u51AA\u54AA\u5627\u5853\u5B4A\u5B93\u5BBB\u5BC6\u5CDA\u5E42\u5E4E\u5E66\u5F25\u5F2D\u5F4C\u6202\u64DF\u6520\u6549\u6993\u6A12\u6AC1\u6C68\u6C95\u6CB5\u6CCC\u6D23\u6DE7\u6E33\u6EF5\u6F1E\u6FD4\u6FD7\u7030\u7056\u7190\u7222\u7315\u737C\u74D5\u772B\u772F\u7787\u7955\u7962\u79B0\u79D8\u7C1A\u7C73\u7C8E\u7CDC\u7CF8\u7E3B\u7F83\u7F8B\u8112\u8288\u845E\u84BE\u851D\u8524\u85CC\u863C\u871C\u8746\u88AE\u8993\u8994\u899B\u89C5\u8A78\u8B0E\u8B10\u8C1C\u8C27\u8E0E\u8FF7\u919A\u91BE\u91BF\u91C4\u92A4\u957E\u9761\u9E0D\u9E8A\u9E8B\u9E9B\u9F0F",
"mian":"\u4E0F\u4FDB\u506D\u514D\u5195\u52C9\u52D4\u5595\u5A29\u5A42\u5A94\u5B35\u5B80\u6110\u68C9\u6AB0\u6ACB\u6C45\u6C94\u6E4E\u7704\u7720\u77C8\u77CA\u77CF\u7CC6\u7DBF\u7DDC\u7DEC\u7EF5\u7F05\u817C\u81F1\u8287\u8442\u8752\u9762\u9763\u9BB8\u9EAA\u9EAB\u9EB5\u9EBA\u9EFE",
"mie":"\u4E5C\u5400\u54A9\u54F6\u5B6D\u5E6D\u61F1\u6423\u6AD7\u6EC5\u700E\u706D\u74F1\u7BFE\u8511\u858E\u881B\u884A\u8995\u9456\u9C74\u9D13",
"min":"\u50F6\u51A7\u51BA\u5221\u52C4\u578A\u59C4\u5CB7\u5D0F\u5FDE\u600B\u60AF\u610D\u615C\u61AB\u62BF\u636A\u6543\u654F\u656F\u65FB\u65FC\u668B\u6C11\u6CEF\u6E63\u6F63\u739F\u73C9\u7418\u741D\u7449\u75FB\u76BF\u76FF\u7888\u7B22\u7B3D\u7C22\u7DCD\u7DE1\u7F17\u7F60\u82E0\u8820\u8CEF\u9231\u9309\u9372\u9594\u95A9\u95F5\u95FD\u9C35\u9CD8\u9D16\u9EFD",
"ming":"\u4F72\u51A5\u51D5\u540D\u547D\u59F3\u5AC7\u614F\u63B5\u660E\u669D\u6719\u69A0\u6D3A\u6E9F\u733D\u7700\u7733\u7791\u8317\u84C2\u879F\u89AD\u8A7A\u910D\u9169\u9298\u94ED\u9CF4\u9E23",
"miu":"\u7E46\u7F2A\u8B2C\u8C2C",
"mo":"\u4E6E\u5298\u52B0\u55FC\u56A4\u56A9\u573D\u587B\u58A8\u59BA\u5AEB\u5AFC\u5BDE\u5C1B\u5E13\u5E1E\u603D\u61E1\u62B9\u6469\u6478\u6479\u64F5\u6629\u66AF\u672B\u67BA\u6A21\u6A45\u6B7E\u6B7F\u6B81\u6CAB\u6F20\u7205\u763C\u768C\u771C\u773D\u773F\u7790\u7799\u781E\u78E8\u7933\u79E3\u7C96\u7CE2\u7D48\u7E38\u7E86\u8031\u819C\u8309\u8388\u83AB\u84E6\u85E6\u8611\u86E8\u87D4\u88B9\u8B28\u8B29\u8B55\u8C1F\u8C83\u8C8A\u8C98\u9286\u93CC\u9546\u964C\u977A\u9943\u995D\u998D\u9A40\u9ACD\u9B54\u9B69\u9B79\u9EBC\u9EBD\u9EBF\u9ED8\u9ED9",
"mou":"\u4F94\u52BA\u54DE\u6048\u6859\u6D20\u725F\u7738\u77B4\u87F1\u8B00\u8C0B\u927E\u936A\u9D3E\u9EB0",
"mu":"\u4E06\u4EA9\u4EEB\u51E9\u52DF\u5776\u5893\u58B2\u59C6\u5A12\u5CD4\u5E55\u5E59\u6154\u6155\u62C7\u65C0\u66AE\u6728\u6927\u6958\u6A22\u6BCD\u6BE3\u6BEA\u6C01\u6C90\u7091\u7261\u7267\u7273\u72C7\u734F\u7546\u7552\u755D\u755E\u756E\u76EE\u7766\u782A\u7A46\u80DF\u8252\u82DC\u83AF\u843A\u869E\u8E07\u9267\u926C\u94BC\u96EE\u9702\u97AA",
"na":"\u4E78\u5185\u5436\u54EA\u55F1\u59A0\u5A1C\u62CF\u62FF\u6310\u637A\u7B1D\u7D0D\u7EB3\u80AD\u84B3\u8872\u88A6\u8ABD\u8C7D\u8C80\u8EDC\u90A3\u9209\u93BF\u94A0\u954E\u96EB\u9779\u9B76",
"nai":"\u4E43\u5037\u5948\u5976\u59B3\u5B2D\u5B7B\u5EFC\u6468\u67F0\u6C16\u6E3F\u718B\u7593\u8010\u8149\u827F\u8418\u879A\u8926\u8FFA\u91E2\u933C\u9F10",
"nan":"\u4FBD\u5357\u5583\u56E1\u5A1A\u5A7B\u6201\u62A9\u63C7\u6694\u678F\u67AC\u67DF\u6960\u6E73\u7175\u7537\u7558\u8169\u83AE\u8433\u877B\u8AF5\u8D67\u9056\u96BE\u96E3",
"nang":"\u4E6A\u513E\u56A2\u56CA\u56D4\u64C3\u652E\u66E9\u6B1C\u7062\u8830\u9962\u9995\u9B1E\u9F49",
"nao":"\u5318\u5476\u57B4\u5816\u5912\u5A65\u5AD0\u5B6C\u5CF1\u5DA9\u5DCE\u6013\u607C\u60A9\u60F1\u6320\u6493\u6A82\u6DD6\u7331\u7376\u737F\u7459\u7847\u7899\u78AF\u8111\u8133\u8166\u81D1\u86F2\u87EF\u8A49\u8B4A\u9403\u94D9\u9599\u95F9\u9B27",
"ne":"\u5450\u5462\u6290\u7592\u7732\u8A25\u8BB7",
"nei":"\u5167\u5A1E\u6C1D\u713E\u8147\u9912\u9981\u9BBE\u9BD8",
"nen":"\u5AE9\u80FD",
"ni":"\u4F31\u4F32\u4F60\u502A\u5117\u511E\u533F\u576D\u57FF\u5804\u59AE\u5A57\u5ADF\u5B3A\u5B74\u5C3C\u5C54\u5C70\u6029\u60C4\u6135\u62B3\u62DF\u639C\u64EC\u65CE\u6635\u6672\u66B1\u67C5\u68FF\u6AB7\u6C3C\u6CE5\u6DE3\u6EBA\u72D4\u730A\u75C6\u7724\u7768\u79DC\u7C7E\u7E0C\u80D2\u817B\u81A9\u81E1\u82E8\u85BF\u86AD\u873A\u89EC\u8C8E\u8DDC\u8F17\u8FE1\u9006\u90F3\u922E\u9268\u9448\u94CC\u96AC\u9713\u999C\u9BE2\u9CB5\u9E91\u9F6F",
"nian":"\u5344\u54D6\u57DD\u59E9\u5E74\u5EFF\u5FF5\u62C8\u637B\u649A\u64B5\u6506\u6D8A\u6DF0\u78BE\u79CA\u79E5\u7C10\u824C\u8E68\u8E8E\u8F26\u8F87\u9B8E\u9BF0\u9C87\u9CB6\u9D47\u9ECF",
"niang":"\u5A18\u5B22\u5B43\u917F\u91B8\u91C0",
"niao":"\u5ACB\u5B1D\u5B32\u5C3F\u8132\u8311\u8312\u8526\u8885\u88CA\u892D\u9CE5\u9E1F",
"nie":"\u556E\u55A6\u55EB\u565B\u5699\u56C1\u56D3\u573C\u5B7C\u5B7D\u5D72\u5DAD\u5DD5\u5E07\u60D7\u634F\u63D1\u6470\u655C\u67BF\u69F7\u6AF1\u6D85\u7BDE\u7C4B\u7CF1\u7CF5\u8042\u8076\u8080\u81EC\u81F2\u82F6\u83CD\u8616\u8825\u8B98\u8E02\u8E17\u8E19\u8E51\u8EA1\u9269\u931C\u93B3\u9477\u9480\u954A\u954D\u95D1\u9667\u9689\u9873\u989E\u9F67",
"nin":"\u56DC\u60A8\u62F0\u810C",
"ning":"\u4F5E\u4FAB\u511C\u51DD\u549B\u5680\u5B23\u5B81\u5BCD\u5BD5\u5BD7\u5BDC\u5BE7\u62E7\u64F0\u67E0\u6A63\u6AB8\u6CDE\u6F9D\u6FD8\u72DE\u7370\u752F\u77C3\u804D\u8079\u85B4\u944F\u9B21\u9E0B",
"niu":"\u599E\u5FF8\u626D\u677B\u6C7C\u6C91\u7084\u725B\u725C\u72C3\u7D10\u7EBD\u83A5\u9215\u94AE\u9775",
"nong":"\u4FAC\u5102\u519C\u54DD\u5665\u5F04\u61B9\u630A\u6335\u6B01\u6D53\u6FC3\u7651\u79AF\u79FE\u7A60\u7E77\u8113\u81BF\u857D\u895B\u8FB2\u8FB3\u91B2\u9F48",
"nou":"\u5542\u69C8\u6ABD\u7373\u7FBA\u8028\u8B68\u8B73\u9392\u941E",
"nu":"\u4F16\u4F2E\u5089\u52AA\u5974\u5B65\u5F29\u6012\u6419\u782E\u7B2F\u80EC\u99D1\u9A7D",
"nv":"\u5973\u6067\u6712\u7C79\u8842\u8844\u91F9\u9495",
"nuan":"\u597B\u6696\u6E1C\u7156\u7157\u992A",
"nue":"\u759f\u8c11\u8650",
"nuo":"\u50A9\u513A\u558F\u61E6\u61E7\u632A\u63BF\u6426\u643B\u685B\u689B\u6992\u6A60\u71F6\u7878\u7A2C\u7A64\u7CD1\u7CE5\u7CEF\u8AFE\u8BFA\u8E43\u903D\u90CD\u9369\u9518\u9EC1",
"o":"\u5594",
"ou":"\u4E6F\u5076\u5418\u5455\u54E6\u5614\u5662\u5878\u591E\u6004\u616A\u6AD9\u6B27\u6B50\u6BB4\u6BC6\u6BEE\u6CA4\u6F1A\u71B0\u74EF\u750C\u7B7D\u8026\u8162\u8192\u8545\u85D5\u85F2\u8B33\u8BB4\u93C2\u97B0\u9D0E\u9DD7\u9E25\u9F75",
"pa":"\u556A\u5991\u5E0A\u5E15\u6015\u63B1\u6777\u6F56\u722C\u7436\u7685\u7B62\u8019\u8225\u8469\u8899\u8DB4",
"pai":"\u4FF3\u54CC\u5F98\u62CD\u6392\u68D1\u6D3E\u6E43\u724C\u72A4\u7305\u78D7\u7B84\u7C30\u848E\u8F2B\u9383",
"pan":"\u51B8\u5224\u53DB\u5762\u5ABB\u5E4B\u642B\u6500\u67C8\u69C3\u6C9C\u6CEE\u6EBF\u6F58\u700A\u708D\u723F\u7249\u7554\u7568\u76D8\u76E4\u76FC\u7705\u78D0\u7E0F\u84B0\u87E0\u88A2\u897B\u8A4A\u8DD8\u8E52\u8E63\u92EC\u939C\u947B\u97B6\u9816\u9D65",
"pang":"\u4E53\u5390\u55D9\u5ACE\u5E9E\u65C1\u6C78\u6C97\u6EC2\u7090\u7BE3\u802A\u80A8\u80D6\u80EE\u8196\u823D\u8783\u882D\u89AB\u9004\u96F1\u9736\u9AC8\u9C1F\u9CD1\u9F8E\u9F90",
"pao":"\u5228\u530F\u5486\u5789\u5945\u5E96\u629B\u62CB\u6CE1\u70AE\u70B0\u722E\u72CD\u75B1\u76B0\u7832\u791F\u792E\u812C\u8422\u86AB\u888D\u891C\u8DD1\u8EF3\u9784\u9E85\u9EAD",
"pei":"\u4F02\u4F69\u4FD6\u5478\u57F9\u59F5\u5D8F\u5E14\u600C\u65BE\u65C6\u67F8\u6BF0\u6C9B\u6D7F\u73EE\u7B29\u80A7\u80DA\u84DC\u8843\u88F4\u88F5\u8CE0\u8D54\u8F61\u8F94\u914D\u9185\u9307\u952B\u962B\u966A\u966B\u9708\u99B7",
"pen":"\u5460\u55AF\u55B7\u5674\u6B55\u6E53\u6FC6\u74EB\u76C6\u7FC9\u7FF8\u8450",
"peng":"\u4E76\u5017\u50B0\u527B\u5309\u55B8\u562D\u580B\u585C\u5873\u5DFC\u5F38\u5F6D\u6026\u6072\u6189\u62A8\u6337\u6367\u63BD\u6412\u670B\u6888\u68DA\u6916\u692A\u69F0\u6A25\u6CD9\u6D4C\u6DCE\u6F28\u6F30\u6F8E\u70F9\u71A2\u768F\u7830\u7851\u787C\u78B0\u78DE\u7A1D\u7AFC\u7BF7\u7E84\u80D3\u81A8\u8283\u8391\u84EC\u87DA\u87DB\u8E2B\u8EEF\u8F23\u930B\u945D\u959B\u95CF\u97F8\u97FC\u99CD\u9A2F\u9AFC\u9B05\u9B14\u9D6C\u9E4F",
"pi":"\u4E15\u4EF3\u4F13\u4F3E\u50FB\u5288\u5339\u5564\u567C\u567D\u568A\u56AD\u572E\u576F\u57E4\u58C0\u5AB2\u5AD3\u5C41\u5CAF\u5D25\u5E80\u6036\u6082\u61B5\u6279\u62AB\u62B7\u63CA\u64D7\u65C7\u6707\u6787\u6911\u698C\u6BD7\u6BD8\u6BDE\u6DE0\u6E12\u6F4E\u6FBC\u708B\u7137\u72C9\u72D3\u7435\u7513\u7588\u75B2\u75DE\u7656\u76AE\u7764\u7765\u7812\u78C7\u7914\u7915\u79DB\u79E0\u7B13\u7BFA\u7C32\u7D15\u7EB0\u7F74\u7F86\u7FCD\u801A\u80B6\u8134\u813E\u8157\u818D\u8298\u82C9\u868D\u86BD\u8731\u87B7\u882F\u8AC0\u8B6C\u8C7C\u8C7E\u8C94\u90B3\u90EB\u91FD\u921A\u9239\u925F\u9294\u92A2\u9303\u930D\u94CD\u95E2\u9630\u9674\u96A6\u9739\u99D3\u9AEC\u9B6E\u9B7E\u9B8D\u9C8F\u9D04\u9D67\u9DFF\u9E0A\u9F19",
"pian":"\u504F\u56E8\u5AA5\u6944\u6969\u7247\u728F\u7BC7\u7FE9\u80FC\u8141\u8991\u8ADA\u8ADE\u8C1D\u8CB5\u8CC6\u8E41\u99E2\u9A08\u9A17\u9A19\u9A88\u9A97\u9ABF\u9B78\u9DA3",
"piao":"\u50C4\u527D\u52E1\u560C\u5AD6\u5F6F\u5FB1\u6153\u65DA\u6B8D\u6F02\u72A5\u74E2\u76AB\u779F\u78E6\u7968\u7BFB\u7E39\u7F25\u7FF2\u85B8\u87B5\u91A5\u95DD\u9860\u98C3\u98C4\u98D8\u9B52",
"pie":"\u4E3F\u5AF3\u6486\u6487\u66BC\u6C15\u77A5\u82E4\u9405",
"pin":"\u54C1\u56AC\u59D8\u5A09\u5AD4\u5B2A\u62FC\u6729\u6980\u6C56\u725D\u73AD\u7415\u77C9\u7917\u7A66\u8058\u85B2\u8CA7\u8D2B\u983B\u9870\u9891\u98A6\u99AA\u9A5E",
"ping":"\u4E52\u4FDC\u51ED\u51F4\u546F\u576A\u5840\u5A26\u5C4F\u5C5B\u5CBC\u5E21\u5E32\u5E48\u5E73\u617F\u6191\u67B0\u6A98\u6D34\u6D84\u6DDC\u7129\u73B6\u74F6\u7501\u7539\u782F\u7AEE\u7BB3\u7C08\u7F3E\u8060\u8275\u82F9\u8353\u840D\u84F1\u860B\u86B2\u86E2\u8A55\u8BC4\u8EFF\u8F27\u90F1\u9829\u9B83\u9C86",
"po":"\u53F5\u54F1\u5619\u5761\u5964\u5A1D\u5A46\u5C00\u5CA5\u5CB6\u5EF9\u6540\u6622\u6AC7\u6CFC\u6D26\u6E8C\u6F51\u70DE\u73C0\u76A4\u7834\u7836\u7B38\u7C95\u84AA\u8522\u8B08\u8FEB\u9131\u9166\u91B1\u91D9\u9255\u93FA\u948B\u94B7\u9817\u9887\u99CA\u9B44",
"pou":"\u517A\u5256\u5485\u54DB\u54E3\u5837\u5A44\u6294\u6299\u634A\u638A\u7283\u7B81\u88D2\u9892",
"pu":"\u4EC6\u50D5\u530D\u5657\u5703\u5711\u5724\u57D4\u58A3\u5DEC\u5DED\u6251\u62AA\u64B2\u64C8\u6534\u6535\u666E\u669C\u66DD\u6734\u67E8\u6A38\u6A8F\u6C06\u6D66\u6EA5\u6F7D\u6FEE\u7011\u7087\u70F3\u749E\u75E1\u77A8\u7A59\u7E80\u8216\u8217\u8386\u83D0\u83E9\u8461\u84B1\u84B2\u8AE9\u8B5C\u8C31\u8D0C\u8E7C\u917A\u92EA\u93F7\u9420\u94FA\u9564\u9568\u9660\u99C7\u9BC6",
"qi":"\u4E03\u4E5E\u4E93\u4E9D\u4F01\u501B\u50DB\u5176\u51C4\u5258\u542F\u5447\u546E\u54A0\u5518\u552D\u5553\u5554\u555F\u5601\u5650\u5668\u573B\u57FC\u5921\u5947\u5951\u59BB\u5A38\u5A4D\u5C7A\u5C82\u5C90\u5C93\u5D0E\u5D5C\u5E3A\u5F03\u5FEF\u6053\u60BD\u6112\u612D\u617C\u617D\u6187\u61A9\u61E0\u621A\u637F\u6391\u6456\u6589\u658A\u65C2\u65D7\u6675\u66A3\u671E\u671F\u675E\u67D2\u6814\u6816\u6864\u687C\u68C4\u68CA\u68CB\u68E8\u68F2\u69BF\u69ED\u6AB1\u6AC0\u6B2B\u6B3A\u6B67\u6C14\u6C17\u6C23\u6C54\u6C7D\u6C8F\u6CE3\u6DC7\u6DD2\u6E0F\u6E46\u6E47\u6F06\u6FDD\u7081\u7309\u7382\u7398\u7426\u742A\u7482\u7508\u7566\u75A7\u76C0\u76F5\u77F5\u780C\u7881\u7895\u789B\u78B6\u78CE\u78DC\u78E7\u78E9\u7918\u7941\u7947\u7948\u797A\u79A5\u7AD2\u7C2F\u7C31\u7C4F\u7CB8\u7D2A\u7DA5\u7DA6\u7DA8\u7DAE\u7DBA\u7DC0\u7DD5\u7E83\u7EEE\u7F3C\u7F4A\u8006\u80B5\u8110\u81CD\u8269\u8291\u829E\u82AA\u8360\u8401\u840B\u847A\u8572\u85BA\u85C4\u8604\u8691\u8694\u869A\u86F4\u871D\u871E\u87A7\u87FF\u8810\u88FF\u8900\u8904\u8A16\u8AC6\u8AEC\u8AFF\u8BAB\u8C48\u8D77\u8DC2\u8E11\u8E26\u8E4A\u8EDD\u8FC4\u8FC9\u9094\u90EA\u91EE\u9321\u93DA\u951C\u95D9\u970B\u980E\u9880\u9A0E\u9A0F\u9A39\u9A90\u9A91\u9B10\u9B3E\u9B3F\u9B4C\u9B55\u9BD5\u9C2D\u9CAF\u9CCD\u9D78\u9D80\u9D88\u9E92\u9EA1\u9F4A\u9F50",
"qia":"\u51BE\u5736\u5E22\u6070\u6118\u62E4\u6390\u6B8E\u6D3D\u7848\u845C\u8DD2\u9160\u9790\u9AC2",
"qian":"\u4E79\u4E7E\u4EDF\u4EF1\u4F23\u4F65\u4FD4\u5029\u5042\u5094\u50C9\u5119\u51F5\u520B\u524D\u5343\u55DB\u5731\u5732\u5811\u5879\u5898\u58CD\u5977\u5A5C\u5A8A\u5B31\u5B6F\u5C8D\u5C92\u5D4C\u5D70\u5FF4\u6093\u60AD\u6106\u614A\u6173\u6266\u6272\u62D1\u62EA\u6394\u63AE\u63F5\u6434\u647C\u6481\u6510\u6511\u6513\u6744\u68C8\u6920\u69A9\u69CF\u69E7\u6A6C\u6AB6\u6ACF\u6B20\u6B26\u6B49\u6B6C\u6C58\u6C67\u6D45\u6DFA\u6F5B\u6F5C\u6FF3\u704A\u7275\u727D\u7698\u7ACF\u7B7E\u7B9D\u7B9E\u7BCF\u7BDF\u7C3D\u7C56\u7C64\u7C81\u7DAA\u7E34\u7E7E\u7F31\u7FAC\u80B7\u8181\u81E4\u828A\u82A1\u831C\u833E\u8368\u84A8\u8533\u8541\u8654\u8688\u8738\u8930\u8AD0\u8B19\u8B74\u8C26\u8C34\u8C38\u8EE1\u8F24\u8FC1\u9063\u9077\u91FA\u9206\u9210\u9246\u9257\u925B\u92AD\u9322\u9386\u93F2\u9431\u9453\u948E\u94A4\u94B1\u94B3\u94C5\u9621\u96C3\u97C6\u9845\u9A1A\u9A1D\u9A2B\u9A9E\u9B1C\u9B1D\u9C2C\u9D6E\u9E50\u9ED4\u9EDA",
"qiang":"\u515B\u545B\u5534\u55C6\u55F4\u588F\u5899\u58BB\u5AF1\u5B19\u5D88\u5EE7\u5F37\u5F3A\u6215\u6217\u6227\u62A2\u6436\u6464\u65A8\u67AA\u690C\u69CD\u6A2F\u6AA3\u6EAC\u6F12\u709D\u7197\u7244\u7246\u7310\u7347\u73B1\u7437\u7472\u74E9\u7BEC\u7E48\u7E66\u7F8C\u7F97\u7F9F\u7FA5\u7FAB\u7FBB\u8154\u8262\u8503\u8537\u8594\u8620\u8723\u8941\u8B12\u8DC4\u8E4C\u8E61\u9306\u9397\u93D8\u93F9\u9516\u9535\u956A",
"qiao":"\u4E54\u4FA8\u4FCF\u50D1\u50FA\u5281\u55AC\u563A\u589D\u58BD\u5AF6\u5CED\u5D6A\u5DE7\u5E29\u5E67\u6084\u6100\u6194\u64AC\u64BD\u6572\u6865\u69D7\u6A35\u6A47\u6A4B\u6BBC\u71C6\u729E\u7644\u7744\u77A7\u7857\u785A\u78BB\u78FD\u7904\u7A8D\u7AC5\u7E51\u7E70\u7F32\u7FD8\u7FF9\u834D\u835E\u83EC\u854E\u85EE\u8A9A\u8B59\u8BEE\u8C2F\u8DAB\u8DAC\u8DF7\u8E0D\u8E7A\u8E7B\u8E88\u90FB\u9117\u9121\u9125\u91E5\u936B\u936C\u9408\u9430\u9539\u9657\u9792\u9798\u97A9\u97BD\u97D2\u981D\u9866\u9AB9\u9ADA\u9ADC",
"qie":"\u4E14\u503F\u5207\u5327\u5392\u59BE\u602F\u608F\u60EC\u611C\u6308\u6705\u6D2F\u6DC1\u767F\u7A55\u7A83\u7ACA\u7B21\u7BA7\u7BCB\u7C61\u7DC1\u807A\u82C6\u8304\u85D2\u86EA\u8E25\u9365\u9411\u9532\u9B65\u9BDC",
"qin":"\u4EB2\u4FB5\u52E4\u5422\u5423\u551A\u55EA\u5659\u5745\u57C1\u5A87\u5AC0\u5BD1\u5BDD\u5BE2\u5BF4\u5D5A\u5D94\u5E88\u61C3\u61C4\u628B\u6366\u63FF\u6407\u64B3\u64D2\u65B3\u6611\u68AB\u6A8E\u6B3D\u6C81\u6EB1\u6FBF\u7019\u73E1\u7434\u7439\u763D\u77DD\u79BD\u79E6\u7B09\u7D85\u8039\u82A9\u82B9\u83E3\u83E6\u83F3\u85FD\u8699\u8793\u87BC\u8804\u887E\u89AA\u8A9B\u8D7A\u8D7E\u9219\u92DF\u94A6\u9513\u96C2\u9772\u9849\u99F8\u9A8E\u9BBC\u9CF9",
"qing":"\u503E\u50BE\u512C\u51CA\u5260\u52CD\u537F\u570A\u57E5\u591D\u5E86\u5EBC\u5ECE\u60C5\u6176\u6385\u64CE\u64CF\u6674\u6692\u68FE\u6A08\u6AA0\u6ABE\u6AE6\u6B91\u6BB8\u6C22\u6C2B\u6C30\u6DF8\u6E05\u6F00\u6FEA\u7520\u7858\u7883\u78EC\u7B90\u7F44\u82D8\u845D\u873B\u8ACB\u8B26\u8BF7\u8EFD\u8F15\u8F7B\u90EC\u944B\u9751\u9752\u9758\u9803\u9877\u9BD6\u9CAD\u9EE5",
"qiong":"\u511D\u536D\u5B86\u60F8\u618C\u684F\u6A69\u712A\u712D\u7162\u718D\u743C\u749A\u74CA\u74D7\u7758\u778F\u7A77\u7A79\u7AAE\u7AC6\u7B3B\u7B47\u823C\u8315\u85D1\u85ED\u86E9\u86EC\u8D79\u8DEB\u909B\u928E",
"qiu":"\u4E18\u4E20\u4FC5\u53F4\u5512\u56DA\u5775\u5A9D\u5BC8\u5D37\u5DEF\u5DF0\u6058\u624F\u641D\u6739\u6882\u6978\u6B8F\u6BEC\u6C42\u6C53\u6CC5\u6D57\u6E1E\u6E6D\u716A\u72B0\u738C\u7403\u7486\u76B3\u76DA\u79CB\u79CC\u7A50\u7BCD\u7CD7\u7D0C\u7D7F\u7DE7\u808D\u838D\u8429\u84F2\u8612\u866C\u866F\u86AF\u86F7\u8764\u8775\u87D7\u8824\u88D8\u89D3\u89E9\u8A04\u8A05\u8CD5\u8D47\u8DA5\u900E\u9011\u9052\u90B1\u914B\u9194\u91DA\u91FB\u92B6\u97A6\u97A7\u9B82\u9BC4\u9C0C\u9C0D\u9C3D\u9C43\u9CC5\u9D6D\u9D96\u9E59\u9F3D\u9F9D",
"qu":"\u4F39\u4F49\u4F62\u521E\u52AC\u5324\u5337\u533A\u5340\u53BA\u53BB\u53D6\u547F\u5765\u5A36\u5C48\u5C96\u5CA8\u5CB4\u5D87\u5FC2\u6188\u6235\u62BE\u657A\u65AA\u66F2\u6710\u6711\u6B0B\u6C0D\u6D40\u6DED\u6E20\u7048\u7496\u74A9\u766F\u78F2\u795B\u7AD8\u7AEC\u7B41\u7C67\u7CAC\u7D36\u7D47\u7FD1\u7FF5\u801D\u80CA\u80E0\u81DE\u83C3\u844B\u8556\u8627\u86C6\u86D0\u877A\u87B6\u87DD\u8837\u883C\u8850\u8862\u88AA\u89B0\u89B7\u89BB\u89D1\u8A53\u8A58\u8AB3\u8BCE\u8D8B\u8DA3\u8DA8\u8EA3\u8EAF\u8EC0\u8EE5\u8FF2\u90E5\u947A\u95B4\u95C3\u9612\u9639\u99C6\u99C8\u9A45\u9A71\u9AF7\u9B7C\u9C38\u9C4B\u9D1D\u9E1C\u9E32\u9EAE\u9EAF\u9EB4\u9EB9\u9EE2\u9F01\u9F29\u9F72\u9F8B",
"quan":"\u4F7A\u5168\u5238\u529D\u52E7\u52F8\u5573\u5708\u570F\u57E2\u59FE\u5A58\u5B49\u5CD1\u5DCF\u5DFB\u606E\u609B\u60D3\u62F3\u643C\u6743\u68EC\u6926\u697E\u6A29\u6B0A\u6C71\u6CC9\u6D24\u6E76\u70C7\u7276\u7277\u7288\u72AC\u72AD\u7454\u753D\u754E\u75CA\u7842\u7B4C\u7D5F\u7DA3\u7E13\u7EFB\u8143\u8343\u8472\u8647\u8737\u8838\u89E0\u8A6E\u8BE0\u8DE7\u8E21\u8F07\u8F81\u919B\u9293\u9409\u94E8\u95CE\u97CF\u9874\u98A7\u99E9\u9A21\u9B08\u9C01\u9CC8\u9F64",
"que":"\u5374\u537B\u57C6\u5859\u58A7\u5D05\u60AB\u6128\u6164\u6409\u69B7\u6BC3\u7094\u71E9\u7638\u76B5\u785E\u786E\u788F\u78BA\u7910\u792D\u7F3A\u8203\u849B\u8D9E\u95CB\u95D5\u9615\u9619\u96C0\u9D72\u9E4A",
"qun":"\u56F7\u590B\u5BAD\u5CEE\u5E2C\u7FA3\u7FA4\u88D9\u88E0\u8F11\u9021",
"ran":"\u5184\u5189\u5465\u562B\u59CC\u5AA3\u67D3\u6A6A\u7136\u71C3\u73C3\u7E4E\u80B0\u82D2\u8485\u86A6\u86BA\u887B\u8887\u88A1\u9AE5\u9AEF",
"rang":"\u5134\u52F7\u56B7\u58CC\u58E4\u61F9\u6518\u703C\u7219\u737D\u74E4\u79B3\u7A63\u7A70\u7E95\u8618\u8B72\u8B93\u8BA9\u8E9F\u9B24",
"rao":"\u5A06\u5B08\u6270\u64FE\u6861\u6A48\u72AA\u7E5E\u7ED5\u835B\u8558\u8953\u9076\u96A2\u9952\u9976",
"ruo":"\u504C\u53D2\u5A7C\u5D76\u5F31\u633C\u637C\u6949\u6E03\u712B\u7207\u7BAC\u7BDB\u82E5\u84BB\u9100\u9C19\u9C2F\u9DB8",
"re":"\u60F9\u70ED\u71B1",
"ren":"\u4EBA\u4EBB\u4EC1\u4EDE\u4EED\u4EFB\u5203\u5204\u58EC\u598A\u59D9\u5C7B\u5FC8\u5FCD\u5FCE\u6041\u6268\u6732\u6752\u6820\u6823\u6895\u68EF\u7263\u79C2\u79F9\u7A14\u7D09\u7D1D\u7D4D\u7D9B\u7EAB\u7EB4\u8095\u814D\u82A2\u834F\u8375\u845A\u887D\u88B5\u8A12\u8A8D\u8BA4\u8BB1\u8EB5\u8ED4\u8F6B\u9213\u928B\u976D\u9771\u97CC\u97E7\u98EA\u9901\u996A\u9B5C\u9D40",
"reng":"\u4ECD\u6254\u793D\u82BF\u8FB8\u967E",
"ri":"\u56F8\u65E5\u91F0\u9224\u99B9\u9A72",
"rong":"\u5087\u5197\u5AB6\u5AC6\u5B2B\u5B82\u5BB9\u5CF5\u5D58\u5D64\u5DB8\u620E\u6408\u6411\u6449\u66E7\u6804\u6995\u69AE\u69B5\u6BE7\u6C04\u6EB6\u701C\u70FF\u7194\u7203\u72E8\u7462\u7A41\u7A43\u7D68\u7E19\u7ED2\u7FA2\u809C\u8319\u8338\u8363\u84C9\u877E\u878D\u878E\u8811\u8923\u8EF5\u9394\u9555\u99E5\u9AF6",
"rou":"\u53B9\u5A83\u5B8D\u63C9\u67D4\u697A\u6E18\u7163\u7448\u74C7\u79B8\u7C88\u7CC5\u8089\u816C\u8447\u875A\u8E42\u8F2E\u9352\u97A3\u97D6\u9A25\u9C07\u9D94",
"ru":"\u4E73\u4F9E\u5112\u5165\u55D5\u5685\u5982\u5AB7\u5B2C\u5B7A\u5DBF\u5E24\u6256\u64E9\u66D8\u6741\u6847\u6C5D\u6D33\u6E2A\u6EBD\u6FE1\u71F8\u7B4E\u7E1F\u7E7B\u7F1B\u8097\u8339\u8498\u84D0\u8560\u85B7\u8815\u88BD\u8925\u8966\u8FB1\u8FBC\u909A\u910F\u91B9\u92A3\u94F7\u986C\u98A5\u9C6C\u9CF0\u9D11\u9D3D",
"ruan":"\u5044\u5827\u58D6\u5A86\u5AF0\u611E\u648B\u670A\u744C\u74C0\u789D\u791D\u7DDB\u800E\u815D\u8761\u8EDF\u8F2D\u8F6F\u962E",
"rui":"\u53E1\u58E1\u6798\u6875\u6A64\u6C6D\u745E\u7524\u777F\u7DCC\u7E60\u82AE\u854A\u854B\u8564\u8602\u8603\u868B\u8739\u92B3\u92ED\u9510",
"run":"\u6A4D\u6DA6\u6F64\u958F\u95A0\u95F0",
"sa":"\u4EE8\u5345\u644B\u6492\u680D\u686C\u6AD2\u6D12\u6F75\u7051\u810E\u8428\u85A9\u8A2F\u9212\u9491\u96A1\u9778\u98AF\u98D2\u99BA",
"sai":"\u50FF\u55EE\u5625\u567B\u585E\u6122\u63CC\u6BE2\u6BF8\u7C3A\u816E\u8644\u8CFD\u8D5B\u984B\u9C13\u9CC3",
"san":"\u4E09\u4F1E\u4FD5\u5098\u5381\u53C1\u58ED\u5F0E\u6563\u6A75\u6BF5\u6BF6\u6BFF\u7299\u7CC1\u7CC2\u7CDD\u7CE3\u7CE4\u7E56\u93D2\u9590\u994A\u9993\u9B16",
"sang":"\u4E27\u55AA\u55D3\u6421\u6851\u6852\u69E1\u78C9\u892C\u939F\u9859\u98A1",
"sao":"\u57FD\u5AC2\u6145\u626B\u6383\u63BB\u6414\u6C09\u6E9E\u7619\u77C2\u7E45\u7F2B\u81CA\u98BE\u9A12\u9A37\u9A9A\u9ADE\u9C20\u9C62\u9CCB",
"se":"\u556C\u55C7\u61CE\u64CC\u681C\u69EE\u6B6E\u6B70\u6D13\u6DA9\u6E0B\u6F80\u6F81\u6FC7\u6FCF\u7012\u7417\u745F\u74B1\u7637\u7A51\u7A61\u7A6F\u7BF8\u7E07\u7E6C\u8053\u8272\u88C7\u8942\u8B45\u8F56\u92AB\u93FC\u94EF\u95AA\u96ED\u98CB\u9B19",
"sen":"\u68ee",
"seng":"\u50e7",
"sha":"\u4E77\u503D\u50BB\u510D\u5239\u5526\u553C\u5565\u55A2\u5E39\u6331\u6740\u699D\u6A27\u6B43\u6BBA\u6C99\u715E\u7300\u75E7\u7802\u7870\u7B91\u7C86\u7D17\u7E4C\u7E7A\u7EB1\u7FDC\u7FE3\u838E\u8410\u8531\u88DF\u93A9\u94E9\u95AF\u95B7\u970E\u9B66\u9BCA\u9BCB\u9CA8",
"shai":"\u6652\u66EC\u7B5B\u7BE9\u7C01\u7C1B",
"shan":"\u5093\u50D0\u5220\u522A\u5261\u527C\u5584\u5607\u5738\u57CF\u58A0\u58A1\u59CD\u59D7\u5B17\u5C71\u5E53\u5F61\u6247\u633B\u6427\u64C5\u657E\u6671\u66D1\u6749\u6763\u692B\u6A3F\u6A86\u6C55\u6F78\u6F98\u7057\u70B6\u70FB\u7154\u717D\u718C\u72E6\u73CA\u759D\u75C1\u7752\u78F0\u7B18\u7E3F\u7E55\u7F2E\u7FB4\u7FB6\u8120\u81B3\u81BB\u8222\u829F\u82EB\u852A\u87EE\u87FA\u886B\u89A2\u8A15\u8B06\u8B71\u8BAA\u8D0D\u8D61\u8D78\u8DDA\u8ED5\u9096\u912F\u91E4\u928F\u9425\u9490\u9583\u958A\u95EA\u9655\u965D\u994D\u9A38\u9A9F\u9BC5\u9C53\u9C54\u9CDD",
"shang":"\u4E04\u4E0A\u4EE9\u4F24\u50B7\u5546\u57A7\u5892\u5C19\u5C1A\u6066\u6113\u616F\u6244\u664C\u6B87\u6BA4\u6EF3\u6F21\u71B5\u7DD4\u7EF1\u850F\u87AA\u88F3\u89DE\u89F4\u8B2A\u8CDE\u8D4F\u945C\u9B3A",
"shao":"\u52AD\u52FA\u5372\u54E8\u5A0B\u5C11\u5F30\u634E\u65D3\u67D6\u68A2\u6F72\u70E7\u713C\u713D\u71D2\u73BF\u7A0D\u7B72\u7D39\u7DA4\u7ECD\u8244\u828D\u82D5\u83A6\u8414\u8571\u86F8\u8891\u8F0E\u90B5\u97F6\u98B5\u9AFE\u9BB9",
"she":"\u4F58\u538D\u5399\u5962\u5C04\u5F3D\u6151\u6174\u61FE\u6368\u6442\u6444\u6475\u651D\u6AA8\u6B07\u6D89\u6DBB\u6E09\u6EE0\u7044\u731E\u7572\u793E\u820C\u820D\u820E\u850E\u8675\u86C7\u86E5\u8802\u8A2D\u8BBE\u8CD2\u8CD6\u8D4A\u8D66\u8F0B\u97D8\u9A07\u9E9D",
"shen":"\u4F38\u4F81\u4FBA\u515F\u547B\u54C2\u5814\u59BD\u59FA\u5A20\u5A76\u5B38\u5BA1\u5BB7\u5BE9\u5C7E\u5CF7\u5F1E\u613C\u614E\u625F\u628C\u661A\u66CB\u67DB\u692E\u6939\u698A\u6C20\u6C88\u6D81\u6DF1\u6E16\u6E17\u6EF2\u700B\u71CA\u73C5\u751A\u7521\u7527\u7533\u7606\u762E\u7712\u7718\u77AB\u77E4\u77E7\u7837\u795E\u7973\u7A7C\u7C76\u7C78\u7D33\u7EC5\u7F59\u7F67\u80BE\u80C2\u8124\u814E\u8460\u84E1\u8518\u8593\u8703\u88D1\u89BE\u8A20\u8A37\u8A75\u8AD7\u8B85\u8BDC\u8C02\u8C09\u8EAB\u90A5\u926E\u92E0\u9823\u99EA\u9B6B\u9BD3\u9BF5\u9C30\u9C3A\u9CB9\u9D62",
"sheng":"\u5057\u5269\u5270\u52DD\u5347\u544F\u5723\u58AD\u58F0\u5D4A\u61B4\u6598\u6607\u665F\u6660\u66FB\u67A1\u69BA\u6A73\u6B85\u6CE9\u6E11\u6E3B\u6E66\u6FA0\u713A\u7272\u73C4\u741E\u751F\u7525\u76DB\u7701\u771A\u7AD4\u7B19\u7E04\u7E69\u7EF3\u8056\u8072\u80DC\u82FC\u8542\u8B5D\u8CB9\u8CF8\u924E\u935F\u9629\u965E\u9679\u9C66\u9D7F\u9F2A",
"shi":"\u4E16\u4E17\u4E68\u4E8A\u4E8B\u4EC0\u4ED5\u4F66\u4F7F\u4F8D\u5158\u5159\u519F\u52BF\u52E2\u5341\u534B\u53D3\u53F2\u545E\u5469\u55DC\u566C\u57D8\u5852\u58EB\u5931\u596D\u59CB\u59FC\u5B15\u5B9E\u5B9F\u5BA4\u5BA9\u5BD4\u5BE6\u5C38\u5C4D\u5C4E\u5CD5\u5D3C\u5D75\u5E02\u5E08\u5E2B\u5F0F\u5F11\u5F12\u5FA5\u5FD5\u6040\u6043\u623A\u62ED\u62FE\u63D3\u65BD\u65F6\u65F9\u662F\u6630\u6642\u67BE\u67F9\u67FF\u683B\u6981\u69AF\u6AA1\u6C0F\u6D49\u6E5C\u6E64\u6E7F\u6EA1\u6EAE\u6EBC\u6FA8\u6FD5\u70BB\u70D2\u7176\u72EE\u7345\u7461\u74E7\u7702\u770E\u7721\u7757\u77E2\u77F3\u793A\u793B\u794F\u7ACD\u7B36\u7B39\u7B6E\u7BB7\u7BD2\u7C2D\u7C42\u7C6D\u7D41\u8210\u8213\u83B3\u8479\u8492\u8494\u84CD\u8671\u8672\u8680\u8755\u8768\u87AB\u8906\u8937\u896B\u8979\u8996\u89C6\u89E2\u8A66\u8A69\u8A93\u8ADF\u8AE1\u8B1A\u8B58\u8BC6\u8BD5\u8BD7\u8C25\u8C55\u8CB0\u8D33\u8EFE\u8F7C\u8FBB\u9002\u901D\u9048\u9069\u907E\u90BF\u917E\u91C3\u91C8\u91CA\u91CB\u91F6\u9230\u9242\u9243\u9247\u9250\u927D\u92B4\u9366\u94C8\u98DF\u98E0\u98FE\u991D\u9963\u9970\u99DB\u9A76\u9B96\u9BF4\u9C18\u9C23\u9C24\u9CA5\u9CBA\u9CF2\u9CFE\u9DB3\u9E24\u9F2B\u9F2D\u9F5B",
"shou":"\u517D\u53CE\u53D7\u552E\u57A8\u58FD\u5900\u5B88\u5BFF\u624B\u624C\u6388\u6536\u6DAD\u72E9\u7363\u7378\u75E9\u7626\u7DAC\u7EF6\u8184\u824F\u93C9\u9996",
"shu":"\u4E66\u4FB8\u500F\u5010\u5135\u53D4\u54B0\u587E\u5885\u59DD\u5A4C\u5B70\u5C0C\u5C17\u5EB6\u5EBB\u6037\u6055\u620D\u6292\u6393\u6445\u6504\u6570\u6578\u668F\u6691\u66D9\u66F8\u672E\u672F\u675F\u6778\u67A2\u67D5\u6811\u68B3\u6A1E\u6A39\u6A7E\u6B8A\u6BB3\u6BF9\u6BFA\u6CAD\u6DD1\u6F31\u6F44\u6F7B\u6F8D\u6FD6\u702D\u7102\u719F\u7479\u74B9\u758E\u758F\u7659\u79EB\u7AD6\u7AEA\u7C54\u7CEC\u7D13\u7D49\u7D80\u7EBE\u7F72\u8167\u8212\u8357\u83FD\u8481\u852C\u85A5\u85AF\u85F7\u866A\u8700\u8834\u8853\u88CB\u8961\u8969\u8C4E\u8D16\u8D4E\u8DFE\u8E08\u8ED7\u8F38\u8F93\u8FF0\u9103\u9265\u9330\u93E3\u964E\u9B9B\u9C6A\u9C70\u9D68\u9D90\u9D91\u9E00\u9ECD\u9F20\u9F21",
"shua":"\u5237\u5530\u800D\u8A9C",
"shuai":"\u535B\u5E05\u5E25\u6454\u7529\u87C0\u8870",
"shuan":"\u62F4\u6813\u6DAE\u8168\u9582\u95E9",
"shuang":"\u53CC\u587D\u5B40\u5B47\u6161\u6A09\u6B06\u6EDD\u7040\u723D\u7935\u7E14\u826D\u93EF\u96D9\u971C\u9A3B\u9A66\u9AA6\u9DDE\u9E18\u9E74",
"shui":"\u542E\u54FE\u5E28\u696F\u6A53\u6C34\u6C35\u6C3A\u6D97\u6D9A\u7761\u779A\u77A4\u77AC\u7971\u7A05\u7A0E\u813D\u821C\u8563\u88DE\u8AAA\u8AAC\u8AB0\u8BF4\u8C01\u9596\u9806\u987A\u9B0A",
"shuo":"\u55CD\u55FD\u5981\u6420\u6714\u69CA\u6B36\u70C1\u720D\u7361\u77DF\u7855\u78A9\u7BBE\u84B4\u9399\u9460\u94C4",
"si":"\u4E1D\u4E7A\u4E96\u4F3A\u4F3C\u4F40\u4FA1\u4FDF\u4FEC\u5129\u5155\u51D8\u53AE\u53B6\u53F8\u549D\u55E3\u5636\u565D\u56DB\u59D2\u5A30\u5AA4\u5B60\u5BFA\u5DF3\u5EDD\u601D\u6056\u6495\u65AF\u67B1\u67F6\u68A9\u6952\u69B9\u6B7B\u6C5C\u6CC0\u6CD7\u6CE4\u6D0D\u6D98\u6F8C\u7003\u71CD\u726D\u78C3\u7940\u7997\u79A0\u79A9\u79C1\u7AE2\u7B25\u7CF9\u7D72\u7DE6\u7E9F\u7F0C\u7F52\u7F73\u801C\u8082\u8086\u856C\u857C\u8652\u86F3\u8724\u8784\u87A6\u87D6\u87F4\u8997\u8C84\u91F2\u923B\u9270\u92AF\u92D6\u9376\u9401\u9536\u98B8\u98D4\u98E4\u98FC\u9972\u99DF\u9A03\u9A26\u9A77\u9DE5\u9E36\u9F36",
"song":"\u502F\u50B1\u51C7\u5A00\u5B8B\u5D27\u5D69\u5D77\u5EBA\u5FEA\u6002\u609A\u612F\u616B\u61BD\u6352\u677E\u6780\u67A9\u67D7\u68A5\u6AA7\u6DDE\u6FCD\u7879\u7AE6\u8038\u8073\u83D8\u8719\u8A1F\u8AA6\u8BBC\u8BF5\u9001\u93B9\u980C\u9882\u9938\u99F7\u9B06",
"sou":"\u5081\u51C1\u53DC\u53DF\u55D6\u55FE\u5EC0\u5ECB\u635C\u641C\u6457\u64DE\u64FB\u6AE2\u6EB2\u7340\u7636\u778D\u8258\u8490\u84C3\u85AE\u85EA\u878B\u910B\u9199\u93AA\u953C\u98BC\u98D5\u993F\u998A\u9A2A",
"su":"\u4FD7\u5083\u50F3\u55C9\u56CC\u5850\u5851\u5919\u5ACA\u5BBF\u612B\u612C\u619F\u6880\u69A1\u6A0E\u6A15\u6A5A\u6AEF\u6B90\u6CDD\u6D2C\u6D91\u6EAF\u6EB8\u6F5A\u6F65\u738A\u73DF\u749B\u7526\u78BF\u7A23\u7A4C\u7AA3\u7C0C\u7C9B\u7C9F\u7D20\u7E24\u8083\u8085\u8186\u82CF\u850C\u85D7\u8607\u8613\u89EB\u8A34\u8B16\u8BC9\u8C21\u8D9A\u8E5C\u901F\u9061\u906C\u9165\u92C9\u9917\u9A4C\u9A95\u9BC2\u9C50\u9DEB\u9E54",
"suan":"\u5334\u72FB\u75E0\u7958\u7B07\u7B6D\u7B97\u849C\u9178",
"sui":"\u4E97\u5020\u54F8\u57E3\u590A\u5B18\u5C81\u5D57\u65DE\u6A96\u6B72\u6B73\u6D7D\u6ED6\u6FBB\u6FC9\u7021\u716B\u71A3\u71E7\u74B2\u74CD\u772D\u775F\u7762\u7815\u788E\u795F\u79AD\u7A42\u7A57\u7A5F\u7C8B\u7D8F\u7E40\u7E50\u7E78\u7EE5\u813A\u81B8\u8295\u837D\u837E\u8470\u867D\u895A\u8AB6\u8B62\u8C07\u8CE5\u9040\u9042\u9083\u9406\u9429\u968B\u968F\u96A7\u96A8\u96D6\u9796\u9AC4\u9AD3",
"sun":"\u5B59\u5B6B\u5DFA\u635F\u640D\u640E\u69AB\u69C2\u6F60\u72F2\u733B\u7543\u7B0B\u7B4D\u7BB0\u7C28\u836A\u84C0\u8575\u859E\u93A8\u96BC\u98E7\u98F1\u9DBD",
"suo":"\u509E\u5506\u5522\u55E6\u55E9\u5A11\u60E2\u6240\u6332\u644D\u669B\u686B\u68AD\u6E91\u6EB9\u7410\u7411\u7463\u7743\u7C11\u7C14\u7D22\u7E2E\u7F29\u7FA7\u838F\u84D1\u8736\u8D96\u9024\u938D\u9396\u93BB\u93BC\u93C1\u9501\u9AFF\u9BBB",
"ta":"\u4ED6\u4FA4\u549C\u5683\u56BA\u584C\u5854\u5896\u5B83\u5D09\u631E\u6428\u64BB\u6999\u69BB\u6BFE\u6DBE\u6EBB\u6FBE\u6FCC\u7260\u72E7\u736D\u737A\u7942\u79A2\u891F\u8968\u8ABB\u8B76\u8DBF\u8E0F\u8E4B\u8E79\u8EA2\u905D\u9062\u9248\u9314\u94CA\u95D2\u95E5\u95FC\u9618\u979C\u97B3\u9B99\u9C28\u9CCE",
"tai":"\u5113\u51AD\u53F0\u56FC\u576E\u592A\u5933\u5B2F\u5B61\u5FF2\u6001\u614B\u62AC\u64E1\u65F2\u6AAF\u6C70\u6CF0\u6E99\u70B1\u70B2\u71E4\u73C6\u7B88\u7C49\u7C8F\u80BD\u80CE\u81FA\u8226\u82D4\u83ED\u85B9\u8DC6\u90B0\u915E\u9226\u949B\u98B1\u99D8\u9A80\u9B90\u9C90",
"tan":"\u5013\u509D\u50CB\u53F9\u5574\u55FF\u5606\u563D\u574D\u575B\u5766\u57EE\u58B0\u58B5\u58C7\u58DC\u5A52\u5F3E\u5FD0\u6039\u60D4\u619B\u61B3\u61BB\u63A2\u644A\u64A2\u64F9\u6524\u6619\u66BA\u66C7\u6983\u6A5D\u6A80\u6B4E\u6BEF\u6E60\u6EE9\u6F6C\u6F6D\u7058\u70AD\u74AE\u75D1\u75F0\u762B\u7671\u78B3\u7F48\u7F4E\u8211\u8215\u83FC\u85EB\u8892\u8962\u8983\u8AC7\u8B5A\u8B60\u8C08\u8C2D\u8C9A\u8CAA\u8CE7\u8D2A\u8D55\u90EF\u9188\u9193\u91B0\u926D\u931F\u94BD\u952C\u9843\u9DE4",
"tang":"\u5018\u5052\u508F\u50A5\u513B\u528F\u5510\u557A\u5621\u5763\u5802\u5858\u5D63\u5E11\u6203\u642A\u6465\u66ED\u68E0\u69B6\u6A18\u6A56\u6C64\u6DCC\u6E6F\u6E8F\u6F1F\u70EB\u717B\u71D9\u7223\u746D\u77D8\u78C4\u799F\u7BD6\u7CC3\u7CD6\u7CDB\u7FB0\u8025\u8185\u819B\u84CE\u859A\u876A\u8797\u87B3\u8D6F\u8D9F\u8E3C\u8E5A\u8EBA\u910C\u91A3\u9395\u93B2\u93DC\u940B\u9482\u94F4\u954B\u9557\u95DB\u969A\u97BA\u9933\u9939\u9944\u9967\u9DB6\u9F1E",
"tao":"\u4ED0\u530B\u54B7\u5555\u5932\u5957\u5ACD\u5E4D\u5F22\u6146\u638F\u642F\u6843\u68BC\u69C4\u6AAE\u6D2E\u6D9B\u6DD8\u6ED4\u6FE4\u746B\u7553\u7979\u7D5B\u7DAF\u7E1A\u7E27\u7EE6\u7EF9\u8404\u872A\u88EA\u8A0E\u8A5C\u8B1F\u8BA8\u8FEF\u9003\u9184\u92FE\u932D\u9676\u9780\u9789\u97B1\u97DC\u97EC\u98F8\u9940\u9955\u99E3\u9A0A\u9F17",
"te":"\u5FD1\u5FD2\u615D\u7279\u7286\u8126\u87D8\u8CA3\u92F1\u94FD",
"teng":"\u512F\u551E\u5E50\u6730\u6ED5\u6F1B\u75BC\u75CB\u7C50\u7C58\u7E22\u817E\u81AF\u85E4\u8645\u87A3\u8A8A\u8B04\u9086\u972F\u99E6\u9A30\u9A63\u9C27\u9F1F",
"ti":"\u4F53\u501C\u504D\u5243\u5254\u5397\u557C\u55C1\u568F\u5694\u5A9E\u5C49\u5C5C\u5D39\u5FB2\u608C\u6090\u60D5\u60D6\u60FF\u623B\u632E\u63A6\u63D0\u63E5\u66FF\u68AF\u6974\u6B52\u6BA2\u6D1F\u6D95\u6E27\u6F3D\u73F6\u7445\u74CB\u78AE\u7A0A\u7C4A\u7D88\u7DF9\u7EE8\u7F07\u7F64\u855B\u8599\u876D\u88FC\u8905\u8B15\u8DA7\u8DAF\u8E22\u8E44\u8E4F\u8EB0\u8EC6\u9016\u9037\u9046\u918D\u92BB\u9357\u941F\u9511\u984C\u9898\u9A20\u9AB5\u9AD4\u9AF0\u9B00\u9B04\u9BB7\u9BF7\u9CC0\u9D3A\u9D5C\u9D97\u9D99\u9DC8\u9DC9\u9E48",
"tian":"\u500E\u5172\u553A\u5861\u586B\u5929\u5A56\u5C47\u5FDD\u606C\u60BF\u6375\u63AD\u6437\u666A\u6B84\u6CBA\u6DDF\u6DFB\u6E49\u7420\u7471\u74B3\u751B\u751C\u7530\u754B\u7551\u7560\u75F6\u76F7\u7753\u777C\u78B5\u78CC\u7AB4\u7DC2\u80CB\u8146\u8214\u821A\u83FE\u89A5\u89CD\u8CDF\u915F\u932A\u95D0\u9617\u9754\u975D\u9766\u9902\u9D2B\u9DC6\u9DCF\u9EC7",
"tiao":"\u4F7B\u5B25\u5BA8\u5CA7\u5CB9\u5EA3\u604C\u6311\u65EB\u6640\u6713\u6761\u689D\u6A24\u773A\u7952\u7967\u7A95\u7AB1\u7B24\u7C9C\u7CF6\u7D69\u804E\u8101\u8280\u84DA\u84E8\u8729\u87A9\u899C\u8A82\u8D92\u8DF3\u8FE2\u92DA\u93A5\u94EB\u9797\u982B\u9AEB\u9BC8\u9C37\u9CA6\u9F60\u9F86",
"tie":"\u50E3\u546B\u5E16\u6017\u8051\u841C\u86C8\u8CBC\u8D34\u8DD5\u9244\u9295\u9421\u9422\u9435\u94C1\u98FB\u992E\u9A56\u9D29",
"ting":"\u4E6D\u4EAD\u4FB9\u505C\u5385\u539B\u542C\u5722\u5A17\u5A77\u5D49\u5E81\u5EAD\u5EF0\u5EF3\u5EF7\u633A\u686F\u6883\u695F\u69B3\u6C40\u6D8F\u6E1F\u6FCE\u70C3\u70F4\u70F6\u73FD\u753A\u7B73\u7D8E\u8013\u8064\u8074\u807C\u807D\u8121\u8247\u8248\u827C\u839B\u8476\u8713\u874F\u8A94\u8AEA\u9092\u92CC\u94E4\u95AE\u9706\u9793\u9832\u988B\u9F2E",
"tong":"\u4EDD\u4F5F\u50EE\u52ED\u540C\u54C3\u55F5\u56F2\u5CC2\u5E9D\u5F64\u6078\u615F\u6185\u6345\u664D\u66C8\u6723\u6850\u6876\u6A0B\u6A66\u6C03\u6D75\u6F7C\u70B5\u70D4\u71A5\u729D\u72EA\u735E\u75CC\u75DB\u772E\u77B3\u783C\u79F1\u7A5C\u7AE5\u7B52\u7B69\u7CA1\u7D67\u7D71\u7D82\u7EDF\u81A7\u833C\u84EA\u8692\u8855\u8D68\u901A\u916E\u9256\u9275\u9285\u94DC\u9907\u9BA6\u9C96",
"tou":"\u4EA0\u5077\u5078\u5934\u59B5\u5A7E\u5AAE\u6295\u6568\u65A2\u6B95\u7D0F\u7DF0\u8623\u900F\u936E\u982D\u9AB0\u9EC8",
"tu":"\u514E\u5154\u51C3\u51F8\u5410\u550B\u56F3\u56FE\u5716\u5717\u571F\u5721\u580D\u5817\u5857\u5C60\u5CF9\u5D5E\u5D80\u5EA9\u5EDC\u5F92\u6022\u6087\u6348\u6378\u63EC\u688C\u6C62\u6D82\u6D8B\u6E65\u6F73\u75DC\u760F\u79BF\u79C3\u7A0C\u7A81\u7B61\u816F\u837C\u83B5\u83DF\u8456\u84A4\u8DFF\u8FCC\u9014\u9174\u91F7\u922F\u92F5\u934E\u948D\u999F\u99FC\u9D4C\u9D5A\u9D75\u9D9F\u9DCB\u9DF5\u9F35",
"tuan":"\u526C\u5278\u56E2\u56E3\u5715\u5718\u587C\u5F56\u6171\u629F\u6476\u69EB\u6AB2\u6E4D\u6E6A\u6F19\u7153\u732F\u7583\u7BFF\u7CF0\u8916\u8C92\u93C4\u9DD2\u9DFB",
"tui":"\u4FC0\u50D3\u5A27\u5C35\u63A8\u717A\u7A68\u812E\u817F\u84F7\u85EC\u8608\u86FB\u8715\u892A\u8E46\u8E6A\u9000\u96A4\u9839\u983A\u983D\u9893\u99FE\u9ABD\u9B4B",
"tun":"\u541E\u5451\u554D\u5749\u5C6F\u5FF3\u65FD\u66BE\u671C\u6C3D\u6D92\u711E\u757D\u81C0\u81CB\u829A\u8C58\u8C5A\u8ED8\u9715\u98E9\u9968\u9B68\u9C80\u9ED7",
"tuo":"\u4E47\u4F57\u4F82\u4FBB\u5483\u553E\u5768\u5836\u59A5\u5AA0\u5AF7\u5CAE\u5EB9\u5F75\u6258\u6261\u62D3\u62D5\u62D6\u6329\u635D\u64B1\u6754\u67C1\u67DD\u692D\u6955\u69D6\u6A50\u6A62\u6BE4\u6BFB\u6C51\u6CB0\u6CB1\u6DB6\u72CF\u7823\u7824\u78A2\u7BA8\u7C5C\u7D3D\u812B\u8131\u838C\u841A\u8600\u8889\u88A5\u8A17\u8A51\u8BAC\u8DC5\u8DCE\u8E3B\u8FF1\u9161\u9640\u9641\u98E5\u9966\u99B1\u99B2\u99C4\u99DD\u99DE\u9A28\u9A52\u9A5D\u9A6E\u9A7C\u9B60\u9B80\u9C16\u9D15\u9D4E\u9E35\u9F09\u9F0D\u9F27",
"wa":"\u4F64\u52B8\u5493\u54C7\u5558\u55D7\u55E2\u5A03\u5A32\u5AA7\u5C72\u5F8D\u6316\u6432\u6528\u6D3C\u6E9B\u6F25\u74E6\u74F2\u7556\u7819\u7A8A\u7AAA\u8049\u817D\u8183\u86D9\u889C\u896A\u90B7\u97C8\u97E4\u9F03",
"wai":"\u558E\u5916\u5D34\u6B6A\u7AF5\u9861",
"wan":"\u4E07\u4E38\u4E5B\u5007\u5213\u525C\u534D\u5350\u550D\u57E6\u5846\u58EA\u59A7\u5A49\u5A60\u5B8C\u5B9B\u5C8F\u5E35\u5F2F\u5F4E\u5FE8\u60CB\u628F\u633D\u6356\u6365\u665A\u6669\u667C\u6764\u689A\u6900\u6C4D\u6DB4\u6E7E\u6F6B\u7063\u70F7\u73A9\u7413\u742C\u7579\u7696\u76CC\u7755\u7897\u7B02\u7D08\u7DA9\u7DB0\u7EA8\u7EFE\u7FEB\u8118\u8155\u8284\u839E\u83C0\u842C\u858D\u873F\u8C4C\u8CA6\u8D03\u8D0E\u8E20\u8F13\u909C\u92C4\u92D4\u933D\u9350\u93AB\u9811\u987D",
"wang":"\u4EA1\u4EBE\u4EFC\u5166\u5984\u5C23\u5C29\u5C2A\u5C2B\u5F7A\u5F80\u5F83\u5FD8\u5FF9\u60D8\u65FA\u6680\u671B\u6722\u6789\u68E2\u6C6A\u7007\u7139\u738B\u76F3\u7DB2\u7F51\u7F54\u83A3\u83F5\u869F\u86E7\u8744\u8AB7\u8F1E\u8F8B\u8FCB\u9B4D",
"wei":"\u4E3A\u4EB9\u4F1F\u4F2A\u4F4D\u5049\u504E\u507D\u50DE\u5130\u536B\u5371\u5383\u53DE\u5473\u552F\u5582\u55A1\u55B4\u56D7\u56F4\u570D\u5729\u589B\u58DD\u59D4\u5A01\u5A13\u5A81\u5A99\u5AA6\u5BEA\u5C09\u5C3E\u5CD7\u5CDE\u5D23\u5D54\u5D6C\u5DB6\u5DCD\u5E0F\u5E37\u5E43\u5EC6\u5FAB\u5FAE\u60DF\u6104\u6107\u6170\u61C0\u6364\u63CB\u63FB\u6596\u6637\u6690\u672A\u6845\u68B6\u6932\u6933\u6972\u6CA9\u6D08\u6D27\u6D58\u6DA0\u6E28\u6E2D\u6E4B\u6E88\u6EA6\u6F4D\u6F59\u6F7F\u6FCA\u6FF0\u6FFB\u7022\u709C\u70BA\u70D3\u7140\u7152\u715F\u7168\u71AD\u71F0\u7232\u729A\u72A9\u7325\u732C\u73AE\u741F\u744B\u748F\u754F\u75CF\u75FF\u7650\u7653\u784A\u7859\u78A8\u78D1\u7DAD\u7DED\u7DEF\u7E05\u7EAC\u7EF4\u7F7B\u80C3\u8172\u8249\u829B\u82C7\u82FF\u8371\u83CB\u840E\u8466\u8468\u8473\u848D\u84F6\u851A\u853F\u8587\u85B3\u85EF\u8636\u8732\u873C\u875B\u875F\u87B1\u885B\u885E\u893D\u89A3\u89B9\u8A74\u8AC9\u8B02\u8B86\u8B8F\u8BFF\u8C13\u8E13\u8E97\u8E9B\u8ECE\u8F4A\u8FDD\u9036\u9055\u912C\u9180\u9317\u934F\u9361\u93CF\u95C8\u95F1\u9687\u9688\u9697\u9728\u973A\u973B\u97CB\u97D1\u97D9\u97E1\u97E6\u97EA\u9820\u98B9\u9927\u9935\u9956\u9AA9\u9AAA\u9AAB\u9B4F\u9B87\u9BA0\u9BAA\u9C03\u9C04\u9C94\u9CC2\u9CDA",
"wen":"\u520E\u543B\u545A\u5461\u554F\u586D\u598F\u5F63\u5FDF\u6286\u63FE\u6435\u6587\u687D\u6985\u69B2\u6B9F\u6C76\u6E02\u6E29\u6EAB\u7086\u73F3\u7465\u74BA\u7612\u761F\u7807\u7A33\u7A4F\u7A69\u7D0A\u7D0B\u7D7B\u7EB9\u805E\u80B3\u8115\u8117\u82A0\u83AC\u8689\u868A\u87A1\u87C1\u8C71\u8F3C\u8F40\u8F92\u922B\u93BE\u95BA\u95BF\u95C5\u95E6\u95E7\u95EE\u95FB\u960C\u96EF\u9850\u9942\u99BC\u9B70\u9C1B\u9C2E\u9CC1\u9CFC\u9D0D\u9F24",
"weng":"\u52DC\u55E1\u5855\u5963\u5D61\u66A1\u6EC3\u74EE\u7515\u7788\u7F4B\u7FC1\u806C\u84CA\u8579\u8789\u9393\u9DB2\u9E5F\u9F46",
"wo":"\u4EF4\u502D\u5053\u5367\u5529\u5594\u5A50\u5A51\u5A89\u5E44\u6211\u631D\u6370\u637E\u63E1\u64BE\u65A1\u6943\u6C83\u6DA1\u6DB9\u6E25\u6E26\u6FE3\u7125\u7327\u74C1\u7783\u786A\u7A9D\u7AA9\u809F\u815B\u81D2\u81E5\u83B4\u8435\u8717\u8778\u8E12\u9F77\u9F8C",
"wu":"\u4E44\u4E4C\u4E94\u4EF5\u4F06\u4F0D\u4FAE\u4FC9\u5035\u511B\u5140\u526D\u52A1\u52D9\u52FF\u5348\u5433\u5434\u543E\u5449\u545C\u5514\u554E\u55DA\u572C\u575E\u5862\u5966\u59A9\u5A2A\u5A2C\u5A7A\u5AF5\u5BE4\u5C4B\u5C7C\u5C89\u5D4D\u5D68\u5DEB\u5E91\u5EE1\u5F19\u5FE2\u5FE4\u6003\u609E\u609F\u60AE\u61AE\u620A\u6264\u6342\u6440\u6544\u65E0\u65FF\u6664\u6747\u674C\u68A7\u6A46\u6B4D\u6B66\u6BCB\u6C59\u6C5A\u6C61\u6D16\u6D3F\u6D6F\u6EA9\u6F55\u70CF\u7110\u7111\u7121\u7183\u7193\u7269\u727E\u739D\u73F7\u73F8\u7466\u7491\u7512\u75E6\u77F9\u7894\u7966\u7991\u7A8F\u7AB9\u7BBC\u7C85\u821E\u829C\u82B4\u8323\u8381\u856A\u8601\u8708\u8790\u8A88\u8AA3\u8AA4\u8BEC\u8BEF\u8DB6\u8E8C\u8FD5\u901C\u90AC\u90DA\u9114\u91EB\u92C8\u933B\u93A2\u94A8\u9622\u9696\u96FE\u971A\u9727\u9770\u9A16\u9A9B\u9BC3\u9C1E\u9D2E\u9D50\u9D61\u9DA9\u9DE1\u9E40\u9E49\u9E5C\u9F2F\u9F3F\u9F40",
"xi":"\u4E60\u4FC2\u4FD9\u5092\u50D6\u516E\u51DE\u5338\u534C\u5365\u5380\u5438\u546C\u54A5\u550F\u553D\u559C\u563B\u564F\u56B1\u5848\u58D0\u5915\u595A\u5A2D\u5AB3\u5B06\u5B09\u5C43\u5C56\u5C57\u5C63\u5D60\u5D8D\u5DC7\u5E0C\u5E2D\u5F86\u5F99\u5F9A\u5FAF\u5FDA\u5FE5\u602C\u6038\u6044\u606F\u6089\u6095\u60C1\u60DC\u6140\u6198\u6199\u620F\u622F\u6231\u6232\u6278\u6614\u665E\u6670\u6673\u66BF\u66E6\u676B\u6790\u67B2\u6878\u691E\u693A\u69BD\u69E2\u6A28\u6A40\u6A72\u6A84\u6B2F\u6B37\u6B56\u6B59\u6B5A\u6C25\u6C50\u6D17\u6D60\u6DC5\u6E13\u6EAA\u6ECA\u6F07\u6F1D\u6F5D\u6F5F\u6F99\u70EF\u7101\u7108\u711F\u712C\u7155\u7182\u7184\u7188\u7199\u71B9\u71BA\u71BB\u71E8\u7214\u727A\u7280\u7294\u72A0\u72A7\u72F6\u73BA\u740B\u74BD\u761C\u7699\u76FB\u774E\u77A6\u77D6\u77FD\u7852\u78F6\u7902\u798A\u79A7\u7A00\u7A27\u7A78\u7AB8\u7C9E\u7CFB\u7D30\u7D8C\u7DC6\u7E30\u7E65\u7E9A\u7EC6\u7EE4\u7FB2\u7FD2\u7FD5\u7FD6\u80B8\u80B9\u819D\u8204\u823E\u8383\u83E5\u8448\u8478\u84A0\u84B5\u84C6\u84F0\u856E\u8582\u8669\u8725\u8777\u8785\u8787\u87CB\u87E2\u8835\u884B\u88AD\u8972\u897F\u8980\u89A1\u89A4\u89CB\u89F9\u89FD\u89FF\u8A92\u8AF0\u8B11\u8B35\u8B46\u8BF6\u8C3F\u8C40\u8C68\u8C6F\u8C95\u8D65\u8D69\u8D87\u8D98\u8E5D\u8EA7\u90C4\u90CB\u90D7\u90E4\u910E\u9145\u91AF\u91F3\u91F8\u9222\u9291\u932B\u93B4\u93ED\u9474\u94E3\u9521\u95DF\u960B\u9699\u969F\u96B0\u96B5\u96DF\u972B\u973C\u98C1\u990F\u9919\u993C\u9969\u997B\u9A31\u9A3D\u9A68\u9B29\u9BD1\u9C3C\u9C5A\u9CDB\u9D57\u9E02\u9ED6\u9F37",
"xia":"\u4E05\u4E0B\u4FA0\u4FE0\u5084\u5323\u53A6\u5413\u5477\u5687\u5737\u590F\u5913\u5CE1\u5CFD\u5EC8\u61D7\u6433\u656E\u6687\u67D9\u68BA\u6E8A\u70A0\u70DA\u7146\u72CE\u72ED\u72F9\u73E8\u7455\u759C\u75A8\u7771\u778E\u7856\u7864\u78AC\u78CD\u796B\u7B1A\u7B6A\u7E00\u7E16\u7F45\u7FC8\u821D\u823A\u8578\u867E\u8766\u8AD5\u8C3A\u8D6E\u8F44\u8F96\u9050\u935C\u938B\u93EC\u9595\u959C\u965C\u967F\u971E\u98AC\u9A22\u9B7B\u9C15\u9DB7\u9EE0",
"xian":"\u4ED9\u4EDA\u4F2D\u4F61\u50CA\u50E9\u50F2\u50F4\u5148\u51BC\u53BF\u549E\u54B8\u54EF\u550C\u5563\u5615\u57B7\u597E\u59B6\u59ED\u5A0A\u5A28\u5A34\u5A39\u5A71\u5ACC\u5AFA\u5AFB\u5B10\u5B45\u5BAA\u5C1F\u5C20\u5C73\u5C98\u5CF4\u5D04\u5DAE\u5E70\u5EEF\u5F26\u5FFA\u61AA\u61B2\u61B8\u6326\u6380\u641F\u648A\u648F\u6507\u6515\u663E\u665B\u66B9\u6774\u67AE\u6A4C\u6AF6\u6BE8\u6C19\u6D80\u6D8E\u6E7A\u6F96\u7017\u7066\u70CD\u71F9\u72DD\u7303\u732E\u736B\u736E\u737B\u7381\u73B0\u73D7\u73FE\u7509\u75EB\u7647\u764E\u770C\u774D\u784D\u7925\u7946\u7992\u79C8\u7B45\u7BB2\u7C7C\u7CAF\u7CEE\u7D43\u7D64\u7DAB\u7DDA\u7E23\u7E4A\u7E8E\u7E96\u7EA4\u7EBF\u7F10\u7FA1\u7FA8\u80D8\u817A\u81D4\u81FD\u8237\u82CB\u82EE\u83A7\u83B6\u859F\u85D3\u85D4\u85D6\u861A\u86AC\u86BF\u86DD\u8706\u8854\u8858\u893C\u8973\u8AA2\u8AB8\u8AF4\u8B63\u8C4F\u8CE2\u8D12\u8D24\u8D7B\u8DE3\u8DF9\u8E6E\u8E9A\u8F31\u9170\u918E\u929B\u929C\u92E7\u930E\u9341\u9342\u934C\u93FE\u9466\u94E6\u9528\u9591\u95F2\u9650\u9665\u9669\u9677\u967A\u96AA\u9730\u97C5\u97EF\u97F1\u9855\u986F\u9921\u9985\u99A6\u9BAE\u9C7B\u9C9C\u9DB1\u9DF3\u9DF4\u9DFC\u9E47\u9E79\u9EB2\u9F38",
"xiang":"\u4E61\u4EAB\u4EAF\u4F6D\u50CF\u52E8\u53A2\u5411\u54CD\u554C\u56AE\u59E0\u5D91\u5DF7\u5EA0\u5EC2\u5FC0\u60F3\u6651\u66CF\u6819\u697F\u6A61\u6B00\u6E58\u73E6\u74D6\u74E8\u76F8\u7965\u7BB1\u7D74\u7DD7\u7F03\u7F3F\u7FD4\u81B7\u8297\u842B\u8459\u858C\u8683\u87D3\u8801\u8944\u8950\u8A73\u8BE6\u8C61\u8DED\u90F7\u9109\u910A\u9115\u9284\u940C\u9472\u9576\u97FF\u9805\u9879\u98E8\u9909\u9957\u995F\u9977\u9999\u9A64\u9AA7\u9B9D\u9BD7\u9C4C\u9C5C\u9C76\u9C9E\u9E98",
"xiao":"\u4FBE\u4FF2\u509A\u524A\u52B9\u547A\u54B2\u54D3\u54EE\u554B\u5578\u560B\u5610\u5628\u562F\u5635\u56A3\u56BB\u56C2\u5A4B\u5B5D\u5BAF\u5BB5\u5C0F\u5D24\u5EA8\u5F47\u6054\u6077\u61A2\u63F1\u64A8\u6548\u6569\u6585\u6586\u6653\u6681\u66C9\u67AD\u67B5\u6821\u689F\u6AF9\u6B4A\u6B57\u6BCA\u6D28\u6D88\u6D8D\u6DC6\u6EE7\u6F47\u701F\u7071\u7072\u7107\u71BD\u7307\u7362\u75DA\u75DF\u769B\u76A2\u785D\u7863\u7A58\u7A99\u7B11\u7B71\u7B7F\u7BAB\u7BE0\u7C18\u7C2B\u7D83\u7EE1\u7FDB\u8096\u81AE\u8427\u8437\u856D\u85C3\u8648\u8653\u87C2\u87CF\u87F0\u8828\u8A24\u8A68\u8A9F\u8AB5\u8B0F\u8B1E\u8E03\u900D\u90E9\u92B7\u9500\u9704\u9A4D\u9A81\u9AC7\u9AD0\u9B48\u9D1E\u9D35\u9DCD\u9E2E",
"xie":"\u4E9B\u4EB5\u4F33\u5055\u5070\u50C1\u5199\u51A9\u52A6\u52F0\u534F\u5354\u5368\u5378\u55CB\u5667\u57A5\u586E\u5911\u594A\u5A0E\u5A9F\u5BEB\u5C51\u5C53\u5C5F\u5C67\u5C6D\u5CEB\u5DB0\u5EE8\u5FA2\u604A\u6136\u61C8\u62F9\u631F\u633E\u63F3\u643A\u64B7\u64D5\u64F7\u651C\u659C\u65EA\u66AC\u68B0\u6954\u698D\u69AD\u6B47\u6CC4\u6CFB\u6D29\u6E2B\u6FA5\u7009\u7023\u707A\u70A7\u70A8\u710E\u7181\u71EE\u71F2\u7215\u7332\u736C\u744E\u7944\u79BC\u7CCF\u7D32\u7D4F\u7D5C\u7D6C\u7D8A\u7DE4\u7DF3\u7E88\u7EC1\u7F2C\u7F37\u7FD3\u80C1\u8105\u8107\u810B\u818E\u85A2\u85A4\u85DB\u874E\u8762\u87F9\u880D\u880F\u887A\u8909\u893B\u896D\u8AE7\u8B1D\u8B97\u8C10\u8C22\u8E9E\u8EA0\u9082\u90AA\u9437\u978B\u97A2\u97B5\u97F0\u9F42\u9F58\u9F65\u9FA4",
"xin":"\u4F08\u4F29\u4FE1\u4FFD\u5677\u567A\u56DF\u59A1\u5B1C\u5B5E\u5EDE\u5FC3\u5FC4\u5FFB\u60DE\u65B0\u6615\u677A\u6794\u6B23\u6B46\u6F43\u7098\u712E\u76FA\u812A\u820B\u82AF\u8398\u85AA\u8845\u8A22\u8A2B\u8ED0\u8F9B\u90A4\u91C1\u920A\u92C5\u9414\u946B\u950C\u9620\u9856\u99A8\u99AB\u99B8\u9B35",
"xing":"\u4F80\u5016\u5174\u5211\u54D8\u578B\u57B6\u5842\u59D3\u5A19\u5A5E\u5B39\u5E78\u5F62\u6027\u60BB\u60FA\u64E4\u661F\u66D0\u674F\u6D10\u6DAC\u714B\u72CC\u7329\u7446\u76A8\u7772\u784E\u7BB5\u7BC2\u7DC8\u8165\u81D6\u8208\u8347\u8395\u86F5\u884C\u88C4\u89EA\u89F2\u8B03\u90A2\u90C9\u9192\u9203\u9276\u9292\u92DE\u9498\u94CF\u9649\u9658\u9A02\u9A8D\u9B8F\u9BF9",
"xiong":"\u5144\u5147\u51F6\u5308\u54C5\u5910\u5FF7\u605F\u657B\u6C79\u6D36\u718A\u80F7\u80F8\u828E\u8A29\u8A57\u8A7E\u8BBB\u8BC7\u96C4",
"xiu":"\u4F11\u4FE2\u4FEE\u54BB\u5CAB\u5EA5\u673D\u6A07\u6EB4\u6EEB\u70CB\u70CC\u73DB\u7407\u7493\u79C0\u7CD4\u7D87\u7D89\u7E4D\u7E61\u7EE3\u7F9E\u8119\u8129\u81F9\u82EC\u8791\u8896\u88E6\u890E\u890F\u8C85\u929D\u92B9\u9380\u93C5\u93E5\u93FD\u9508\u98CD\u9948\u9990\u9AE4\u9AF9\u9BB4\u9D42\u9E3A\u9F45",
"xu":"\u4F35\u4F90\u4FC6\u5066\u5194\u52D6\u52D7\u5379\u53D9\u5474\u55A3\u55C5\u5618\u5653\u57BF\u589F\u58FB\u59C1\u5A7F\u5AAD\u5B03\u5E41\u5E8F\u5F90\u6064\u6149\u620C\u63DF\u654D\u6558\u65ED\u65F4\u662B\u668A\u6702\u6829\u6948\u69D2\u6B28\u6B30\u6B3B\u6B54\u6B58\u6B88\u6C7F\u6C80\u6D2B\u6E51\u6E86\u6F35\u6F4A\u70C5\u70FC\u7166\u735D\u73DD\u73EC\u755C\u759E\u76E2\u76E8\u76F1\u7781\u77B2\u7809\u7A30\u7A38\u7AA2\u7CC8\u7D6E\u7D9A\u7DD2\u7DD6\u7E03\u7E8C\u7EEA\u7EED\u805F\u80E5\u84A3\u84C4\u84FF\u8566\u85C7\u85DA\u8657\u865A\u865B\u8751\u8A0F\u8A31\u8A39\u8A61\u8ADD\u8B43\u8BB8\u8BE9\u8C1E\u8CC9\u9126\u9157\u9191\u928A\u9450\u9700\u9808\u980A\u987B\u987C\u9A49\u9B1A\u9B46\u9B56\u9C6E",
"xuan":"\u5107\u5405\u54BA\u55A7\u5847\u5A97\u5AD9\u5B1B\u5BA3\u6030\u60AC\u6103\u610B\u61F8\u63CE\u65CB\u660D\u6621\u6645\u6684\u6685\u66B6\u688B\u6965\u6966\u6A88\u6CEB\u6E32\u6F29\u70AB\u70DC\u714A\u7384\u73B9\u7401\u7404\u7444\u7487\u74BF\u75C3\u7663\u766C\u7729\u7734\u777B\u77CE\u78B9\u79A4\u7BAE\u7D62\u7E3C\u7E4F\u7EDA\u7FE7\u7FFE\u8431\u8432\u84D2\u8519\u857F\u85FC\u8610\u8701\u8756\u8809\u8852\u88A8\u8AE0\u8AFC\u8B5E\u8B82\u8C16\u8D19\u8ED2\u8F69\u9009\u9078\u9249\u9379\u93C7\u94C9\u955F\u9799\u98B4\u99FD\u9C1A",
"xue":"\u4E74\u5437\u5779\u5B66\u5B78\u5CA4\u5CC3\u5DA8\u6034\u6588\u6856\u6A30\u6CE7\u6CF6\u6FA9\u7025\u70D5\u71E2\u72D8\u75A6\u75B6\u7A74\u81A4\u825D\u8313\u8486\u859B\u8840\u8895\u89F7\u8B14\u8C11\u8D90\u8E05\u8F4C\u8FA5\u96E4\u96EA\u9774\u97BE\u9C48\u9CD5\u9DFD\u9E34",
"xun":"\u4F28\u4F9A\u5071\u52CB\u52DB\u52F2\u52F3\u5342\u5640\u565A\u5691\u5743\u57D9\u5864\u58CE\u58E6\u595E\u5BFB\u5C0B\u5CCB\u5DE1\u5DFD\u5EF5\u5F87\u5FAA\u6042\u613B\u63D7\u6533\u65EC\u66DB\u674A\u6812\u686A\u6A33\u6B89\u6BBE\u6BE5\u6C5B\u6D35\u6D54\u6F6F\u7065\u7104\u718F\u71C2\u71C5\u71D6\u71FB\u720B\u72E5\u736F\u73E3\u7495\u77C4\u7AA8\u7D03\u7E81\u81D0\u8340\u8512\u8548\u85AB\u85B0\u860D\u87F3\u8951\u8A0A\u8A13\u8A19\u8A62\u8BAD\u8BAF\u8BE2\u8CD0\u8FC5\u8FFF\u900A\u905C\u9129\u91BA\u9442\u9868\u99B4\u99E8\u9A6F\u9C4F\u9C58\u9C9F",
"ya":"\u4E2B\u4E9A\u4E9C\u4E9E\u4F22\u4FF9\u529C\u538A\u538B\u5393\u5440\u54D1\u5516\u555E\u5714\u5720\u57AD\u57E1\u5810\u58D3\u5A05\u5A6D\u5B72\u5C88\u5D15\u5D16\u5E8C\u5E98\u62BC\u631C\u6397\u63E0\u6792\u6860\u690F\u6C29\u6C2C\u6DAF\u6F04\u7259\u72BD\u731A\u7330\u73A1\u740A\u7458\u758B\u75D6\u7602\u775A\u7811\u7A0F\u7A75\u7AAB\u7B0C\u8050\u82BD\u8565\u869C\u8859\u897E\u8A1D\u8BB6\u8FD3\u930F\u941A\u94D4\u96C5\u9D09\u9D28\u9D76\u9E26\u9E2D\u9F56\u9F7E",
"yan":"\u4E25\u4E75\u4FE8\u5043\u5050\u5063\u50BF\u513C\u5156\u5157\u5186\u5266\u533D\u538C\u53A3\u53AD\u53B3\u53B4\u54BD\u5501\u55AD\u565E\u56A5\u56B4\u5830\u5869\u5895\u58DB\u58E7\u5935\u5944\u598D\u599F\u59F2\u59F8\u5A2B\u5A2E\u5AE3\u5B0A\u5B2E\u5B3F\u5B4D\u5BB4\u5CA9\u5D26\u5D43\u5D52\u5D53\u5D96\u5DCC\u5DD6\u5DD7\u5DD8\u5DDA\u5EF6\u5F07\u5F65\u5F66\u6079\u611D\u61D5\u622D\u624A\u6281\u639E\u63A9\u63C5\u63DC\u6565\u6616\u664F\u66A5\u66D5\u66EE\u68EA\u693B\u693C\u694C\u6A2E\u6A90\u6ABF\u6AE9\u6B15\u6C87\u6CBF\u6DF9\u6E30\u6E37\u6E6E\u6EDF\u6F14\u6F39\u704E\u7054\u7067\u7069\u708E\u70DF\u7109\u7114\u7130\u7131\u7159\u7196\u71C4\u71D5\u7213\u726A\u72FF\u7312\u73DA\u7402\u7430\u7517\u76D0\u773C\u7814\u781A\u784F\u786F\u787D\u789E\u7939\u7B75\u7BF6\u7C37\u7D96\u7E2F\u7F68\u80ED\u814C\u81D9\u8273\u8276\u8277\u82AB\u839A\u83F8\u8412\u8455\u8505\u852B\u8664\u8712\u8758\u884D\u88FA\u8917\u898E\u89C3\u89FE\u8A00\u8A01\u8A2E\u8A7D\u8AFA\u8B8C\u8B9E\u8BA0\u8C1A\u8C33\u8C53\u8C54\u8D0B\u8D17\u8D18\u8D5D\u8EBD\u8EC5\u9043\u90D4\u90FE\u9122\u9140\u9153\u917D\u9183\u91B6\u91BC\u91C5\u9586\u95B9\u95BB\u95EB\u9609\u960E\u9681\u9692\u96C1\u984F\u9854\u9869\u989C\u990D\u995C\u9A10\u9A13\u9A34\u9A57\u9A60\u9A8C\u9B33\u9B47\u9B58\u9C0B\u9CEB\u9D08\u9D33\u9DA0\u9DC3\u9DF0\u9E7D\u9E99\u9EA3\u9EE1\u9EE4\u9EEB\u9EEC\u9EED\u9EF6\u9F34\u9F39\u9F5E\u9F74\u9F91",
"yang":"\u4EF0\u4F52\u4F6F\u509F\u517B\u52B7\u536C\u5489\u5771\u579F\u592E\u594D\u59CE\u5C9F\u5D35\u5D38\u5F89\u600F\u6059\u6143\u61E9\u626C\u62B0\u63DA\u6501\u656D\u65F8\u661C\u6698\u6768\u67CD\u6837\u694A\u6967\u69D8\u6A23\u6B83\u6C1C\u6C27\u6C31\u6CF1\u6D0B\u6F3E\u7001\u7080\u70B4\u70CA\u716C\u73DC\u75A1\u75D2\u760D\u7662\u770F\u773B\u7922\u7993\u79E7\u7D3B\u7F8A\u7F8F\u7F95\u7FAA\u80E6\u86D8\u8A47\u8AF9\u8EEE\u8F30\u9260\u9348\u935A\u940A\u9496\u9626\u9633\u967D\u96F5\u9737\u9785\u98BA\u98CF\u990A\u99DA\u9C11\u9D26\u9D39\u9E09\u9E2F",
"yao":"\u4EF8\u5004\u5060\u509C\u5406\u54AC\u5593\u55C2\u579A\u582F\u592D\u5996\u59DA\u5A79\u5AB1\u5B8E\u5C27\u5C2D\u5C86\u5CE3\u5D3E\u5DA2\u5DA4\u5E7A\u5FAD\u5FBA\u612E\u62AD\u63FA\u6416\u6447\u647F\u669A\u66DC\u66E3\u6773\u6796\u67FC\u6946\u699A\u69A3\u6B80\u6BBD\u6E94\u70D1\u718E\u71FF\u723B\u72D5\u733A\u735F\u73E7\u7464\u7476\u7711\u77C5\u78D8\u7945\u7A7E\u7A85\u7A88\u7A91\u7A94\u7AAF\u7AB0\u7B44\u7E47\u7E85\u8000\u80B4\u8170\u8200\u825E\u82ED\u836F\u846F\u847D\u84D4\u85AC\u85E5\u8628\u888E\u8981\u899E\u8A1E\u8A4F\u8B20\u8B21\u8B91\u8C23\u8EFA\u8F7A\u9059\u9065\u9080\u929A\u9390\u9470\u95C4\u977F\u9864\u98BB\u98D6\u9906\u991A\u9A15\u9C29\u9CD0\u9D01\u9D22\u9DC2\u9DD5\u9E5E\u9F3C\u9F69",
"ye":"\u4E1A\u4E5F\u4EAA\u4EB1\u503B\u505E\u50F7\u51B6\u53F6\u5414\u5622\u564E\u5688\u57DC\u5828\u58B7\u58C4\u591C\u5DAA\u5DAB\u62B4\u6353\u6359\u6396\u63F6\u64D6\u64DB\u64E8\u64EA\u64EB\u6654\u668D\u66C4\u66C5\u66D7\u66F3\u67BC\u67BD\u6930\u696A\u696D\u6B4B\u6B97\u6D02\u6DB2\u6F1C\u6F71\u6FB2\u70E8\u71C1\u7217\u7237\u723A\u76A3\u77B1\u77B8\u790F\u8036\u814B\u8449\u882E\u8B01\u8C12\u90BA\u9113\u9134\u91CE\u91FE\u92E3\u9371\u9381\u9391\u94D8\u9765\u9768\u9801\u9875\u9923\u9941\u998C\u9A5C\u9D7A\u9E08",
"yi":"\u4E00\u4E41\u4E42\u4E49\u4E59\u4E84\u4EA6\u4EBF\u4EE1\u4EE5\u4EEA\u4F07\u4F0A\u4F3F\u4F41\u4F5A\u4F7E\u4F87\u4F9D\u4FCB\u501A\u506F\u5100\u5104\u517F\u519D\u5208\u5293\u52AE\u52DA\u52E9\u5307\u531C\u533B\u541A\u5453\u546D\u5479\u54A6\u54BF\u5508\u566B\u56C8\u571B\u572F\u5744\u57BC\u57F6\u57F8\u58BF\u58F1\u58F9\u5901\u5937\u5955\u59B7\u59E8\u5A90\u5AD5\u5ADB\u5B04\u5B11\u5B1F\u5B90\u5B9C\u5BA7\u5BF1\u5BF2\u5C79\u5CC4\u5CD3\u5D3A\u5DA7\u5DAC\u5DB7\u5DF2\u5DF8\u5E1F\u5E20\u5E46\u5EA1\u5ED9\u5F02\u5F08\u5F0B\u5F0C\u5F2C\u5F5B\u5F5C\u5F5D\u5F5E\u5F79\u5FC6\u5FD4\u6008\u6021\u603F\u605E\u6092\u6098\u60A5\u610F\u61B6\u61CC\u61FF\u6245\u6246\u6291\u6339\u63D6\u648E\u653A\u6561\u657C\u6581\u65D1\u65D6\u6613\u6679\u6686\u66C0\u66CE\u66F5\u6759\u675D\u678D\u67BB\u67C2\u6818\u6827\u683A\u684B\u68ED\u6905\u692C\u6938\u698F\u69F8\u6A8D\u6AA5\u6AB9\u6B2D\u6B39\u6B5D\u6B94\u6BAA\u6BB9\u6BC5\u6BC9\u6C82\u6CB6\u6CC6\u6D22\u6D42\u6D65\u6D73\u6E59\u6EA2\u6F2A\u6F69\u6FBA\u7037\u7088\u7132\u71A0\u71A4\u71AA\u71BC\u71DA\u71E1\u71F1\u72CB\u7317\u7348\u73B4\u747F\u74F5\u7569\u7570\u7591\u75AB\u75CD\u75EC\u7617\u761E\u7631\u7654\u76CA\u7719\u7796\u77E3\u7912\u794E\u7995\u79C7\u79FB\u7A26\u7A53\u7AE9\u7B16\u7C03\u7C4E\u7E0A\u7E44\u7E76\u7E79\u7ECE\u7F22\u7F9B\u7FA0\u7FA9\u7FBF\u7FCA\u7FCC\u7FF3\u7FFC\u8034\u8084\u808A\u80F0\u8189\u81C6\u8223\u8257\u8264\u827A\u8285\u82C5\u82E1\u82E2\u8351\u8413\u841F\u84FA\u858F\u85D9\u85DD\u8619\u8649\u8681\u86DC\u86E1\u86E6\u8734\u8794\u8798\u87A0\u87FB\u8863\u8864\u886A\u8875\u8898\u88A3\u88D4\u88DB\u8939\u897C\u89FA\u8A32\u8A33\u8A4D\u8A52\u8A63\u8A83\u8ABC\u8B3B\u8B69\u8B6F\u8B70\u8B89\u8B9B\u8BAE\u8BD1\u8BD2\u8BE3\u8C0A\u8C59\u8C5B\u8C77\u8C96\u8CA4\u8CBD\u8D00\u8D3B\u8DC7\u8DE0\u8EFC\u8F22\u8F59\u8F76\u8FB7\u8FC6\u8FE4\u8FFB\u9018\u9038\u9057\u907A\u9091\u90FC\u914F\u91AB\u91B3\u91B7\u91D4\u91F4\u9218\u9220\u926F\u92A5\u93B0\u93D4\u943F\u9487\u94F1\u9552\u9571\u9623\u966D\u96BF\u972C\u977E\u9809\u9824\u9825\u984A\u9857\u9890\u98F4\u9950\u9974\u99C5\u9A5B\u9A7F\u9AAE\u9BA7\u9BA8\u9BE3\u9CE6\u9D82\u9D83\u9D8D\u9DC1\u9DCA\u9DD6\u9DE7\u9DFE\u9E03\u9E5D\u9E62\u9E65\u9ED3\u9EDF\u9EF3\u9F6E\u9F78",
"yin":"\u4E51\u4E5A\u4F8C\u5198\u51D0\u5370\u541F\u5432\u552B\u5591\u5656\u567E\u569A\u56D9\u56E0\u5701\u5794\u57A0\u57BD\u5819\u5924\u59FB\u5A63\u5A6C\u5BC5\u5C39\u5CFE\u5D1F\u5D2F\u5DBE\u5ED5\u5EF4\u5F15\u6114\u6147\u616D\u6196\u6197\u61DA\u65A6\u6704\u6836\u6A83\u6AAD\u6ABC\u6AFD\u6B45\u6BA5\u6BB7\u6C24\u6CFF\u6D07\u6D15\u6DEB\u6DFE\u6E5A\u6EB5\u6EDB\u6FE5\u6FE6\u70CE\u72BE\u72FA\u730C\u73E2\u748C\u7616\u763E\u764A\u766E\u7892\u78E4\u798B\u79F5\u7B43\u7C8C\u7D6A\u7DF8\u80E4\u82C2\u831A\u8335\u836B\u8376\u8491\u8529\u852D\u861F\u8693\u87BE\u87EB\u88C0\u8A14\u8A1A\u8A21\u8ABE\u8AF2\u8B94\u8D9B\u911E\u9173\u91FF\u920F\u921D\u9280\u92A6\u94DF\u94F6\u95C9\u9625\u9634\u9670\u967B\u9682\u9690\u96A0\u96B1\u9712\u9720\u972A\u9777\u9787\u97F3\u97FE\u98EE\u98F2\u996E\u99F0\u9A83\u9BA3\u9DE3\u9F57\u9F66\u9F82\u9F88",
"ying":"\u5040\u50CC\u5568\u55B6\u5624\u565F\u56B6\u584B\u5A74\u5A96\u5AB5\u5AC8\u5B30\u5B34\u5B46\u5B7E\u5DC6\u5DCA\u5E94\u5EEE\u5F71\u5FDC\u6125\u61C9\u646C\u6484\u650D\u6516\u651A\u6620\u668E\u6720\u685C\u68AC\u6979\u6A31\u6AFB\u6AFF\u6D67\u6E36\u6E81\u6E8B\u6ECE\u6EE2\u6F41\u6F46\u6FD9\u6FDA\u6FF4\u7005\u701B\u7020\u702F\u7034\u7050\u705C\u7150\u7192\u71DF\u73F1\u745B\u7469\u748E\u74D4\u7507\u7516\u763F\u766D\u76C1\u76C8\u77E8\u786C\u78A4\u792F\u7A4E\u7C5D\u7C6F\u7DD3\u7E08\u7E93\u7EEC\u7F28\u7F42\u7F43\u7F4C\u81A1\u81BA\u82F1\u8314\u8365\u8367\u83B9\u83BA\u8424\u8425\u8426\u843E\u84E5\u85C0\u8621\u86CD\u8747\u8767\u877F\u87A2\u8805\u8833\u892E\u89AE\u8B0D\u8B4D\u8B7B\u8CCF\u8D0F\u8D62\u8EC8\u8FCE\u90E2\u93A3\u941B\u944D\u9533\u9719\u9795\u97FA\u9834\u988D\u9895\u9896\u9D2C\u9DA7\u9DAF\u9DEA\u9DF9\u9E0E\u9E1A\u9E66\u9E70",
"yo":"\u54DF\u5537\u55B2",
"yong":"\u4F63\u4FD1\u509B\u50AD\u52C7\u52C8\u548F\u5581\u55C8\u5670\u57C7\u584E\u5889\u58C5\u5ADE\u5D71\u5EB8\u5EF1\u5F6E\u603A\u607F\u6080\u60E5\u6111\u6139\u6142\u6175\u62E5\u64C1\u67E1\u6810\u69E6\u6C38\u6CF3\u6D8C\u6E67\u6EFD\u6FAD\u7049\u7245\u7528\u752C\u75C8\u7655\u7670\u783D\u7867\u799C\u81C3\u82DA\u86F9\u8A60\u8E0A\u8E34\u9095\u90FA\u9118\u919F\u92BF\u93DE\u955B\u96CD\u96DD\u9852\u9899\u9954\u9BD2\u9C2B\u9C45\u9CAC\u9CD9\u9DDB",
"you":"\u4E23\u4EB4\u4F18\u4F51\u4F91\u5064\u512A\u5363\u53C8\u53CB\u53F3\u5466\u54CA\u5500\u5698\u56FF\u59F7\u5B67\u5BA5\u5C22\u5C24\u5CDF\u5CF3\u5E7C\u5E7D\u5EAE\u5FE7\u601E\u6023\u602E\u60A0\u6182\u61EE\u6538\u65BF\u6709\u67DA\u6884\u688E\u6962\u69F1\u6ACC\u6AFE\u6C8B\u6CB9\u6CD1\u6D5F\u6E38\u6E75\u6EFA\u7000\u7256\u7257\u7270\u72B9\u72D6\u7336\u7337\u7531\u75A3\u7950\u7989\u79DE\u7CFF\u7E8B\u7F90\u7F91\u8030\u8048\u80AC\u811C\u82C3\u839C\u83A0\u83A4\u83B8\u848F\u8555\u86B0\u86B4\u870F\u8763\u8A27\u8A98\u8BF1\u8C81\u8F0F\u8F36\u8FF6\u900C\u9030\u904A\u908E\u90AE\u90F5\u913E\u9149\u916D\u91C9\u923E\u92AA\u94C0\u94D5\u99C0\u9B77\u9B8B\u9C7F\u9C89\u9E80\u9EDD\u9F2C",
"yu":"\u4E0E\u4E88\u4E8E\u4E90\u4F03\u4F1B\u4F59\u4FC1\u4FDE\u4FE3\u4FFC\u504A\u50B4\u5125\u516A\u532C\u5401\u5539\u5585\u5590\u55A9\u55BB\u564A\u5673\u5704\u5709\u572B\u57DF\u5809\u5823\u582C\u59A4\u59AA\u5A1B\u5A2F\u5A31\u5A80\u5AD7\u5B29\u5B87\u5BD3\u5BD9\u5C7F\u5CEA\u5CFF\u5D33\u5D4E\u5D5B\u5D8E\u5DBC\u5EBD\u5EBE\u5F67\u5FA1\u5FEC\u6086\u60D0\u6108\u6109\u611A\u617E\u61D9\u622B\u625C\u6275\u6327\u63C4\u6554\u6594\u659E\u65BC\u65DF\u6631\u6745\u682F\u68DB\u68DC\u68EB\u6940\u6961\u6970\u6986\u6AF2\u6B0E\u6B1D\u6B24\u6B32\u6B48\u6B5F\u6B76\u6BD3\u6D74\u6DE2\u6DE4\u6DEF\u6E14\u6E1D\u6E61\u6EEA\u6F01\u6F9E\u6FA6\u706A\u7134\u715C\u71CF\u71E0\u7229\u724F\u72F1\u72F3\u7344\u7389\u7397\u7399\u7419\u7440\u745C\u74B5\u756C\u756D\u7600\u7609\u7610\u7652\u76C2\u76D3\u776E\u77DE\u7821\u7862\u7907\u7916\u791C\u7964\u79A6\u79B9\u79BA\u79D7\u7A22\u7A36\u7A65\u7A7B\u7AAC\u7AB3\u7AFD\u7B8A\u7BFD\u7C45\u7C5E\u7C72\u7D06\u7DCE\u7E58\u7EA1\u7F6D\u7FAD\u7FBD\u807F\u80B2\u8174\u81FE\u8201\u8206\u8207\u8245\u828B\u828C\u831F\u8330\u8362\u842D\u842E\u8438\u84AE\u84E3\u84F9\u854D\u8577\u8581\u860C\u861B\u865E\u8676\u871F\u872E\u8753\u87B8\u8867\u88AC\u88D5\u8915\u89A6\u89CE\u8A89\u8A9E\u8ADB\u8AED\u8B23\u8B7D\u8BED\u8C00\u8C15\u8C6B\u8C90\u8E30\u8EC9\u8F0D\u8F3F\u8F5D\u8FC2\u8FC3\u9033\u903E\u9047\u9079\u9098\u90C1\u9105\u9151\u91A7\u91EA\u923A\u9289\u92CA\u92D9\u9325\u935D\u942D\u94B0\u94FB\u95BE\u9608\u9653\u9683\u9685\u96A9\u96D3\u96E8\u96E9\u9731\u9810\u9884\u98EB\u9918\u9947\u996B\u9980\u99AD\u9A1F\u9A48\u9A6D\u9AAC\u9AC3\u9B30\u9B31\u9B3B\u9B4A\u9B5A\u9B63\u9BBD\u9BF2\u9C05\u9C4A\u9C7C\u9CFF\u9D25\u9D27\u9D2A\u9D52\u9DE0\u9DF8\u9E06\u9E12\u9E46\u9E6C\u9E8C\u9F6C\u9F89",
"yuan":"\u5086\u5143\u51A4\u5248\u539F\u53A1\u53B5\u5458\u54E1\u566E\u56E6\u56ED\u5706\u570E\u5712\u5713\u571C\u57A3\u57B8\u586C\u5917\u59B4\u5A9B\u5AB4\u5AC4\u5B3D\u5BC3\u5F32\u6028\u6081\u60CC\u613F\u63BE\u63F4\u676C\u68E9\u699E\u69AC\u6A7C\u6ADE\u6C85\u6DF5\u6E01\u6E06\u6E0A\u6E15\u6E72\u6E90\u6E92\u7041\u7230\u7328\u733F\u7342\u7457\u76F6\u7722\u7990\u7B0E\u7BA2\u7DE3\u7E01\u7F18\u7FB1\u8099\u82D1\u847E\u849D\u84AC\u8597\u8696\u870E\u8735\u875D\u876F\u8788\u884F\u8881\u88EB\u8911\u8924\u8B1C\u8C9F\u8D20\u8F45\u8F95\u8FDC\u903A\u9060\u908D\u90A7\u915B\u9228\u92FA\u93B1\u9662\u9858\u99CC\u9A35\u9B6D\u9CF6\u9D1B\u9D77\u9DA2\u9DB0\u9E22\u9E33\u9E53\u9EFF\u9F0B\u9F18\u9F1D",
"yue":"\u5216\u5B33\u5C84\u5CB3\u5DBD\u5F5F\u5F60\u6071\u6085\u60A6\u6209\u6288\u6373\u66F0\u66F1\u6708\u6782\u6A3E\u6C4B\u7039\u721A\u73A5\u77F1\u793F\u79B4\u7BB9\u7BD7\u7C46\u7C65\u7C70\u7CA4\u7CB5\u7D04\u7EA6\u8625\u868E\u868F\u8D8A\u8DC0\u8DC3\u8E8D\u8ECF\u9205\u925E\u94A5\u94BA\u95B1\u95B2\u9605\u9E11\u9E19\u9EE6\u9FA0\u9FA5",
"yun":"\u4E91\u4F1D\u508A\u5141\u52FB\u5300\u544D\u5597\u56E9\u593D\u596B\u5998\u5B55\u607D\u60F2\u6120\u612A\u614D\u628E\u62A3\u6600\u6655\u6688\u679F\u6A52\u6B92\u6B9E\u6C32\u6C33\u6C84\u6DA2\u6EB3\u6F90\u7174\u7185\u7189\u71A8\u72C1\u73A7\u7547\u7703\u78D2\u79D0\u7B7C\u7BD4\u7D1C\u7DFC\u7E15\u7E1C\u7E67\u7EAD\u7F0A\u8018\u803A\u816A\u82B8\u837A\u8480\u8495\u84B7\u8553\u8570\u8574\u8580\u85F4\u860A\u8779\u891E\u8CF1\u8D07\u8D5F\u8FD0\u904B\u90D3\u90E7\u9106\u9116\u915D\u9196\u919E\u9217\u92C6\u962D\u9668\u9695\u96F2\u9723\u97D7\u97DE\u97EB\u97F5\u97FB\u9835\u992B\u99A7\u99BB\u9F6B\u9F73",
"za":"\u531D\u5482\u56D0\u5E00\u62F6\u6742\u685A\u6C9E\u6CAF\u7838\u78FC\u7D25\u7D2E\u81DC\u81E2\u894D\u9254\u96D1\u96DC\u96E5\u97F4\u9B73",
"zai":"\u4ED4\u50A4\u510E\u518D\u54C9\u5728\u5BB0\u5D3D\u6257\u683D\u6D05\u6E3D\u6EA8\u707D\u707E\u70D6\u753E\u7775\u7E21\u83D1\u8CF3\u8F09\u8F7D\u9168",
"zan":"\u507A\u5127\u5139\u5142\u54B1\u5592\u56CB\u5BC1\u648D\u6512\u6522\u661D\u6682\u66AB\u6FFD\u7052\u74C9\u74D2\u74DA\u79B6\u7C2A\u7C2E\u7CCC\u8978\u8B83\u8B9A\u8CDB\u8D0A\u8D5E\u8DB1\u8DB2\u8E54\u913C\u9142\u9147\u933E\u93E8\u9415\u9961",
"zang":"\u585F\u5958\u5F09\u7242\u7F98\u810F\u81D3\u81DF\u81E7\u846C\u8CCD\u8CD8\u8D13\u8D1C\u8D43\u92BA\u99D4\u9A75\u9AD2",
"zao":"\u50AE\u51FF\u5515\u5523\u55BF\u566A\u6165\u65E9\u67A3\u6806\u688D\u68D7\u6FA1\u7076\u7170\u71E5\u7485\u74AA\u7681\u7682\u7AC3\u7AC8\u7C09\u7CDF\u8241\u85BB\u85FB\u86A4\u8B5F\u8DAE\u8E67\u8E81\u9020\u906D\u91A9\u947F",
"ze":"\u4EC4\u4F2C\u5219\u5247\u5536\u5567\u556B\u5616\u5928\u5AE7\u5D31\u5E3B\u5E58\u5E82\u629E\u62E9\u6351\u64C7\u6603\u6617\u6A0D\u6B75\u6C44\u6CA2\u6CCE\u6CFD\u6EAD\u6FA4\u769F\u7794\u77E0\u790B\u7A04\u7B2E\u7BA6\u7C00\u802B\u8234\u8536\u880C\u8957\u8ACE\u8B2E\u8CAC\u8CFE\u8D23\u8D5C\u8FEE\u9E05\u9F5A\u9F70",
"zei":"\u8808\u8CCA\u8D3C\u9C02\u9C61\u9C97",
"zen":"\u56CE\u600E\u8B56\u8B5B\u8C2E",
"zeng":"\u5897\u589E\u618E\u66FD\u66FE\u6A67\u71B7\u7494\u7511\u77F0\u78F3\u7E52\u7F2F\u7F7E\u8B44\u8D08\u8D60\u912B\u92E5\u9503\u9C5B",
"zha":"\u4E4D\u5067\u5284\u538F\u5412\u548B\u54A4\u54F3\u55B3\u5953\u5BB1\u624E\u62AF\u6313\u63F8\u6429\u643E\u6463\u672D\u67E4\u67F5\u6805\u6942\u69A8\u6A1D\u6E23\u6EA0\u7079\u70B8\u7160\u7250\u7534\u75C4\u76B6\u76BB\u76BC\u7728\u781F\u7B9A\u81AA\u82F2\u86B1\u86BB\u89F0\u8A50\u8B47\u8B57\u8BC8\u8E37\u8ECB\u8F67\u8FCA\u91A1\u9358\u94E1\u9598\u95F8\u9705\u9B93\u9BBA\u9C8A\u9C9D\u9F44\u9F47",
"zhai":"\u503A\u50B5\u5908\u5B85\u5BE8\u635A\u6458\u658B\u658E\u658F\u69B8\u7635\u7826\u7A84\u7C82\u7FDF\u9259\u9F4B",
"zhan":"\u4F54\u5061\u5360\u5661\u5AF8\u5C55\u5D2D\u5D83\u5D84\u5D98\u5DA6\u60C9\u6218\u6226\u6230\u62C3\u640C\u65A9\u65AC\u65C3\u65DC\u6808\u6834\u685F\u68E7\u6990\u6A4F\u6BE1\u6C08\u6C0A\u6CBE\u6E5B\u6FB6\u7416\u76BD\u76CF\u76DE\u77BB\u7AD9\u7C98\u7DBB\u7EFD\u83DA\u859D\u8638\u8665\u8666\u86C5\u89B1\u8A40\u8A79\u8B6B\u8B9D\u8C35\u8D88\u8E4D\u8F1A\u8F3E\u8F4F\u8F97\u9085\u9186\u959A\u9711\u98AD\u98D0\u98E6\u9958\u9A4F\u9A59\u9AA3\u9B59\u9C63\u9CE3\u9E07\u9E6F\u9EF5",
"zhang":"\u4E08\u4EC9\u4ED7\u50BD\u5887\u5ADC\u5D82\u5E10\u5E33\u5E5B\u5E65\u5F20\u5F21\u5F35\u5F70\u615E\u6259\u638C\u66B2\u6756\u6A1F\u6DA8\u6DB1\u6F32\u6F33\u7350\u748B\u75EE\u762C\u7634\u7795\u7903\u7AE0\u7C80\u7CBB\u80C0\u8139\u8501\u87D1\u8CEC\u8D26\u9067\u9123\u93F1\u9423\u969C\u979D\u9926\u9A3F\u9C46\u9E9E",
"zhao":"\u4F4B\u5146\u53EC\u5541\u5797\u59B1\u5DF6\u627E\u62DB\u65D0\u662D\u66CC\u679B\u68F9\u6AC2\u6CBC\u70A4\u7167\u71F3\u722B\u72E3\u7475\u76C4\u77BE\u7ABC\u7B0A\u7B8C\u7F40\u7F69\u7F84\u8081\u8087\u8088\u8A54\u8BCF\u8D75\u8D99\u91D7\u924A\u9363\u948A\u99CB\u9BA1",
"zhe":"\u5387\u54F2\u5560\u5586\u55FB\u569E\u57D1\u5AEC\u608A\u6298\u647A\u6662\u6663\u67D8\u68CF\u6A00\u6A1C\u6B7D\u6D59\u6DDB\u77FA\u7813\u78D4\u7C77\u7C8D\u8005\u8517\u8674\u86F0\u8707\u87C4\u87C5\u88A9\u8936\u8975\u8A5F\u8B2B\u8B3A\u8B81\u8B8B\u8C2A\u8D6D\u8F12\u8F19\u8F4D\u8F84\u8F99\u8FD9\u9019\u906E\u92B8\u937A\u9517\u9BBF\u9DD3\u9E67",
"zhen":"\u4FA6\u4FB2\u5075\u5733\u5866\u59EB\u5AC3\u5BCA\u5C52\u5E2A\u5F2B\u62AE\u630B\u632F\u63D5\u6438\u6552\u6576\u659F\u6623\u6715\u6795\u6815\u681A\u6862\u686D\u6968\u699B\u69C7\u6A3C\u6B9D\u6D48\u6E5E\u6F67\u6FB5\u7349\u73CD\u73CE\u7467\u7504\u755B\u75B9\u7715\u771E\u771F\u7739\u7827\u78AA\u796F\u798E\u799B\u7A39\u7BB4\u7C48\u7D16\u7D3E\u7D7C\u7E1D\u7E25\u7EBC\u7F1C\u8044\u80D7\u81FB\u8419\u8474\u8496\u84C1\u85BD\u8704\u8897\u88D6\u8999\u8A3A\u8AAB\u8BCA\u8C9E\u8CD1\u8D1E\u8D48\u8EEB\u8F43\u8F78\u8FB4\u9049\u9159\u91DD\u9241\u92F4\u9331\u9356\u937C\u93AD\u93AE\u9488\u9547\u9635\u9663\u9707\u9755\u99D7\u9B12\u9C75\u9D06\u9E29\u9EEE\u9EF0",
"zheng":"\u4E89\u4F42\u51E7\u57E9\u5863\u59C3\u5A9C\u5CE5\u5D1D\u5D22\u5E27\u5E40\u5F81\u5FB0\u5FB4\u5FB5\u6014\u6138\u628D\u62EF\u6323\u6399\u639F\u63C1\u649C\u653F\u6574\u6678\u6B63\u6C36\u70A1\u70DD\u722D\u72F0\u7319\u75C7\u7665\u7710\u7741\u775C\u7B5D\u7B8F\u7BDC\u7CFD\u8047\u84B8\u8A3C\u8ACD\u8B49\u8BC1\u8BE4\u8E2D\u90D1\u912D\u9266\u931A\u94B2\u94EE\u9B07\u9D0A",
"zhi":"\u4E4B\u4E7F\u4F84\u4FE7\u5001\u5024\u503C\u506B\u5082\u5128\u51EA\u5236\u5295\u52A7\u536E\u5394\u53EA\u5431\u54AB\u5740\u5741\u5767\u5781\u57F4\u57F7\u5886\u588C\u5902\u59EA\u5A21\u5B02\u5BD8\u5CD9\u5D3B\u5DF5\u5E0B\u5E19\u5E1C\u5E5F\u5EA2\u5EA4\u5ECC\u5F58\u5F8F\u5F94\u5F9D\u5FD7\u5FEE\u6049\u6179\u6184\u61E5\u61EB\u6220\u6267\u627A\u627B\u62A7\u6303\u6307\u631A\u63B7\u6418\u6431\u646D\u646F\u64F2\u64FF\u652F\u65D8\u65E8\u664A\u667A\u679D\u67B3\u67E3\u6800\u6809\u684E\u6894\u68BD\u690D\u6925\u69B0\u69DC\u6A34\u6ACD\u6ADB\u6B62\u6B96\u6C41\u6C65\u6C66\u6C9A\u6CBB\u6D14\u6D37\u6DFD\u6ECD\u6EDE\u6EEF\u6F10\u6F4C\u6F6A\u7004\u7099\u71AB\u72FE\u7318\u74C6\u74E1\u7564\u7590\u75B7\u75BB\u75D4\u75E3\u7608\u76F4\u77E5\u780B\u7929\u7949\u7951\u7957\u796C\u7983\u7994\u79B5\u79D3\u79D6\u79E9\u79EA\u79F2\u79F7\u7A19\u7A1A\u7A3A\u7A49\u7A92\u7B6B\u7D19\u7D29\u7D77\u7D95\u7DFB\u7E36\u7E54\u7EB8\u7EC7\u7F6E\u7FD0\u8040\u8041\u804C\u8077\u80A2\u80D1\u80DD\u8102\u81A3\u81B1\u81F3\u81F4\u81F8\u8296\u829D\u82B7\u830B\u85E2\u8635\u86ED\u8718\u87B2\u87D9\u8879\u887C\u889F\u88A0\u88FD\u8967\u899F\u89D7\u89EF\u89F6\u8A28\u8A8C\u8B22\u8C51\u8C52\u8C78\u8CAD\u8CEA\u8D04\u8D28\u8D3D\u8DBE\u8DD6\u8DF1\u8E2C\u8E2F\u8E60\u8E62\u8E91\u8E93\u8EC4\u8EF9\u8F0A\u8F75\u8F7E\u90C5\u916F\u91DE\u928D\u92D5\u9455\u94DA\u9527\u9624\u962F\u965F\u96B2\u96BB\u96C9\u99B6\u99BD\u99E4\u9A2D\u9A3A\u9A47\u9A98\u9BEF\u9CF7\u9D19\u9D32\u9DD9\u9E37\u9EF9\u9F05",
"zhong":"\u4E2D\u4EF2\u4F00\u4F17\u5045\u51A2\u5223\u55A0\u5839\u585A\u5990\u5995\u5A91\u5C30\u5E52\u5F78\u5FE0\u67CA\u6B71\u6C77\u6CC8\u7082\u7144\u72C6\u7607\u76C5\u773E\u794C\u794D\u79CD\u7A2E\u7B57\u7C66\u7D42\u7EC8\u80BF\u816B\u822F\u833D\u8520\u869B\u87A4\u87BD\u8846\u8873\u8876\u8877\u8AE5\u8E35\u8E71\u8FDA\u91CD\u9221\u937E\u9418\u949F\u953A\u9D24\u9F28",
"zhou":"\u4F37\u4F9C\u50FD\u5191\u5468\u546A\u5492\u54AE\u558C\u5663\u568B\u59AF\u5A64\u5B99\u5DDE\u5E1A\u5F9F\u663C\u665D\u666D\u6D00\u6D32\u6DCD\u70BF\u70D0\u73D8\u7503\u759B\u76B1\u76BA\u76E9\u776D\u77EA\u78A1\u7B92\u7C40\u7C52\u7C55\u7C99\u7CA5\u7D02\u7E10\u7EA3\u7EC9\u8098\u80C4\u821F\u836E\u83F7\u8464\u8A4B\u8B05\u8B78\u8BCC\u8BEA\u8CD9\u8D52\u8EF8\u8F08\u8F16\u8F74\u8F80\u9031\u90EE\u914E\u9282\u970C\u99CE\u99F2\u9A06\u9A5F\u9AA4\u9BDE\u9D43\u9E3C",
"zhu":"\u4E36\u4E3B\u4F2B\u4F47\u4F4F\u4F8F\u529A\u52A9\u52AF\u5631\u56D1\u577E\u58B8\u58F4\u5B4E\u5B94\u5C5E\u5C6C\u5D40\u62C4\u65B8\u66EF\u6731\u677C\u67F1\u67F7\u682A\u69E0\u6A26\u6A65\u6AE7\u6AEB\u6B18\u6BB6\u6CCF\u6CE8\u6D19\u6E1A\u6F74\u6FD0\u7026\u705F\u70A2\u70B7\u70DB\u7151\u716E\u71ED\u7225\u732A\u73E0\u75B0\u7603\u771D\u77A9\u77DA\u782B\u7843\u795D\u7969\u79FC\u7A8B\u7ADA\u7AF9\u7AFA\u7B01\u7B1C\u7B51\u7B6F\u7BB8\u7BC9\u7BEB\u7D35\u7D38\u7D51\u7EBB\u7F5C\u7F9C\u7FE5\u8233\u82A7\u82CE\u82E7\u8331\u833F\u8387\u8457\u84EB\u85F8\u86C0\u86DB\u876B\u880B\u8829\u883E\u88BE\u8A3B\u8A5D\u8A85\u8AF8\u8BDB\u8BF8\u8C6C\u8CAF\u8D2E\u8DD3\u8DE6\u8E85\u8EF4\u8FEC\u9010\u90BE\u9252\u9296\u92F3\u9444\u9483\u94E2\u94F8\u967C\u9714\u98F3\u99B5\u99D0\u99EF\u9A7B\u9BA2\u9BFA\u9C41\u9D38\u9E86\u9E88\u9F04",
"zhua":"\u6293\u6A9B\u722A\u7C3B\u81BC\u9AFD",
"zhuai":"\u62FD\u8DE9",
"zhuan":"\u4E13\u50CE\u53C0\u556D\u56C0\u581F\u5AE5\u5B68\u5C02\u5C08\u64B0\u7077\u7451\u747C\u750E\u7816\u78DA\u7AF1\u7BC6\u7C51\u7E33\u819E\u8483\u87E4\u8948\u8AEF\u8B54\u8CFA\u8D5A\u8EE2\u8F49\u8F6C\u911F\u9853\u989B\u994C\u9994\u9C44",
"zhuang":"\u4E2C\u58EE\u58EF\u58F5\u5986\u599D\u5A24\u5E62\u5E84\u5E92\u6205\u649E\u6869\u6889\u6A01\u6E77\u710B\u72B6\u72C0\u7CA7\u7CDA\u8358\u838A\u88C5\u88DD",
"zhui":"\u5760\u589C\u5A37\u60F4\u690E\u6C9D\u7500\u7577\u787E\u7908\u7B0D\u7DB4\u7E0B\u7F00\u7F12\u8187\u8AC8\u8D05\u8D58\u8F5B\u8FFD\u918A\u9310\u9323\u9446\u9525\u96B9\u991F\u9A05\u9A93\u9D7B",
"zhun":"\u51C6\u51D6\u57FB\u5B92\u6E96\u7A15\u7A80\u7DA7\u80AB\u8860\u8A30\u8AC4\u8C06\u8FCD",
"zhuo":"\u4E35\u502C\u5285\u5353\u53D5\u5544\u5545\u5734\u59B0\u5A3A\u5F74\u62D9\u6349\u64AF\u64C6\u64E2\u6580\u65AB\u65AE\u65B1\u65B2\u65B5\u666B\u684C\u68B2\u68C1\u68F3\u6913\u69D5\u6AE1\u6D4A\u6D5E\u6DBF\u6FC1\u6FEF\u7042\u707C\u70AA\u70F5\u72B3\u7422\u7438\u7740\u787A\u799A\u7A5B\u7A71\u7AA1\u7AA7\u7BE7\u7C57\u7C71\u7F6C\u8301\u8817\u883F\u8AC1\u8AD1\u8B36\u8BFC\u914C\u92DC\u942F\u9432\u956F\u9D6B\u9DDF",
"zi":"\u5033\u5179\u525A\u5407\u5470\u54A8\u5528\u5559\u55DE\u59C9\u59CA\u59D5\u59FF\u5B50\u5B56\u5B57\u5B5C\u5B73\u5B76\u5D30\u5D6B\u6063\u674D\u6825\u6893\u6914\u699F\u6A74\u6DC4\u6E0D\u6E7D\u6ECB\u6ED3\u6F2C\u6FAC\u7278\u7386\u7725\u7726\u77F7\u798C\u79C4\u79ED\u79F6\u7A35\u7B2B\u7C7D\u7CA2\u7D0E\u7D2B\u7DC7\u7F01\u8014\u80CF\u80D4\u80FE\u81EA\u8293\u830A\u8321\u8332\u8458\u8678\u89DC\u8A3E\u8A3F\u8AEE\u8C18\u8CB2\u8CC7\u8D40\u8D44\u8D7C\u8D91\u8DA6\u8F1C\u8F3A\u8F8E\u9111\u91E8\u922D\u9319\u937F\u93A1\u9531\u9543\u983E\u983F\u9AED\u9BD4\u9C26\u9CBB\u9D85\u9F12\u9F4D\u9F5C\u9F87",
"zong":"\u500A\u5027\u506C\u50AF\u582B\u5B97\u5D4F\u5D55\u5D78\u603B\u60E3\u60FE\u6121\u6374\u63D4\u6403\u6460\u662E\u6721\u68D5\u6936\u71A7\u71EA\u7314\u7323\u75AD\u7632\u7882\u78EB\u7A2F\u7CBD\u7CC9\u7D9C\u7DC3\u7DCF\u7DF5\u7E02\u7E26\u7E31\u7E3D\u7EB5\u7EFC\u7FEA\u8159\u8250\u847C\u84D7\u876C\u8C75\u8E28\u8E2A\u8E64\u931D\u936F\u93D3\u9441\u9A0C\u9A23\u9A94\u9B03\u9B09\u9B37\u9BEE\u9BFC",
"zou":"\u594F\u5AB0\u63AB\u63CD\u68F7\u68F8\u7B83\u7DC5\u83C6\u8ACF\u8BF9\u8D70\u8D71\u90B9\u90F0\u9112\u9139\u966C\u9A36\u9A7A\u9BD0\u9BEB\u9CB0\u9EC0\u9F7A",
"zu":"\u4FCE\u50B6\u5346\u5352\u54EB\u5D12\u5D2A\u65CF\u723C\u73C7\u7956\u79DF\u7A21\u7BA4\u7D44\u7EC4\u83F9\u8445\u84A9\u8A5B\u8B2F\u8BC5\u8DB3\u8E24\u8E3F\u93BA\u93C3\u955E\u963B\u977B",
"zuan":"\u5297\u63DD\u6525\u7C6B\u7E64\u7E82\u7E89\u7E98\u7F35\u8E9C\u8EA6\u945A\u947D\u94BB",
"zui":"\u539C\u55FA\u5634\u567F\u5D8A\u5DB5\u666C\u6700\u6718\u67A0\u682C\u6A36\u6A87\u6A8C\u6B08\u6FE2\u74BB\u797D\u7A5D\u7D4A\u7E97\u7F6A\u855E\u87D5\u8FA0\u9154\u917B\u9189\u92F7\u930A",
"zun":"\u50D4\u5642\u5C0A\u5D9F\u6358\u6499\u6A3D\u7E5C\u7F47\u8B50\u9075\u928C\u940F\u9C52\u9CDF\u9D8E\u9DF7",
"zuo":"\u4F50\u4F5C\u4FB3\u505A\u5497\u5511\u5750\u5C9D\u5C9E\u5DE6\u5EA7\u600D\u637D\u6628\u67DE\u690A\u795A\u79E8\u7A13\u7B70\u7CF3\u7E53\u80D9\u838B\u8443\u8444\u84D9\u888F\u923C\u963C\u98F5"
};



//判断数组
function isArray(obj) {
	return Object.prototype.toString.call(obj) === '[object Array]';
}
//个位数补零
function n20n(s){
	s=Number(s);
    return s < 10 ? '0'+s : ''+s;
}
//判断是否 不是undefined
function isDefined(e){
	if(e==undefined || e==''){
		return false;
	}else{
		return true;
	}
}
//阻塞
function sleep(numberMillis) {
	var now = dateNow();
	var exitTime = now.getTime() + numberMillis;
	while (true) {
		now = dateNow();
			if (now.getTime() > exitTime)
		return;
	}
}

function n2br(s){
	s=s.replace(new RegExp("\n","g"),"<br>");
	return s;
}
function dateNow(){
	var timeNow=(new Date()).getTime();
	if(window.widget && window.widget.serverDate){
		timeNow=window.widget.serverDate()*1000;
	}
	return new Date(timeNow);
}
function timeNow(){
	var timeNow=(new Date()).getTime();
	if(window.widget && window.widget.serverDate){
		timeNow=window.widget.serverDate()*1000;
	}
	return timeNow;
}
function date2time(myDate){
	if(typeof(myDate)=='object'){
		myDate=myDate.getTime();
	}else if(typeof(myDate)=='string'){
		myDate=parseInt(myDate);
	}
	return myDate;
}
function time2date(myTime){
	if(typeof(myTime)!='object'){
		if(typeof(myTime)!='Number'){
			myTime=Number(myTime);
		}
		myTime=new Date(myTime);
	}
	return myTime;
}
//dom轮询任务
function $domInterval(obj){
	if(obj.retime<0){
		return false;
	}
	//var $myDom=$(dom);
	obj.retime==undefined ? obj.retime=1000:'';
	var myInterval=setInterval(function(){
		var $dom=obj.$dom();
		if($dom.length>0){
			obj.callback($dom);
		}else{
			clearInterval(myInterval);
		}
	},obj.retime)
}
//dom定时触发任务
function $domTimeout(obj){
	function runCallback(){
		var $dom=obj.$dom();
		if($dom.length>0){
			obj.callback($dom);
		}
	}
	if(obj.retime<0){
		runCallback();
		return false;
	}
	//var $myDom=$(dom);
	obj.retime==undefined ? obj.retime=1000:'';
	var myTimeout=setTimeout(function(){
		runCallback();
	},obj.retime)
}
//$domInterval({
//	$dom:function(){
//		return $('.sandbox');
//	},
//	callback:function($dom){
//		$dom.html(new Date())
//	},
//	retime:1000
//})


//参数格式验证
var formCheck= {
	inputCheck:function ($input,isBlur){
		var checkTypeArr=$input.attr('data-inputcheck').split(',');
		var checkValue=$input.value();
		//清除两端空白
		if($input.attr('type')!='password' && isBlur){
			checkValue=$.trim(checkValue);
			$input.val(checkValue);
		}
		var checkResult = {};
		function inputCheckRule(ruleName){
			var myResult={
				'mode':false,
				'errorTips':''
			}
			switch (ruleName) {
				case 'length':
					var minLen=$input.attr('data-minlen');
					var maxLen=$input.attr('data-maxlen');
					if(!minLen){
						minLen=1;
					}
					if(!maxLen){
						maxLen=0;
					}
					if (checkValue.length< minLen) {
						if(minLen==1){
							myResult.errorTips = '该内容不能为空';
						}else{
							myResult.errorTips = '输入内容长度不能低于'+minLen;
						}
					}else if(maxLen >0 && checkValue.length > maxLen){
						myResult.errorTips = '输入内容长度不能大于'+maxLen;
					}
					break;
				case 'passwordleval':
					formCheck.passwordLevel($input)
					break;
				case 'cn':
					if (!checkValue.match(/^[\u4e00-\u9fa5]{1,10}$/)) {
						myResult.errorTips = '姓名必须为中文';
					}
					break;
				case 'phone':
					checkValue=Number(checkValue);
					if(isNaN(checkValue) || checkValue<10000000000 || checkValue>=20000000000 ){
						myResult.errorTips = '手机号码有误';
					}
					/*if (!checkValue.match(/^(1(([357][0-9])|(47)|[8][0123456789]))\d{8}$/)) {
						myResult.errorTips = '手机号码有误';
					}*/
					break;
				case 'email':
					if (!checkValue.match(/^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/)) {
						myResult.errorTips = '邮箱格式错误';
					}
					break;
				default:
					myResult.errorTips = 'noThisType';
					break;
			}
			if(myResult.errorTips=='noThisType' || myResult.errorTips==''){
				myResult.mode = true;
			}
			else{
				myResult.mode = false;
			}
			return myResult;
		}
		for(var i=0,len=checkTypeArr.length;i<len;i++){
			var checkTypeI=checkTypeArr[i];
			checkResult=inputCheckRule(checkTypeI);
			if(!checkResult.mode){
				break;
			}
		}
		//解析结果
		if(checkResult.mode){
			formCheck.checkResultShow($input,true);
		}
		else{
			if(isBlur){
				formCheck.checkResultShow($input,false,checkResult.errorTips);
			}
		}
		return checkResult.mode;
	},
	checkResultShow:function ($input,mode,tips){
		var $inputGroup=$input.closest('.input-group');
		$inputGroup.addClass('has-feedback');
		if($input.next('.glyphicon.form-control-feedback').length<1){
			$input.after('<span class="glyphicon form-control-feedback"></span>');
		}
		var $feedbackIcon=$input.next('.glyphicon.form-control-feedback');
		if(mode){
			$inputGroup.removeClass('has-error').addClass('has-success');
			$feedbackIcon.removeClass('glyphicon-remove').addClass('glyphicon-ok');
		}
		else{
			$inputGroup.removeClass('has-success').addClass('has-error');
			$feedbackIcon.removeClass('glyphicon-ok').addClass('glyphicon-remove');
			if(tips){
				formCheck.errorTips($input,tips);
			}
		}
	},
	errorTips:function ($input,errorInfo){
		var myClientRect=$input[0].getBoundingClientRect();
		var myMenu=new PopUpBox({
			'type':'menu',
			'style':'arrowDown black_alpha80',
			'show':true,
			'content':errorInfo,
			'position':{
				'left':myClientRect.left,
				'top':myClientRect.top
			}
		});
	},
	//密码强度
	passwordLevel:function ($input){
		var $passwordLevel=$input.closest('.input-group').find('.passwordLevel');
		function CharMode(pwdLetter){
		    if( pwdLetter >= 48 && pwdLetter <= 57 ){//数字
		        pl_0=1;
		    }else if( pwdLetter >= 65 && pwdLetter <= 90 ){//大写字母
		        pl_A=1;
		    }else if( pwdLetter >= 97 && pwdLetter <= 122 ){//小写字母
		        pl_a=1;
		    }else{//特殊字符
		        pl_$=1;
		    }
		}
		if($passwordLevel.length>0){
			var pl_0=0,pl_A=0,pl_a=0,pl_$=0,pl_l=0;
			var passwordValue=$input.val();
		    for( var i = 0,len=passwordValue.length; i<len ; i++ ){
		        CharMode(passwordValue.charCodeAt(i));
		    }
		    if( passwordValue.length > 4 ){
		    	pl_l=pl_0+pl_A+pl_a+pl_$;
		    }
		    var levelBarWidth='0%';
		    var levelBarColor='#ccc';
		    var levelBarClass='bar';
		    var levelBarTips='';
		    switch(pl_l){
		    	case 0:
		    		levelBarWidth='20%';
		    		levelBarColor='#ff0000';
		    		levelBarClass='-danger';
		    		levelBarTips='极差';
		    		break
		    	case 1:
		    		levelBarWidth='40%'
		    		levelBarColor='#ff9c00';
		    		levelBarClass='-warning';
		    		levelBarTips='低';
		    		break
		    	case 2:
		    		levelBarWidth='60%'
		    		levelBarColor='#deff00';
		    		levelBarClass='-info';
		    		levelBarTips='中';
		    		break
		    	case 3:
		    		levelBarWidth='80%'
		    		levelBarColor='#53ba86';
		    		levelBarClass='-primary';
		    		levelBarTips='高';
		    		break
		    	case 4:
		    		levelBarWidth='100%';
		    		levelBarColor='#1e48ff';
		    		levelBarClass='-success';
		    		levelBarTips='安全';
		    		break
		    }
		    $passwordLevel.children('.bar').stop(true,true).animate({'width':levelBarWidth})
		    .attr('class','bar btn'+levelBarClass);
		    //.css({'background-color':levelBarColor});
		    $passwordLevel.children('.text').attr('class','text text'+levelBarClass).html(levelBarTips);
		}
	},
	inputCompare:function ($input){
		if($input.attr('data-inputcompare')){
			if(!formCheck.inputCheck($input)){
				return false;
			}
		}
		var $targetInput=$('#'+$input.attr('data-inputcompare'));
		var tipsText='';
		if($targetInput.length>0){
			var val1=$targetInput.val();
			var val2=$input.val();
			if(val1!=val2){
				tipsText = '两次输入内容不一致';
			}
		}else{
			tipsText = '不存在可校验的对象';
		}
		if(tipsText==''){
			formCheck.checkResultShow($input,true);
			return true;
		}else{
			formCheck.checkResultShow($input,false,tipsText);
			return false;
		}
	},
	formGroupCheck:function($formGroup){
		var formCheckResult=true;
		$formGroup.find('[data-inputcheck]').each(function(){
			if(!formCheck.inputCheck($(this),true)){
				formCheckResult=false;
			};
		});
		$formGroup.find('[data-inputcompare]').each(function(){
			if(!formCheck.inputCompare($(this))){
				formCheckResult=false;
			};
		});
		return formCheckResult;
	},
	bind:function(){
		//输入内容校验
		$('body').on('keyup','[data-inputcheck]',function(){
			formCheck.inputCheck($(this));
		})
		$('body').on('keyup','[data-lockbtn]',function(){
			var $this=$(this);
			var checkResultMode=formCheck.inputCheck($this);
			var $btn=$('#'+$this.attr('data-lockbtn'));
			if(checkResultMode){
				$btn.removeAttr('disabled');
			}else{
				$btn.attr('disabled',true);
			}
		})
		$('body').on('blur','[data-inputcheck]',function(){
			formCheck.inputCheck($(this),true);
		})
		//重复校验
		$('body').on('blur','[data-inputcompare]',function(){
			formCheck.inputCompare($(this));
		})
		//表单组校验
		$('body').on('click','[data-formcheck]',function(){
			var $formGroup=$('#'+$(this).attr('data-formcheck'));
			if($formGroup.length){
				formCheck.formGroupCheck($formGroup);
			}
		})
	}


}
Date.prototype.Format = function (fmt) { //author: meizz
    var o = {
        "M+": this.getMonth() + 1, //月份
        "D+": n20n(this.getDate()), //日
        "h+": n20n(this.getHours()), //小时
        "m+": n20n(this.getMinutes()), //分
        "s+": n20n(this.getSeconds()), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(Y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
    if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
    /*
    var time1 = dateNow().Format("YYYY-MM-DD");
	var time2 = dateNow().Format("YYYY-MM-DD hh:mm:ss");
    */
}
function strCodeCheck(str){
	str=str.trim();
	var RexStr = /\<|\>|\"|\'|\&/g;
	    str = str.replace(RexStr, function(MatchStr) {
	        switch (MatchStr) {
	        case "<":
	            return "&lt;";
	            break;
	        case ">":
	            return "&gt;";
	            break;
	        case "\"":
	            return "&quot;";
	            break;
	        case "'":
	            return "&#39;";
	            break;
	        case "&":
	            return "&amp;";
	            break;
	        default:
	            break;
	        }
	    })
	str=n2br(str);
	return str;
}
//时间间隔转化为可读性文字标记
function time2dateMark(datemark,datenow){
	datemark=date2time(datemark);
	datenow=date2time(datenow);

	//换算为分
	var dateLong=Math.floor((datenow-datemark)/(1000*60));

	//求出天数
	var dateMark_dd=Math.floor(dateLong/(24*60))
	var dateMarkHtml='';
	if(dateMark_dd>0){
		if(dateMark_dd<2){
			dateMarkHtml+='昨天'
		}else if(dateMark_dd<3){
			dateMarkHtml+='前天'
		}else{
			dateMarkHtml+=dateMark_dd+'天前';
		}
	}else{
		//时间余数
		dateLong=dateLong-dateMark_dd*24*60
		//剩余小时
		var dateMark_hh=Math.floor(dateLong/60);
		if(dateMark_hh>0){
			dateMarkHtml+=dateMark_hh+'小时前';
		}else{
			//时间余数
			dateLong=dateLong-dateMark_hh*60;
			if(dateLong>0){
				dateMarkHtml+=dateLong+'分钟前';
			}else{
				dateMarkHtml+='刚刚';
			}
		}

	}
	return dateMarkHtml;
}

//参数格式验证
function checkRule(value, type) {
	var checkreturn = 'ok';
	switch (type) {
		case 'nospace':
			if (value == '') {
				checkreturn = '填写不能为空';
			}
			break;
		case 'namecn':
			if (!value.match(/^[\u4e00-\u9fa5]{1,10}$/)) {
				checkreturn = '姓名必须为中文';
			}
			break;
//		case 'idnum':
//			if (checkIdcard(value) != true) {
//				checkreturn = '身份证号码有误';
//			}
//			break;
		case 'phone':
			if (!value.match(/^(1\d{10}|23\d{9})$/)) {
				checkreturn = '手机号码有误';
			}
			break;
		case 'email':
			if (!value.match(/^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/)) {
				checkreturn = '邮箱格式错误';
			}
			break;
//		case 'pin':
//			if (myInput.attr('data-pincheck') != 1) {
//				checkreturn = '验证码有误';
//			}
//			break;
	}
	return checkreturn;
}

//为input添加清除文本的按钮
function inputClear(myInput,callback){
	var $myInput=$(myInput);
	$myInput.on('input',function(){
		if(this.value!=''){
			if($myInput.next('.inputClear').length<1){
				$myInput.after('<div class="inputClear"></div>');
				$myInput.next('.inputClear').css({
					'left':$myInput.position().left+$myInput.outerWidth()-20,
					'top':$myInput.position().top+($myInput.outerHeight()-20)/2,
				}).bind('click','.inputClear',function(){
					$(this).prev('input').val('');
					if(callback!=undefined){
						callback();
					}
					$(this).remove();
				})
			}
		}else{
			if($myInput.next('.inputClear').length>0){
				$myInput.next('.inputClear').remove();
			}
		}
	})

}
//返回中文指向的拼音
function pinyin(myText){
	myText=myText+'';
	var myLen = myText.length;
    var newText = '';
    function returnPinyin(myChar) {
        for (var k in pinyinCode) {
            if (pinyinCode[k].indexOf(myChar) != -1) {
                return k;
            }
        }
        return false;
    }
    //首字母大写
    function capitalize(e) {
        if (e.length > 0) {
            var a = e.substr(0, 1).toUpperCase();
            var b = e.substr(1, e.length);
            return a + b;
        }
    }
    for (var i = 0; i < myLen; i++) {
        var myChar = myText.substr(i, 1);
    	var isEn = new RegExp("[a-zA-Z0-9- ]");
        if (isEn.test(myChar)) {
            newText += myChar
        } else {
        	var myPinyin = returnPinyin(myChar);
            if (myPinyin !== false) {
                newText += myPinyin;
            }else{
            	// TODO: send this char to server
            	//console.log("NoPinyin:"+myChar);
            }
        }
    };

	return newText;
}

function _localeCompare(str1,str2){
	function enStr1IsFront(enStr1,enStr2){
		if(enStr1==enStr2){
			return false;
		}
		var str1LowerCase=enStr1.toLowerCase();
		var str2LowerCase=enStr2.toLowerCase();
		if(str1LowerCase!=str2LowerCase){
			if(str1LowerCase<str2LowerCase){
				return true;
			}else{
				return false;
			}
		}else{
			if(enStr1>enStr2){
				return true;
			}else{
				return false;
			}
		}
	}
	function enStr2IsFront(enStr1,enStr2){
		if(enStr1==enStr2){
			return false;
		}
		var str1LowerCase=enStr1.toLowerCase();
		var str2LowerCase=enStr2.toLowerCase();
		if(str1LowerCase!=str2LowerCase){
			if(str2LowerCase<str1LowerCase){
				return true;
			}else{
				return false;
			}
		}else{
			if(enStr2>enStr1){
				return true;
			}else{
				return false;
			}
		}
	}
	function strArr(str){
		str+='';
		var arr=[];
		var prevIsChar=true;
		var hasChar=false;
		for(var i=0,len=str.length;i<len;i++){
			var character=str[i];
			if(character.charCodeAt(0) > 255){
				arr.push(character);
				hasChar=true;
				prevIsChar=true;
			}else{
				if(character==' '){
					prevIsChar=true;
				}else{
					if(prevIsChar){
						arr.push(character);
					}else{
						if(arr.length<1){
							arr.push[''];
						}
						arr[arr.length-1]+=character;
					}
					prevIsChar=false;
				}

			}
		}
		if(!hasChar){
			return [str];
		}
		return arr;
	}
	var strArr1=strArr(str1);
	var strArr2=strArr(str2);
	var strArrMinLen=Math.min(strArr1.length,strArr2.length);
	//var sortBefor=0;
	var str2Position='normal';
	var compareResult=0;
	for(var i=0;i<strArrMinLen;i++){
		var charStr1=strArr1[i]//.toLowerCase();
		var charStr2=strArr2[i]//.toLowerCase();
		var charEnStr1=pinyin(charStr1);
		var charEnStr2=pinyin(charStr2);


		if(charEnStr1!='' && charEnStr2==''){
			//console.log('str1为特殊字符')
			str2Position='before';
			break;
		}else if(charEnStr1=='' && charEnStr2!=''){
			//console.log('str2为特殊字符')
			str2Position='after';
			break;
		}else if(charEnStr1=='' && charEnStr2==''){
			//console.log('str1,str2为特殊字符')
			if(enStr1IsFront(charStr1,charStr2)){
				str2Position='after';
				break;
			}else if(enStr2IsFront(charStr1,charStr2)){
				str2Position='before';
				break;
			}
		}else{
			//console.log('str1,str2为中英字符',charEnStr1,charEnStr2,charEnStr1<charEnStr2)
			if(enStr1IsFront(charEnStr1,charEnStr2)){
				str2Position='after';
				break;
			}else if(enStr2IsFront(charEnStr1,charEnStr2)){
				str2Position='before';
				break;
			}
			//console.log('发音排序相等时')
			if(charStr1==charEnStr1 && charStr2!=charEnStr2){
				//console.log('str1为英文字符,str2为中文字符')
				str2Position='after';
			}else if(charStr1!=charEnStr1 && charStr2==charEnStr2){
				//console.log('str1为中文字符,str2为英文字符')
				str2Position='before';
			}else if(charStr1!=charEnStr1 && charStr2!=charEnStr2){
				//console.log('str1,str2都为中文字符')
				if(enStr1IsFront(charStr1,charStr2)){
					str2Position='after';
					break;
				}else if(enStr2IsFront(charStr1,charStr2)){
					str2Position='before';
					break;
				}
			}
		}
	}

	switch (str2Position){
		case 'before':
			break;
		case 'after':
			break;
		case 'normal':
			if(strArr1.length<strArr2.length){
				str2Position='after';
			}else if(strArr1.length>strArr2.length){
				str2Position='before';
			}
			break;
	}

	switch (str2Position){
		case 'before':
			compareResult=1;
			break;
		case 'after':
			compareResult=-1;
			break;
		case 'normal':
			compareResult=0;
			break;
	}

	//console.log("strArr1,strArr2",strArr1,strArr2,compareResult);
	return compareResult;
}

//返回所在地
function returnArea(num){
	var areaText='';
	switch (num){
		case '0':
			areaText='北京市';
			break;
		case '1':
			areaText='天津市';
			break;
		case '2':
			areaText='上海市';
			break;
		case '3':
			areaText='重庆市';
			break;
		case '4':
			areaText='香港特别行政区';
			break;
		case '5':
			areaText='澳门特别行政区';
			break;
		case '6':
			areaText='河北省';
			break;
		case '7':
			areaText='山西省';
			break;
		case '8':
			areaText='内蒙古自治区';
			break;
		case '9':
			areaText='辽宁省';
			break;
		case '10':
			areaText='吉林省';
			break;
		case '11':
			areaText='黑龙江省';
			break;
		case '12':
			areaText='江苏省';
			break;
		case '13':
			areaText='浙江省';
			break;
		case '14':
			areaText='安徽省';
			break;
		case '15':
			areaText='福建省';
			break;
		case '16':
			areaText='江西省';
			break;
		case '17':
			areaText='山东省';
			break;
		case '18':
			areaText='河南省';
			break;
		case '19':
			areaText='湖北省';
			break;
		case '20':
			areaText='湖南省';
			break;
		case '21':
			areaText='广东省';
			break;
		case '22':
			areaText='广西壮族自治区';
			break;
		case '23':
			areaText='海南省';
			break;
		case '24':
			areaText='四川省';
			break;
		case '25':
			areaText='贵州省';
			break;
		case '26':
			areaText='云南省';
			break;
		case '27':
			areaText='西藏自治区';
			break;
		case '28':
			areaText='陕西省';
			break;
		case '29':
			areaText='甘肃省';
			break;
		case '30':
			areaText='青海省';
			break;
		case '31':
			areaText='宁夏回族自治区';
			break;
		case '32':
			areaText='新疆维吾尔自治区';
			break;
		case '33':
			areaText='台湾省';
			break;

	};
	return areaText
}


//倒计时
function cdtimeShow(selector){
	var root={
		dlCalc:function(tn,tl){
			var mycdt=tl-tn;
			if(mycdt<0){
				mycdt=0;
			}
			var mycdt_hh=mycdt%(24*60*60*1000);
			var mycdt_mt=mycdt_hh%(60*60*1000);
			var mycdt_ss=mycdt_mt%(60*1000);


			var mycdt_dd=Math.floor(mycdt/(24*60*60*1000));
				mycdt_hh=Math.floor(mycdt_hh/(60*60*1000));
				mycdt_mt=Math.floor(mycdt_mt/(60*1000));
				mycdt_ss=Math.floor(mycdt_ss/1000);

			var mycdtObj={
				dd:mycdt_dd,
				hh:mycdt_hh,
				mt:mycdt_mt,
				ss:mycdt_ss
			}
			return mycdtObj;
		},
//		timeHtml:function(timeNow,timeDL){
//			var myObj={};
//			if(timeNow>timeDL){
//				myObj.ispass=true;
//				myObj.dateObj=root.dlCalc(timeDL,timeNow);
//			}else{
//				myObj.ispass=false;
//				myObj.dateObj=root.dlCalc(timeNow,timeDL);
//			};
//			return myObj;
//		},
		rechange:function(mySelector){
			var tn=(dateNow()).getTime();
			var tl=Number(mySelector.attr('date-dltime'));
			var tObj=root.dlCalc(tn,tl);
			mySelector.html(n20n(tObj.hh)+':'+n20n(tObj.mt)+':'+n20n(tObj.ss));
		},
		findAll:function(){
			var $selector=$(selector);
			for(var i=0,sl=$selector.length; i<sl; i++){
				root.rechange($selector.eq(i));
			}
		},
		start:function(){
			setInterval(function(){
				root.findAll();
			},1000)
		}

	}
	return root;
}

//对话窗口
function ChatWin(config){
	var chatWin;
	var root=this;
	root.conIsScroll=false;
	root.msgDateTipsDif=1000*60*5;//消息时间标签间隔
	if(config.winId==undefined){

	}else{
		chatWin=document.getElementById(config.winId);
	}
	var $chatWin=$(chatWin);

	$chatWin.addClass('chatWindow');

	if(config.winStyle!=undefined){
		$chatWin.addClass(config.winStyle);
	}
	$chatWin.html('');
//	var chatTitle=document.createElement('div');
//	chatTitle.className='chatTitle';
//	chatTitle.innerHTML='';
//	chatWin.appendChild(chatTitle);
	if(config.winTopBar){
		var chatTopBar=new ChatTopBar(config.winTopBar);
		chatWin.appendChild(chatTopBar.dom);
	}


	var chatContainer=document.createElement('div');
	chatContainer.className='chatContainer';
	root.content=chatContainer;
	var $chatContainer=$(chatContainer);



	chatWin.appendChild(chatContainer);

//	var chatEditor=new ChatEditor({
//		'toolsArr':config.winTools,
//		'sendBarConfig':{
//			'inputResize':config.inputResize,
//			'msgLength':config.msgLength,
//			'callback':{
//				'msgSend':config.callback.msgSend,
//				'inputResize':function(){
//					//chatContainer.style.bottom=$(chatEditor.dom).outerHeight()+'px';
//					//conScroll();
//				}
//			}
//		}
//
//	})

	var chatEditor=new ChatEditor(config.editor)
	chatWin.appendChild(chatEditor.dom);
	root.chatEditor=chatEditor;
	root.insertInputMsg=chatEditor.chatSendBar.insertInputMsg;

	//适当调整内容滚动高度
	var conScroll= function(callback){
		var needScroll=chatContainer.offsetHeight*2-(chatContainer.scrollHeight-chatContainer.scrollTop);
		if(callback!=undefined){
			callback();
		}
		if(needScroll>0){
			setTimeout(function(){
				conScrollDown();
			},100)
		}
	};
	root.conScroll=conScroll;

	//生成日期标签
	var dateMarkHtml=function(chatDate){
		chatDate=time2date(chatDate);
		if(new Date(chatDate).Format("YYYY-MM-DD")==dateNow().Format("YYYY-MM-DD")){
			var dateMarkHtml='<div class="chatDate">'+new Date(chatDate).Format("hh:mm")+'</div>';
		}else{
			var dateMarkHtml='<div class="chatDate">'+new Date(chatDate).Format("YYYY-MM-DD hh:mm")+'</div>';
		}
		return dateMarkHtml;
	};
	root.dateMarkHtml=dateMarkHtml;

	//聊天内容滚动到底部
	var conScrollDown= function(){
		root.conIsScroll=true;
		$chatContainer.animate(
			{
				scrollTop: chatContainer.scrollHeight-chatContainer.offsetHeight
			},
			100,
			function(){
				root.conIsScroll=false;
			}
		);
	};
	root.conScrollDown=conScrollDown;

	//聊天气泡的html
	var chatBubbleHtml=function (obj,prevMsgObj){
		var chatBubble_html=[];
		var myMsgDate=date2time(obj.date);

		var prevMsgDate=date2time(prevMsgObj.date || 0);
		var needDateMark=myMsgDate - prevMsgDate >= root.msgDateTipsDif;

		var msgMerge=false;
		//判断是否需要时间标签
		if(needDateMark){
			chatBubble_html.push(dateMarkHtml(myMsgDate));
		}else{
			msgMerge= obj.userid==prevMsgObj.userid;
		}

		chatBubble_html.push(
			'<div class="chatBubble type-'+(obj.type=='text'?'text':'html')+' '+obj.relation+(msgMerge?' msgMerge':'')+'" data-userid="'+obj.userid+'" data-chattime="'+date2time(obj.date)+'" data-chatmark="'+(obj.content.markname?obj.content.markname:'')+'">'+
				'<div class="userInfo">'+
					'<div class="headImg"><img src="'+obj.headsrc+'"></div>'+
					'<div class="nickname">'+obj.nickname+'</div>'+
				'</div>'+
				'<div class="chatInfo">'+
					'<div class="infoBox">'+
						'<div class="nickname">'+obj.nickname+'</div>'
		)
		switch(obj.type){
			case 'img':
				chatBubble_html.push('<div class="conBox imgBox"><img onclick="window.mychatWindow.previewImage($(this).attr(\'src\')); " src="'+obj.content+'" / ></div>')
				break;
			case 'classroom':
				var myBtnHtml=obj.content.btnHtml;
				if(obj.content.end-dateNow().getTime()>0){
					$domTimeout({
						$dom:function(){
							return $('.chatBubble[data-chatmark="'+obj.content.markname+'"] .chatInfo .infoBox .conBox .classroom .btnBox');
						},
						callback:function($dom){
							$dom.html('<a class="btn btn-primary disabled" >'+uiText.text('Has ended')+'</a>');
						},
						retime:obj.content.end-dateNow().getTime()
					})
				}else{
					myBtnHtml='<a class="btn btn-primary disabled" >'+uiText.text('Has ended')+'</a>';
				}

				var classroomHtml='<div class="classroom">'+
									'<div class="ownerIcon"><img src="'+obj.content.imgsrc+'"></div>'+
									'<div class="classInfo">'+
										'<div class="classTitle">'+obj.content.title+'</div>'+
										//'<div class="courseProgress"><div class="name">Sections</div><div class="bar"><div class="progressBar" date-type="Progress"><div class="completed" style="width:0%;"></div></div></div><div class="num">0/14</div></div>'+
										'<div class="classTips">'+
											'<div class="date">'+(new Date(obj.content.start)).Format("YYYY-MM-DD hh:mm")+' ~ '+(new Date(obj.content.end)).Format("hh:mm")+'</div>'+
										'</div>'+
									'</div>'+
									'<div class="btnBox">'+myBtnHtml+'</div>'+
								'</div>';
				chatBubble_html.push('<div class="conBox htmlBox">'+classroomHtml+'</div>')
				break;
			default:
				chatBubble_html.push('<div class="conBox textBox">'+obj.content+'</div>')
				break;
		}
		chatBubble_html.push('<div class="sendMode"></div></div></div></div>');
		return chatBubble_html.join('');
	}

	var chatBubbleObj=function(bubbleDom){
		var $bubble=$(bubbleDom)
		var myContentType=$bubble.find('.chatInfo .infoBox .textBox').length>0?'text':'img';

		var myObj={
			userid:$bubble.attr('data-userid'),
			nickname:$bubble.find('.userInfo .nickname').html(),
			headsrc:$bubble.find('.userInfo .headImg img').attr('src'),
			relation:$bubble.hasClass('self')?'self':'other',
			date:$bubble.attr('data-chattime'),
			type:myContentType,
			content:myContentType=='text'?$bubble.find('.chatInfo .infoBox .textBox').html():$bubble.find('.chatInfo .infoBox .imgBox img').attr('src')
		}
		return myObj;
	}


	//增加新聊天气泡消息
	var chatBubble=function(bubbleArray){

		var chatBubbleList_html=[];
		var bubbleObjNow=bubbleArray[0];
		var bubbleObjPrev=chatBubbleObj($chatContainer.children('.chatBubble ').last());
		for(var i=0,len=bubbleArray.length;i<len;i++){
			bubbleObjNow=bubbleArray[i];
			chatBubbleList_html.push(chatBubbleHtml(bubbleObjNow,bubbleObjPrev))
			bubbleObjPrev=bubbleArray[i];
		}

		$chatContainer.append(chatBubbleList_html.join(''));
		conScroll();
	}
	root.chatBubble=chatBubble;





	//监听聊天内容的滚动高度
	$chatContainer.scroll(function(){
		if(!root.conIsScroll){
			var scrollTop=$chatContainer.scrollTop();
			var myMaxScrollTop=$(this)[0].scrollHeight-$(this).outerHeight();

			var dh=5;
			if(scrollTop>dh){
				//console.log('im not top');
			}else{
				//console.log('im top');
				if(config.callback && config.callback.getHistory){
					showHistory(config.callback.getHistory());
				}
			}
			if(scrollTop<myMaxScrollTop-dh){
				//console.log('im not bottom');
			}else{
				//console.log('im bottom');
			}
		}

	});

	//查询历史消息
	var showHistory= function(historyArr,delayTime){
		var msgHtml=[];
		var noHistroyMark=$chatContainer.find('.chatDate.noHistroy');
		if(historyArr.length<1/* || historyArr=='' || historyArr=='[]'*/){//已无更多记录
			if(noHistroyMark.length<1){
				msgHtml.push('<div class="chatDate noHistroy">'+uiText.text('History has no more')+'</div>');
			}
		}else{//还有历史记录
			//如果存在  "没有历史纪录的标记" 则移除该标记
			if(noHistroyMark.length>0){
				noHistroyMark.remove();
			}

			var bubbleObjNow=historyArr[0];
			var bubbleObjPrev=historyArr[0];
			for(var i=0,len=historyArr.length;i<len;i++){
				bubbleObjNow=historyArr[i];
				msgHtml.push(chatBubbleHtml(bubbleObjNow,bubbleObjPrev/*,i==0?true:false*/))
				bubbleObjPrev=historyArr[i];
			}


			//如果不存在 以上为历史纪录 的标记 则加入该标记
			if($chatContainer.children('.oldMsgLine').length<1){
				msgHtml.push('<div class="chatDate oldMsgLine">'+uiText.text('In the top of history')+'</div>');
			}
		}


		//var needScroll=chatContainer.offsetHeight-(chatContainer.scrollHeight-chatContainer.scrollTop);


		var sh_old=chatContainer.scrollHeight;
		var st_old=chatContainer.scrollTop;

		$chatContainer.prepend(msgHtml.join(''));
		if(delayTime==undefined){
			delayTime=0;
		}
		setTimeout(function(){
			var sh_new=chatContainer.scrollHeight;
			$chatContainer.animate({scrollTop: st_old + (sh_new - sh_old)}, 0);
		},delayTime)

		return root;
	};
	root.showHistory=showHistory;

	var chatNoticeBar=document.createElement('div');
	chatNoticeBar.className='chatNoticeBar';
	chatWin.appendChild(chatNoticeBar);
	var chatNotice=function(noticeHtml){
		if(noticeHtml!=''){
			chatNoticeBar.innerHTML=noticeHtml;
			$(chatNoticeBar).slideDown(300);
			$(chatContainer).animate({'top':81},300);
		}else{
			$(chatNoticeBar).slideUp(300);

			$(chatContainer).animate({'top':45},300);
		}

	}
	root.notice=chatNotice;
	
	root.editorDisplay=function(status){
		if(status===true){
			$chatContainer.css('bottom','');
			$(chatEditor.dom).show();
		}else{
			$chatContainer.css('bottom',0);
			$(chatEditor.dom).hide();
		}
	}
	
	if(config.owner==='guest'){
		root.editorDisplay(false);
	}
}

function ChatTopBar(config){
	var root=this;
	var infoArr=config.info;

	var chatTopBar=document.createElement('div');
	chatTopBar.className='chatTopBar';

	var chatTopTitle=document.createElement('div');
	chatTopTitle.className='title';
	chatTopTitle.setAttribute('data-id',infoArr[0].id);
	chatTopBar.appendChild(chatTopTitle);


	chatTopTitle.innerHTML='<span class="titleText">'+infoArr[0].nickname+'</span><span class="titleBtn">▼</span>';

//	function memberList(){
//		var myHtml=
//	}

	var memebrListShow=false;
	function memberListShow(){
		if(memebrListShow){
			$(memberListBox).slideUp(function(){
				$(chatMemberBox).hide();
				memebrListShow=false;
			});
		}else{
			$(chatMemberBox).show();
			$(memberListBox).slideDown(function(){
				memebrListShow=true;
			});
		}
	}
	if(config.type=='group'){
		var chatMemberBox=document.createElement('div');
		chatMemberBox.className='chatMemberBox';
		var memberListBox=document.createElement('ul');
		memberListBox.className='memberListBox';
		var memberListArr=[];
		for(var i=1,len=infoArr.length;i<len;i++){
			var infoI=infoArr[i];
			memberListArr.push(
				'<li class="memberList" data-id="'+infoI.id+'">'+
					'<div class="headImg"><img src="'+infoI.headImgSrc+'" /></div>'+
					'<div class="userName">'+infoI.nickname+'</div>'+
				'</li>'
			)
		}
		memberListBox.innerHTML=memberListArr.join('');
		chatMemberBox.appendChild(memberListBox);
		chatTopBar.appendChild(chatMemberBox);


		$(chatTopTitle).on('click','.titleText,.titleBtn',memberListShow)
		$(memberListBox).on('click',function(e){
			e.stopPropagation();
		})
		$(chatMemberBox).on('click',memberListShow);

		if(config.callback){
			if(config.callback.listClick){
				$(memberListBox).on('click','.memberList',function(e){
					config.callback.listClick($(this).attr('data-id'))
				})
			}
		}
	}else{
		if(config.callback){
			if(config.callback.singleClick){
				$(chatTopTitle).on('click','.titleText,.titleBtn',function(){
					config.callback.singleClick($(chatTopTitle).attr('data-id'))
				})
			}
		}
	}


	root.dom=chatTopBar;
}


function ChatEditor(config){
	var chatEditor=document.createElement('div');
	chatEditor.className='chatEditor';

	var myChatTools=new ChatTools(config.tools)
	chatEditor.appendChild(myChatTools.dom)

	var myChatSendBar=new ChatSendBar(config.sendBar)
	chatEditor.appendChild(myChatSendBar.dom);

	this.dom=chatEditor;
	this.chatSendBar=myChatSendBar;
}

function ChatTools(toolsArr){
	var myChatTools=document.createElement('div');
	myChatTools.className='toolsBox';
	function Tools(toolsObj){
		var myTools=document.createElement('div');
		myTools.className='toolsBtn '+toolsObj.className+(toolsObj.style?' '+toolsObj.style:'');
		if(toolsObj.hoverTips){
			myTools.setAttribute('title',toolsObj.hoverTips)
		}

		$(myTools).on('click',function(){
			toolsObj.callback();
		})
		myChatTools.appendChild(myTools);
	}
	for(var i=0,len=toolsArr.length;i<len;i++){
		Tools(toolsArr[i]);
	}
	this.dom=myChatTools;
}

// textarea版的发送消息
//function ChatSendBarOld(config){
//	var sendBar=document.createElement('div');
//	sendBar.className='textAreaBox';
//
//	var msgInput=document.createElement('textarea');
//	msgInput.className='chatWrite';
//
//	var sendBtn=document.createElement('input');
//	sendBtn.className='sendBtn';
//	sendBtn.type='button';
//	sendBtn.value=uiText.text('Send');
//
//
//	function SendMsg(){
//		var inputValue=strCodeCheck(msgInput.value);
//		if(inputValue!=''){
//			config.callback.msgSend(inputValue);
//		}else{
//			var emptySendTips=new PopUpBox({
//				'type':'tips',
//				'content':uiText.text('Message can not be empty'),
//				'show':true,
//				'time':3000
//			})
//		}
//		msgInput.value='';
//	}
//
//	$(msgInput).on('keydown',function(e){
//
//		var textHeight=18;
//		// enter事件
//		if (e.keyCode == 13)  {
//			if(e.ctrlKey || e.altKey){
//				//console.log("ctrl换行   alt换行");
//	    		var positonI=msgInput.selectionStart;
//	    		var strOld=msgInput.value;
//	    		var strBefore=strOld.substr(0,positonI);
//	    		var strAfter=strOld.substr(positonI);
//	    		var strNew=strBefore+'\n'+strAfter;
//	    		msgInput.value=strNew;
//	    		msgInput.setSelectionRange(positonI+1,positonI+1);
//	    		msgInput.scrollTop=msgInput.scrollTop+textHeight;
//			}else if(e.shiftKey){
//				//console.log("shift换行");
//			}else{
//				e.preventDefault();
//				SendMsg();
//			}
//		}
//	})
//
//	$(msgInput).on('keyup',function(e){
//
//		var maxFontNum=450;
//		var inputValue=msgInput.value
//		var myFontNum=inputValue.length;
//
//
//		//高度调整事件
//		if(config.inputResize!=undefined){
//			var minHeight=config.inputResize[0];
//			var maxHeight=config.inputResize[1];
//			msgInput.style.height = minHeight+'px ';
//			var msgScrollHeight=msgInput.scrollHeight
//			if (msgScrollHeight > minHeight) {
//				if (msgScrollHeight > maxHeight) {
//					msgInput.style.height = maxHeight + 'px';
//					//msgInput.style.overflowY = 'scroll';
//				} else {
//					msgInput.style.height = msgScrollHeight + 'px';
//					//msgInput.style.overflowY = 'hidden ';
//				}
//			}
//
//		}
//		if(myFontNum>maxFontNum){
//			msgInput.value=inputValue.substr(0,maxFontNum);
//			_Tips(uiText.text('The word limit is')+' '+maxFontNum)
//		}
//		config.callback.inputResize();
//	})
//
//	$(sendBtn).on('click',SendMsg)
//	$(sendBtn).on('click',function(){
//		msgInput.focus()
//	})
//
//	sendBar.appendChild(msgInput);
//	sendBar.appendChild(sendBtn);
//
//	this.dom=sendBar;
//}


//function insertHtmlAtCaret($input,insertHtml) {
//	$input.focus()
//	var sel = window.getSelection();
//	var range = sel.getRangeAt(0);
//
//	var el = document.createElement("div");
//	el.innerHTML = insertHtml;
//	var htmlNode = el.firstChild;
//	//var htmlNode=document.createTextNode('<img >')
//
//	//插入内容
//	range.insertNode(htmlNode);
//
//	//更新光标位置
//	range.setStartAfter(htmlNode);//设置光标起点
//	sel.removeAllRanges();//移除光标
//	sel.addRange(range);//添加光标
//}

function InsertAtCaret(config) {
	var root=this;
	root.inputDom=config.inputDom;
	root.caretObj;
	var rangeElement=root.inputDom;
	var sel= window.getSelection();

	//设置光标点
	function SetCaretLocation(){
		root.myRange = sel.getRangeAt(0);
		rangeElement = root.myRange.commonAncestorContainer;
	}

	//插入内容
	var InsertHtml = function (insertStr){
		root.inputDom.focus();

		var el = document.createElement('div');
		if(typeof(insertStr)=='string'){
			el.innerHTML = n2br(insertStr);
		}else{
			el.innerHTML = '';
		}

		//var htmlNode = el.childNodes;
		var htmlNode = el.childNodes;
		if(!root.myRange){
			root.inputDom.focus();
			SetCaretLocation();
		}
		if(root.myRange.commonAncestorContainer!=rangeElement){
			root.inputDom.focus();
			SetCaretLocation();
		}
		var htmlNodeChild;
		for(var htmlNodeEq=0,htmlNodeLen=htmlNode.length;htmlNodeEq<htmlNodeLen;htmlNodeEq++){
			htmlNodeChild=htmlNode[0];
			root.myRange.insertNode(htmlNodeChild);
			root.myRange.setStartAfter(htmlNodeChild);//设置光标起点
		}
		//更新光标位置
		sel.removeAllRanges();//移除光标
		sel.addRange(root.myRange);//添加光标
	}
	root.insertHtml=InsertHtml;
	$(root.inputDom).on('keyup click',SetCaretLocation)


}
//消息编码过滤
function msgCodeReset(str,path){
//
//var msg = '<div >啊实打<div></div>  <img src="test.jpg" >  <img src="" >实的<img class="emoji" src="img/moodface/f10.png"><img class="screenshot" src="http://img.my.csdn.net/uploads/201301/05/1357357366_8428.jpg">啊实打实大苏打啊实打实的<img class="emoji" src="img/moodface/f27.png"><img class="screenshot" src="http://img.my.csdn.net/uploads/201301/05/1357357366_8428.jpg"></div>asdasdasd';
//msg=msgCodeReset(msg,{
//	'emoji':'eeee/',
//	'screenshot':'sss/',
//	'default':'ddd/'
//});
	var reStr=str.replace(/<.*?>/g,function(r){//找出全部<tagname >标签
		if(/<img/.test(r)){//图片标签处理
			var imgClass=/class="(.*?)"/.exec(r)
			if(imgClass){//有class属性
				if(imgClass[1]){//class属性有值
					imgClass=imgClass[1];
					if(/emoji|screenshot/.test(imgClass)==false){
						imgClass='default';
					}
				}else{
					imgClass='default';
				}
			}else{
				imgClass='default'
			}
			if(!path){
				var reText=''
				switch (imgClass){
					case 'emoji':
						reText='表情';
						break;
					case 'screenshot':
						reText='截图';
						break;
					default:
						reText='图片';
						break;
				}
				return '['+reText+']';
			}
			var imgSrc,imgFileName;
			imgSrc=/src="(.*?)"/.exec(r);
			//imgSrc=/src=".*([^\/]*?)"/.exec(r);
			if(imgSrc){//src属性存在
				if(imgSrc[1]){//src地址不为空
					imgSrc=imgSrc[1];
					//imgFileName=/.*\/(.*)/.exec(imgSrc);
					imgFileName=imgSrc.replace(/.*\//,'');
				}else{//src地址为空
					imgSrc='';
					imgFileName='';
				}
			}else{//没有src属性
				imgSrc='';
				imgFileName='';
			}
			if(imgFileName){
				imgSrc=path[imgClass]+imgFileName;
				return '<img onerror="reloadImg(this)" class="'+imgClass+'" src="'+imgSrc+'" />';
			}else{
				return '';
			}
		}else if(/<br/.test(r)){//其他标签去除
			return '<br />';
		}else{//其他标签去除
			return '';
		}
	});
	return reStr;
}
function ChatSendBar(config){
	var root=this;
	var sendBar=document.createElement('div');
	var inputValueOld='';
	sendBar.className='textAreaBox';

	var msgInput=document.createElement('div');
	msgInput.setAttribute('contenteditable-directive','true');
	msgInput.setAttribute('contentEditable','true');
	msgInput.className='chatWrite';
	msgInput.style.margin=0;
	root.msgInput=msgInput;

	var sendBtn=document.createElement('input');
	sendBtn.className='sendBtn';
	sendBtn.type='button';
	sendBtn.value=uiText.text('Send');

	if(config.msgLength==undefined){
		config.msgLength=[1,3000]
	}

	var msgLen_min=config.msgLength[0];
	var msgLen_max=config.msgLength[1];

	function SendMsg(){
		if(checkMsgLength()==false){
			return false;
		}
		var inputText=msgInput.innerText.trim();
		var inputHtml=msgInput.innerHTML;
		if($(msgInput).find('img').length>0 || inputText.length>=msgLen_min){
			config.callback.msgSend(n2br(inputHtml));
			msgInput.innerHTML='';
			msgInputResize();
		}else{
			_Tips(msgLen_min>1?uiText.text('Message must be at least  %1  characters',msgLen_min):uiText.text('Message can not be empty'))
		}
	}
	var msgLengthTips=new PopUpBox({
		'type':'tips',
		'style':'black',
		'show':false,
		'content':'',
		'time':3000
	});
	function checkMsgLength(){
		var maxFontNum=450;
		var maxImageNum=30;
		var inputText=msgInput.innerText;
		var inputHtml=msgInput.innerHTML;
		var myFontNum=inputText.length;
		var myImgNum=$(msgInput).find('img').length;
		//console.log("ss",myImgNum);
		if(myImgNum>maxImageNum){
			//msgInput.innerHTML=msgInput.innerHTML.substring(0,maxFontNum);
			msgLengthTips.content.innerHTML=uiText.text('The images limit is')+' '+maxImageNum;
			msgLengthTips.show();
			return false;
		}
		if(myFontNum>maxFontNum){
			//msgInput.innerHTML=msgInput.innerHTML.substring(0,maxFontNum);
			//The message is too long. Please separate it into several parts and send again.
			msgLengthTips.content.innerHTML=uiText.text('The word limit is')+' '+maxFontNum+'<br />'+
											uiText.text('You input words ')+' '+myFontNum;
			msgLengthTips.show();
			/*var startAfterNode=msgInput.lastChild;
			if(startAfterNode){
				var editAreaRange=window.getSelection().getRangeAt(0);
				editAreaRange.setStartAfter(startAfterNode);
				editAreaRange.setEndAfter(startAfterNode);
				window.getSelection().removeAllRanges();
				window.getSelection().addRange(editAreaRange);
			}*/
			return false;
		}
		if(msgLengthTips.isShow){
			msgLengthTips.close();
		}
		return true;
		//msgInputResize();
	}

	//高度调整事件
	function msgInputResize(){
		if(config.inputResize!=undefined){
			var minHeight=config.inputResize[0];
			var maxHeight=config.inputResize[1];
			msgInput.style.height = minHeight+'px ';
			var msgScrollHeight=msgInput.scrollHeight
			if (msgScrollHeight > minHeight) {
				if (msgScrollHeight > maxHeight) {
					msgInput.style.height = maxHeight + 'px';
					//msgInput.style.overflowY = 'scroll';
				} else {
					msgInput.style.height = msgScrollHeight + 'px';
					//msgInput.style.overflowY = 'hidden ';
				}
			}

		}
		config.callback.inputResize();
	}
	//插入文字事件
	var myInsert=new InsertAtCaret({
		inputDom:msgInput
	})

	$(msgInput).on('keydown',function(e){

		var textHeight=18;
		// enter事件
		if(e.keyCode==27){
			return false;
		}
		if (e.keyCode == 13)  {
			if(e.ctrlKey || e.altKey){
				//console.log("ctrl换行   alt换行");
				myInsert.insertHtml('<br />');
	    		msgInput.scrollTop=msgInput.scrollTop+textHeight;
			}else if(e.shiftKey){
				//console.log("shift换行");
			}else{
				e.preventDefault();
				SendMsg();
			}
		}
	})

	//文本内容长度限制
	$(msgInput).on('input keyup',checkMsgLength);
	$(msgInput).on('paste',function(e){
		e.preventDefault();
		var clipboardDate=e.originalEvent.clipboardData
		var clipboardContent;
        if (/text\/html/.test(clipboardDate.types)) {
            clipboardContent=clipboardDate.getData('text/html');
            if(clipboardContent.match(/<body[\w\W\r\n]*body>/)){
            	clipboardContent=clipboardContent.match(/<body[\w\W\r\n]*body>/)[0];//获取body
            }

            clipboardContent=clipboardContent.replace(/<!--[\w\W\r\n]*?-->/g,'');//去除注释
            clipboardContent=clipboardContent.replace(/<\/?[^>]*>/g,function(rStr){
            	if(/<img/.test(rStr)){//图片标签处理
					var imgClass=rStr.match(/class="[\w\W\r\n]*/);
					if(imgClass){//有class属性
						var className='';
						if(/emoji/.test(imgClass[0])){
							className='emoji';
						}else if(/screenshot/.test(imgClass[0])){
							className='screenshot';
						}
						if(className){
							var srcStr=rStr.match(/src="(.*?)"/)[0];
							return '<img class="'+className+'" '+srcStr+' />';
						}else {
							return '';
						}
					}else{
						return '';
					}
				}else if(/<br/.test(rStr)){//其他标签去除
					return '<br />';
				}else{//其他标签去除
					return '';
				}
            });//去除标签
            clipboardContent=clipboardContent.replace(/\n+/g,'');//去除空格
            clipboardContent=$.trim(clipboardContent);//移除两端空白


        }else if (/text\/plain/.test(clipboardDate.types)) {
            clipboardContent=clipboardDate.getData('text/plain');
        }
		//console.log(clipboardDate.types);
		myInsert.insertHtml(clipboardContent);
	});
	$(msgInput).on('focus',function(){
		$(this).addClass('focus');
	})






	$(sendBtn).on('click',SendMsg);
	$(sendBtn).on('click',function(){
		msgInput.focus();
	})

	sendBar.appendChild(msgInput);
	sendBar.appendChild(sendBtn);

	root.dom=sendBar;

	root.insertInputMsg=function(inputMsg){
		myInsert.insertHtml(inputMsg);
		checkMsgLength();
	}
}


function SearchBar(config){
	var searchBar=document.createElement('div');
	searchBar.className='searchBar';

	var searchInput=document.createElement('input');
	searchInput.className='searchInput';
	searchInput.type='text';

	$(searchInput).on('input',function(){
		var keyword=$(this).val();
		FilterList(keyword,config.$listBox,config.listClass,config.conClassArr)
	})
	searchBar.appendChild(searchInput);
	function FilterList(keytext,$listBox,listClass,conClassArr){
		$listBox.find(listClass).each(function(){
			var $this=$(this);
			var hasKey=false;
			for(var i=0,len=conClassArr.length;i<len;i++){
				var conTextI=$this.find(conClassArr[i]).text();
				if(conTextI.indexOf(keytext)>=0){
					hasKey=true;
					break;
				}
			}
			if(hasKey){
				$this.show();
			}else{
				$this.hide();
			}
		})

	}
	this.dom=searchBar;
	this.searchInput=searchInput;
}


function qnaWin(config){
	var qnaWin;
	var root=this;
	root.conIsScroll=false;
	root.msgDateTipsDif=5*1000;//消息时间标签间隔
	if(config.winId==undefined){

	}else{
		qnaWin=document.getElementById(config.winId);
	}
	var $qnaWin=$(qnaWin);

	$qnaWin.addClass('qnaWindow');

	if(config.winStyle!=undefined){
		$qnaWin.addClass(config.winStyle);
	}



	var qnaContainer=document.createElement('div');
	qnaContainer.className='qnaContainer';
	root.content=qnaContainer;
	var $qnaContainer=$(qnaContainer);



	var qnaSearchBar=new SearchBar({
		$listBox:$qnaContainer,
		listClass:'.bubble',
		conClassArr:[
			'.bubbleContent .textBox',
			'.bubbleInfo .asker'
		]
	})

	qnaWin.appendChild(qnaSearchBar.dom);
	qnaWin.appendChild(qnaContainer);

	var qnaEditor=new ChatEditor(config.editor)
	qnaWin.appendChild(qnaEditor.dom);
	root.insertInputMsg=qnaEditor.chatSendBar.insertInputMsg;

	//适当调整内容滚动高度
	var conScroll= function(callback){
		var needScroll=qnaContainer.offsetHeight*2-(qnaContainer.scrollHeight-qnaContainer.scrollTop);
		if(callback!=undefined){
			callback();
		}
		if(needScroll>0){
			setTimeout(function(){
				conScrollDown();
			},100)
		}
	};
	root.conScroll=conScroll;


	//聊天内容滚动到底部
	var conScrollDown= function(){
		root.conIsScroll=true;
		$qnaContainer.animate(
			{
				scrollTop: qnaContainer.scrollHeight-qnaContainer.offsetHeight
			},
			100,
			function(){
				root.conIsScroll=false;
			}
		);
	};
	root.conScrollDown=conScrollDown;

	//聊天气泡的html
	var qnaBubbleHtml=function (obj){
		var qnaBubble_html=[];

		qnaBubble_html.push(
			'<div class="bubble'+(obj.liked?' liked':'')+'" data-questionid="'+obj.questionId+'" data-askerid="'+obj.askerId+'">'+
				'<div class="bubbleContent">'
		)
		switch(obj.type){
			case 'img':
				qnaBubble_html.push('<div class="imgBox"><img onclick="window.mychatWindow.previewImage($(this).attr(\'src\')); " src="'+obj.content+'" / ></div>')
				break;
			default:
				qnaBubble_html.push('<div class="textBox">'+obj.content+'</div>')
				break;
		}
		
		var likeBtnStr='<div class="likeBtn '+(config.owner=='teacher'?'answer':'question')+'">'+(config.owner=='teacher'?uiText.text('Answer'):'')+'</div>';
		if(config.owner==='guest'){
			likeBtnStr='';
		}
		
		
		
		qnaBubble_html.push('<div class="sendMode"></div></div>'+
			'<div class="bubbleInfo">'+
				'<div class="left">'+
					'<div class="asker">'+obj.askerName+'</div>'+
				'</div>'+
				'<div class="right">'+
					'<div class="likeNum">'+uiText.text('Same question')+': <span class="num">'+obj.likeNum+'</span> </div>'+
					likeBtnStr+
				'</div>'+
			'</div>'+
		'</div>');
		return qnaBubble_html.join('');
	}

	$qnaContainer.on('click','.likeBtn.question',function(){
		if($(this).closest('.bubble').hasClass('liked')){
			return false;
		}
		if(config.callback && config.callback.clickLike){
			config.callback.clickLike($(this).closest('.bubble').attr('data-questionid'))
		}
	})
	$qnaContainer.on('click','.likeBtn.answer',function(){
		if(config.callback && config.callback.clickAnswer){
			config.callback.clickAnswer($(this).closest('.bubble'))
		}
	})

	//增加新聊天气泡消息
	var qnaBubble=function(bubbleArray){

		var qnaBubbleList_html=[]
		for(var i=0,len=bubbleArray.length;i<len;i++){
			qnaBubbleList_html.push(qnaBubbleHtml(bubbleArray[i]))
		}

		$qnaContainer.append(qnaBubbleList_html.join(''));
		conScroll();
	}
	root.qnaBubble=qnaBubble;





	//监听聊天内容的滚动高度
	$qnaContainer.scroll(function(){
		if(!root.conIsScroll){
			var scrollTop=$qnaContainer.scrollTop();
			var myMaxScrollTop=$(this)[0].scrollHeight-$(this).outerHeight();

			var dh=5;
			if(scrollTop>dh){
				//console.log('im not top');
			}else{
				//console.log('im top');
			}
			if(scrollTop<myMaxScrollTop-dh){
				//console.log('im not bottom');
			}else{
				//console.log('im bottom');
			}
		}

	});


	var clickLike=function (qId,liked,likeNum){
		var myQBox=$qnaContainer.children('.bubble[data-questionid="'+qId+'"]')
		if(myQBox.length>0){
			if(liked && !myQBox.hasClass('liked')){
				myQBox.addClass('liked');
			}
			if(likeNum!=undefined){
				myQBox.find('.likeNum .num').html(likeNum)
			}

		}
	}
	root.clickLike=clickLike;
	
	root.editorDisplay=function(status){
		if(status===true){
			$qnaContainer.css('bottom','');
			$(qnaEditor.dom).show();
		}else{
			$qnaContainer.css('bottom',0);
			$(qnaEditor.dom).hide();
		}
	}
	
	if(config.owner==='guest'){
		root.editorDisplay(false);
	}
}
function ClassroomMemberWin(config){
	var memberWin;
	var root=this;
	if(config.winId==undefined){

	}else{
		memberWin=document.getElementById(config.winId);
	}
	var $memberWin=$(memberWin);

	$memberWin.addClass('classroomMemberWindow');


	var teacherListTitle=document.createElement('div');
	teacherListTitle.className='memberListTitle teacher';
	teacherListTitle.innerHTML=uiText.text('Teacher');

	var teacherListCon=document.createElement('div');
	teacherListCon.className='memberListBox teacher';
	var $teacherListCon=$(teacherListCon);

	var assistantListCon=document.createElement('div');
	assistantListCon.className='memberListBox assistant';
	var $assistantListCon=$(assistantListCon);

	var memberListTitle=document.createElement('div');
	memberListTitle.className='memberListTitle teacher';
	memberListTitle.innerHTML=uiText.text('Student');

	var memberListCon=document.createElement('div');
	memberListCon.className='memberListBox member';
	var $memberListCon=$(memberListCon);

	/*//列表html
	function MemberListHtml(memberObj){
		var myHtml=''

		myHtml+='<div class="memberName">'+memberObj.name+'</div>';
		if(memberObj.authorize){
			myHtml+='<div class="iconAuthorize"></div>';
		}

		if(memberObj.voice){
			myHtml+='<div class="microphone';
			if(memberObj.voice>1){
				myHtml+=' disabled';
			}
			myHtml+='"></div>';
		}
		if(memberObj.showUp!=true){
			myHtml+='<div class="showUp"></div>';
		}
		if(memberObj.voice){
			myHtml+='<div class="microphone';
			if(memberObj.voice>1){
				myHtml+=' disabled';
			}
			myHtml+='"></div>';
		}
		if(memberObj.prize!=undefined && memberObj.prize>=0){
			myHtml+='<div class="prize">x<span class="num">'+memberObj.prize+'</span></div>';
		}
		return myHtml;
	}*/
	//列表html
	function memberListEdit(listDom,memberObj){


	}
	//创建单个列表
	function Member(memberObj){
		var member=this;
		member.$dom=$('<div class="memberList" data-id="'+memberObj.id+'"></div>');

		member.update=function(){
			member.$dom.html('');
			var $memberName=$('<div class="memberName" title="'+memberObj.name+'">'+memberObj.name+'</div>').appendTo(member.$dom);


			if(memberObj.raiseHand!=undefined){
				var $raiseHand=$('<div class="raiseHand"></div>').appendTo(member.$dom);
				$raiseHand.css({
					'cursor':'default'
				});
				if(!memberObj.raiseHand){
					$raiseHand.addClass('disabled');
				}
			}
			if(memberObj.authorize!=undefined){
				var $authorize=$('<div class="iconAuthorize"></div>').appendTo(member.$dom);
				$authorize.css({
					'cursor':'default'
				});
				if(!memberObj.authorize){
					$authorize.addClass('disabled');
				}
				$authorize.on('click',function(){
					if(!canModifyOther){
						return false;
					}
					if($authorize.hasClass('disabled')){
						//console.log('授权:',memberObj.id);
						window.mychatWindow.memberAuthorize(memberObj.id, true);
					}else{
						//console.log('取消授权:',memberObj.id);
						window.mychatWindow.memberAuthorize(memberObj.id, false);
					}
				})
			}

			if(memberObj.voice!=undefined){
				var $microphone=$('<div class="microphone"></div>').appendTo(member.$dom);
				$microphone.css({
					'cursor':'default'
				});
				if(!memberObj.voice){
					$microphone.addClass('disabled');
				}
				$microphone.on('click',function(){
					if(!canModifyOther){
						return false;
					}
					if($microphone.hasClass('disabled')){
						//console.log('允许发音:',memberObj.id);
						window.mychatWindow.memberMute(memberObj.id, true);
					}else{
						//console.log('静音:',memberObj.id);
						window.mychatWindow.memberMute(memberObj.id, false);
					}
				})
			}

			if(memberObj.showUp!=undefined){
				var $showUp=$('<div class="showUp"></div>').appendTo(member.$dom);
				if(!canModifyOther){
					$showUp.css({
						'cursor':'default'
					});
				}else{
					$showUp.addClass('btnMode');
				}
				if(!memberObj.showUp){
					$showUp.addClass('disabled');
				}
				$showUp.on('click',function(){
					if(!canModifyOther){
						return false;
					}
					if($showUp.hasClass('disabled')){
						//console.log('上台:',memberObj.id);
						window.mychatWindow.memberStage(memberObj.id, true);
					}else{
						//console.log('取消上台:',memberObj.id);
						window.mychatWindow.memberStage(memberObj.id, false);
					}
				})
			}

			if(canModifyOther){
				if(memberObj.getOut!=undefined){
					var $getOut=$('<div class="getOut"></div>').appendTo(member.$dom);
					if(!canModifyOther){
						$getOut.css({
							'cursor':'default'
						});
					}else{
						$getOut.addClass('btnMode');
					}
					if(!memberObj.showUp){
						$getOut.addClass('disabled');
					}
					$getOut.on('click',function(){
						//console.log('请出教室:',memberObj.id);
							window.mychatWindow.memberKickout(memberObj.id);
					})
				}
			}
			if(memberObj.prize!=undefined){
				var $prize=$('<div class="prize"></div>').appendTo(member.$dom);
				$prize.css({
					'cursor':'default'
				});
				$prize.append('x<span class="num">'+memberObj.prize+'</span>')
				$prize.on('click',function(){
					if(!canModifyOther){
						return false;
					}
					//console.log('奖励:',memberObj.id);
					// XXX: teacher can prize a student here
				})
			}
		}
		member.update();

		switch (memberObj.type){
			case 'teacher':
				teacherListCon.appendChild(member.$dom[0]);
				break;
			case 'assistant':
				assistantListCon.appendChild(member.$dom[0]);
				break;
			case 'member':
				memberListCon.appendChild(member.$dom[0]);
				break;
		}
	}
	root.memberListData={};
	//添加列表
	var AddMemberList=function (arr){
		for(var i=0,len=arr.length;i<len;i++){
			var memberObj=$.extend(true, {}, arr[i]);
			if(root.memberListData[memberObj.id]==undefined){
				memberObj.memberDom=new Member(memberObj);
				root.memberListData[memberObj.id]=memberObj;
			}else{
				var memeberObj=$.extend(true, root.memberListData[memberObj.id], memberObj);
				memeberObj.memberDom.update();
			}
		}
		studentListSort();
	}
	root.AddMemberList=AddMemberList;

	//删除列表
	var DeleMemberList=function (arr){
		for(var i=0,len=arr.length;i<len;i++){
			if(root.memberListData[arr[i].id]!=undefined){
				root.memberListData[arr[i].id].memberDom.$dom.remove();
				delete root.memberListData[arr[i].id];

			}
		}
	}
	root.DeleMemberList=DeleMemberList;

	function studentListSort(domDataList,dataDomName,$parentContainer,sortCallback,mergeSort){
		var domDataList={};
		for(var k in root.memberListData){
			var memberData=root.memberListData[k];
			if(memberData.type=='member'){
				domDataList[memberData.id]=memberData;
			}
		}
		var dataDomName='memberDom';
		var $parentContainer=$(memberListCon);
		var domListArr=[];
		for(var k in domDataList){
			var domData=domDataList[k];
			domListArr.push(domData);
		}
		domListArr.sort(function(a,b){
			return a.id-b.id;
		});

		for(var i=0,len=domListArr.length;i<len;i++){
			var dataDom=domListArr[i][dataDomName];
			var domSn=$parentContainer.children().index(dataDom.$dom);
			if(domSn!=i){
				var $nextDataDom=$parentContainer.children().eq(i);
				$nextDataDom.before(dataDom.$dom);
			}
		}
	}



	//初始化列表内容
	AddMemberList(config.memberArr)


	//搜索条
	var memberSearchBar=new SearchBar({
		$listBox:$memberWin,
		listClass:'.memberList',
		conClassArr:[
			'.memberName'
		]
	})

	memberWin.appendChild(memberSearchBar.dom);
	memberWin.appendChild(teacherListTitle);
	memberWin.appendChild(teacherListCon);
	memberWin.appendChild(assistantListCon);
	memberWin.appendChild(memberListTitle);
	memberWin.appendChild(memberListCon);

	//列表点击事件
	$memberWin.on('click','.memberList',function(e){
		if(config.callback && config.callback.listClick){
			var $list=$(this)
			var memberId=$list.attr('data-id');
			var myVoice=0;
			var $myVoice=$list.children('.microphone');
			if($myVoice.length>0){
				myVoice=1;
				if($myVoice.hasClass('disabled')){
					myVoice=2;
				}
			}
			var myPrize=-1;
			var $myPrize=$list.children('.prize');
			if($myPrize.length>0){
				myPrize=parseInt($myPrize.children('.num').html())
			}
			var myObj={
				id:$list.attr('data-id'),
				name:$list.children('.memberName').html(),
				authorize:$list.children('.iconAuthorize').length>0?true:false,
				type:$list.parent('.memberListBox')[0].className.substring(14),
				voice:myVoice,
				prize:myPrize
			}
			config.callback.listClick(this,myObj,e)
		}
	})

}












//修改用户头像
function changeUserHeadImg(userId,headImgSrc){
	$('[data-userid='+userId+'] .userInfo .headImg img').attr('src',headImgSrc)
}



//搜索联系人
function contactsSearch(rootObj){
	if(rootObj!=undefined){
		contactsSearch.searchObj=rootObj;
	}
	var root=contactsSearch;
		root.haveKey=function(obj,myKey){
			var isHave=false;
			var classArray=root.searchObj.byClass;
			for(var i=0,j=classArray.length;i<j;i++){
				var myClassContent=obj[classArray[i]]+'';
				if(myClassContent.indexOf(myKey)>-1){
					isHave=true;
					break;
				}
			}
			return isHave;
		};
		root.searchData=function(myKey){
			var resultArray=[];
			function clearResultBox(){
				if($('.contactsSearch').children('.resultBox').length>0){
					$('.contactsSearch').children('.resultBox').stop(true,true).remove();
				}
			}
			//判断搜索关键词长度
			if(myKey.length>0){
				var dataObj=root.searchObj.data;
				for(var k in dataObj){
					var mydataObj=dataObj[k];
					if(root.haveKey(mydataObj,myKey)){
						resultArray.push(mydataObj);
					}
				};


				clearResultBox();
				$('.contactsSearch').append('<ul class="resultBox"></ul>');

				var $resultBox=$('.contactsSearch').children('.resultBox');

				//判断搜索结果个数
				if(resultArray.length>0){
					root.showResult(resultArray);
				}else{
					if(checkRule(myKey,'phone')=='ok' && root.searchObj.callback!=undefined && root.searchObj.callback.serverSearch!=undefined){
						$('.contactsSearch').children('.resultBox').html('<div>'+uiText.text('Searching')+'...</div>').css({
							'line-height':'64px',
							'text-align':'center'
						});
						root.searchObj.callback.serverSearch(root.serverSearch);
					}else{
						root.noResult()
					}
				}//判断搜索结果个数end
			}else{
				clearResultBox()
			}//判断关键词长度结束


		};
		root.serverSearch=function(resultArray){
			if($('.contactsSearch').length<1 || $('.contactsSearch').children('.resultBox').length<1){
				return false;
			}
			if(resultArray!='' && resultArray!=false && resultArray!=undefined){
				$('.contactsSearch').children('.resultBox').html('<div style="padding:5px;color:#999;text-align:center;">'+uiText.text('Here is the server search results')+'</div>').removeAttr('style');
				root.showResult(resultArray);
			}else{
				root.noResult()
			}
		};
		root.noResult=function(){
			$('.contactsSearch').children('.resultBox').html(uiText.text('No Search Results')).css({
				'line-height':'64px',
				'text-align':'center'
			}).fadeOut(3000,function(){
				$(this).remove();
			})
		};
		root.showResult=function(resultArray){
			var $resultBox=$('.contactsSearch').children('.resultBox');

			var resultHtml=[];
			for(var i=0,j=resultArray.length; i<j; i++){
				var myObj=resultArray[i];
				resultHtml.push(
					'<li class="resultList" data-resultid="'+ myObj.id +'">'+
						'<div class="resultIcon"><img src="'+myObj.headimg+'"/></div>'+
						'<div class="resultInfo">'+
							'<div class="title">'+(myObj.name==''?myObj.phone:myObj.name)+'</div>'+
							'<div class="id" style="display:none;">'+myObj.id+'</div>'+
							'<div class="tips">'+myObj.selftips+'</div>'+
						'</div>'+
					'</li>'
				);
			}
			resultHtml=resultHtml.join('');
			$resultBox.append(resultHtml);
			for(i=0,j=resultArray.length;i<j;i++){
				var myResultObj=resultArray[i];
				var $myResultList=$resultBox.find('[data-resultid="'+myResultObj.id+'"]');
				$myResultList[0].resultObj=myResultObj;
			}

			if(root.searchObj.callback!=undefined){
				var $resultList=$resultBox.find('.resultList');
				if(root.searchObj.callback.click!=undefined){
					$resultList.on('click',function(){
						var $this=$(this)
						globalVal.clickTimer && clearTimeout(globalVal.clickTimer);
					    globalVal.clickTimer = setTimeout(function(){
							root.searchObj.callback.click($this[0].resultObj)
					    },300);
					})
				}
				if(root.searchObj.callback.dblclick!=undefined){
					$resultList.on('dblclick',function(){
						globalVal.clickTimer && clearTimeout(globalVal.clickTimer);
						root.searchObj.callback.dblclick($(this)[0].resultObj)
					})
				}
				if(root.searchObj.callback.rclick!=undefined){
					$resultList.on('mousedown',function(e){
						if(e.button==2){
							root.searchObj.callback.rclick($(this)[0].resultObj)
						}
					})
				}
			}


		};
		root.by=function(myKey){
			root.searchData(myKey);
		}
	return root;
}








//联系列表
function contactsList(selector){
	var $selector=$(selector);
	var root={
		contactsListHtml:function(obj){
			var myHtml= '<li class="contactsList" data-contactsid="'+obj.id+'">'+
							'<div class="contactsIcon"><img src="'+obj.imgsrc+'"/>';
					var redNum = obj.rednum;
					if(redNum!='0' && redNum!=undefined){
						myHtml+='<div class="redNum">'+redNum+'</div>';
					}
					myHtml+='</div>'+
							'<div class="contactsInfo">'+
								'<div class="title">'+obj.title+'</div>'+
								'<div class="dateMark">'+obj.datemark+'</div>'+
								'<div class="tips">'+msgCodeReset(obj.tips)+'</div>'+
							'</div>'+
						'</li>';
			return myHtml;
		},
		removeList:function(listId){
			$selector.find('[data-contactsid="'+listId+'"]').remove();
		},
		addList:function(contactsArray){
			var $myContactsList=$selector.find('[data-contactsid]');

			//收集要添加的成员ID
			var addContactsIdObj={};
			for(var i=0,j=contactsArray.length;i<j;i++){
				addContactsIdObj[contactsArray[i].id]=true;
			}

			//过滤重复成员信息
			for(i=0,j=$myContactsList.length;i<j;i++){
				var mycontactsObj=$myContactsList.eq(i)[0].contactsObj;
				if(addContactsIdObj[mycontactsObj.id]==undefined){
					contactsArray.push(mycontactsObj);
				}

			}

			var sortMode=$selector.attr('data-sortmode');
			var contactsListObj={
				'contacts':contactsArray
			}
			if(sortMode!=undefined){
				var sortBy=$selector.attr('data-sortby');
				contactsListObj.sort={
					'mode':Number(sortMode),
					'by':sortBy
				}
			}
			root.creat(contactsListObj);

		},
		checked:function(contactsId){
			$selector.attr('data-listchecked',contactsId);
			$selector.find('.contactsList[data-contactsid="'+contactsId+'"]').addClass('checked').siblings('.contactsList').removeClass('checked');
		},
		creat:function(contactsListObj){

			var contactsArray=contactsListObj.contacts;

			//排序
			if(contactsListObj.sort!=undefined){
				var sortMode=Number(contactsListObj.sort.mode);
				var sortBy=contactsListObj.sort.by;
				$selector.attr({
					'data-sortmode':sortMode,
					'data-sortby':sortBy
				});
				//console.log('contactsArray',contactsArray)
				contactsArray=contactsArray.sort(function (a, b) {
					/*if (a < b) {
					 	return -sortMode;
					}
					else {
					 	return sortMode;
					}*/
					return _localeCompare(a[sortBy],b[sortBy])*sortMode;
					//return a.strcmp(b)*sortMode;
				});
			}else{
				$selector.removeAttr('data-sortmode data-sortby');
			}

			//生成
			var contactsListHtml=[];
			for(var i=0,j=contactsArray.length; i<j; i++){
				contactsListHtml.push(root.contactsListHtml(contactsArray[i]));
			};
			contactsListHtml=contactsListHtml.join('');
			$selector.html('').html(contactsListHtml);
			for(i=0,j=contactsArray.length;i<j;i++){
				var myContactsObj=contactsArray[i];
				var $myContactsList=$selector.find('[data-contactsid="'+myContactsObj.id+'"]');
				$myContactsList[0].contactsObj=myContactsObj;
				if(myContactsObj.callback!=undefined){
					if(myContactsObj.callback.click!=undefined){
						$myContactsList.on('click',function(){
							var $this=$(this);
							globalVal.clickTimer && clearTimeout(globalVal.clickTimer);
						    globalVal.clickTimer = setTimeout(function(){
								$this[0].contactsObj.callback.click($this);
						    },300);
						})

					}
					if(myContactsObj.callback.dblclick!=undefined){
						$myContactsList.on('dblclick',function(){
							var $this=$(this);
							globalVal.clickTimer && clearTimeout(globalVal.clickTimer);
							$(this)[0].contactsObj.callback.dblclick($this);
						})
					}
					if(myContactsObj.callback.rclick!=undefined){
						$myContactsList.on('mousedown',function(e){
							var $this=$(this);
							if(e.button==2){
								$this[0].contactsObj.callback.rclick($this)
							}
						})
					}
				}
			}

			if($selector.attr('data-listchecked')!=undefined){
				$selector.find('.contactsList[data-contactsid="'+$selector.attr('data-listchecked')+'"]').addClass('checked')
			}
			$selector.find('.contactsList').on('click',function(){
				root.checked($(this).attr('data-contactsid'));
				//$selector.attr('data-listchecked',$(this).attr('data-contactsid'));
				//$(this).addClass('checked').siblings('.contactsList').removeClass('checked');
			})


		}

	}
	return root;
}
//增加课程列表
function courseList(selector){
	var $selector=$(selector);
	if($selector[0].courseList==undefined){
		$selector[0].courseList={};
		var root=$selector[0].courseList;
		root.courseListHtml=function(courseList){
			var myHtml= '<li class="courseList" data-dlTimeMark="0" data-courselistid="'+courseList.id+'">'+
							'<div class="courseIcon"><img src="'+courseList.iconsrc+'"/></div>'+
							'<div class="courseInfo">'+
								'<div class="courseTitle">'+courseList.title+'</div>'+
								'<div class="courseProgress">'+
									'<div class="name">'+uiText.text('Progress')+'</div>'+
									'<div class="bar">'+
										'<div class="progressBar" date-type="Progress"></div>'+
									'</div>'+
									'<div class="num"></div>'+
								'</div>'+
								'<div class="courseTips">'+
									'<div class="countdown"></div>'+
									'</div>'+
								'</div>'+
								'<div class="btnBox"></div>'+
						'</li>';

			return myHtml;
		};
		root.writeList=function(courseListArray){
			var courseHtml=[];
			for(var i=0,j=courseListArray.length; i<j; i++){
				courseHtml.push(root.courseListHtml(courseListArray[i]));
			}
			courseHtml=courseHtml.join('');
			$selector.html(courseHtml);
			for(i=0; i<j; i++){
				var $myCourselist=$selector.find('[data-courselistid="'+courseListArray[i].id+'"]');
				root.bindFun($myCourselist,courseListArray[i]);
				$myCourselist[0].infoReset();
			}
			root.checkInterval();
		};
		//修改单个套课课程
		root.setCourse=function(courseList){
			root.bindFun($selector.find('[data-courselistid="'+courseList.id+'"]'),courseList)
			setTimeout(root.courseSort,1500);
		};
		root.bindFun=function($myCourseList,myCourseList){

			$myCourseList[0].infoReset=function(){
				var dlTimeMark=2;//2,无课, 1 课程未开始, 0 课程已开始
				var courseStart=-1;
				var beforeOpenTime=10*60*1000;
				var timeNow=root.serverTime();

				var myCourseArray=myCourseList.courseArray;
				var myCourseAll=myCourseArray.length;
				var timeDL=0;

				var myCourse=myCourseArray[myCourseAll-1];
				var courseHtml_title='<span style="color:#ccc">'+myCourse.title+'</span>';
				var courseHtml_progressBar='<div class="completed" style="width:100%;"></div>';
				var courseHtml_progressNum=myCourseAll+'/'+myCourseAll;
				var courseHtml_dlTime='';
				var courseHtml_btnBox='';


				for(var i=0;i<myCourseAll;i++){
					if(timeNow<myCourseArray[i].end){
						courseStart=i;
						break;
					}
				}
				if(courseStart>=0){
					//还有可以进行的课程
					myCourse=myCourseArray[courseStart];
					courseHtml_title=myCourse.title;
					courseHtml_progressBar='<div class="completed" style="width:'+ Math.floor(100*courseStart/myCourseAll) +'%;"></div>';
					courseHtml_progressNum=courseStart+'/'+myCourseAll;
					if(timeNow<myCourse.start){
						//尚未开课
						dlTimeMark=1;//倒计时排名提升

						var myBeforeStart=myCourse.start-timeNow;
						if(myBeforeStart<beforeOpenTime){
							myBeforeStart=' red';
						}else{
							myBeforeStart=' gray';
						}

						var beforeColor=myCourse.start-timeNow;

						courseHtml_dlTime+='<span class="timeNumber'+myBeforeStart+'">';
						timeDL=myCourse.start;
						//if(new Date(timeDL).Format('YYYY-MM-DD')==new Date(timeNow).Format('YYYY-MM-DD')){
						if((timeDL-timeNow)<beforeOpenTime){
							var myTimeObj=cdtimeShow().dlCalc(timeNow,timeDL);
							courseHtml_dlTime +=uiText.text('Time Left')+' : '+ (myTimeObj.dd==0?'':myTimeObj.dd+'&nbsp;&nbsp;')+
												n20n(myTimeObj.hh)+':'+
												n20n(myTimeObj.mt)+':'+
												n20n(myTimeObj.ss)+'</span>';
						}else{
							courseHtml_dlTime +=uiText.text('Class Time')+' : '+ new Date(timeDL).Format('YYYY'+uiText.text('year_-')+'MM'+uiText.text('month_-')+'DD'+uiText.text('day_ ')+' hh:mm');
							//courseHtml_btnBox = '<a class="btn btn-primary" disabled="disabled" >'+uiText.text('Enter after')+' : '+new Date(timeDL-beforeOpenTime).Format('YYYY'+uiText.text('year_-')+'MM'+uiText.text('month_-')+'DD'+uiText.text('day_ ')+' hh:mm')+'</a>';
						}
					}else{
						//已经开课
						dlTimeMark=0;//倒计时排名提升
						courseHtml_dlTime+='<span class="timeNumber darkblue">';
						timeDL=myCourse.end;

						var myTimeObj=cdtimeShow().dlCalc(myCourse.start,timeNow);
						courseHtml_dlTime +=uiText.text('Class in Session')+' : '+ (myTimeObj.dd==0?'':myTimeObj.dd+'&nbsp;&nbsp;')+
											n20n(myTimeObj.hh)+':'+
											n20n(myTimeObj.mt)+':'+
											n20n(myTimeObj.ss)+'</span>';
					}



				}else{
					timeDL=myCourseArray[myCourseAll-1].end;
				}
				//输出标题
				//$myCourseList.find('.courseTitle').html(courseHtml_title);

				//输出进度条
				$myCourseList.find('.progressBar').html(courseHtml_progressBar);

				//输出进度文字
				$myCourseList.find('.courseProgress').children('.num').html(courseHtml_progressNum);

				//输出倒计时
				$myCourseList.find('.countdown').html(courseHtml_dlTime);

				if(myCourse.btnShow!=undefined){
					var btnHtml='';
					var myCourseBtnArray=myCourse.btnShow;
					var myBtnLen=myCourseBtnArray.length;
					for(var j=0;j<myBtnLen;j++){
						var myCourseBtn=myCourseBtnArray[j]
						if(timeNow>=myCourseBtn.start && timeNow<=myCourseBtn.end){
							btnHtml=myCourseBtn.content;
							break;
						}
					}
					var $btnBoxI=$myCourseList.find('.btnBox');
					/*if(courseHtml_btnBox!=''){
						btnHtml=courseHtml_btnBox;
					}*/
					if($btnBoxI.html()!=btnHtml){
						$btnBoxI.html(btnHtml);
					}
				}


				var oldDlTimeMark=Number($myCourseList.attr('data-dlTimeMark').substr(0,1))
				$myCourseList.attr('data-dlTimeMark',dlTimeMark+'_'+timeDL)

				if( dlTimeMark!=oldDlTimeMark){
					root.courseSort();
				}
			}
		};
		root.courseSort=function(){
			var $courseListArray=$selector.find('.courseList');
			var courseListSortArray=[];
			var sortMode=1;
			for(var i=0,j=$courseListArray.length;i<j;i++){
				var $myCourseList=$courseListArray.eq(i);
				var myCourseListObj={
					'dlTimeMark':$myCourseList.attr('data-dlTimeMark'),
					'courseList':$myCourseList
				}
				courseListSortArray.push(myCourseListObj);
			}
			courseListSortArray = courseListSortArray.sort(function (a, b) {
				a=a.dlTimeMark;
				b=b.dlTimeMark;
				if (isNaN(a) || isNaN(b)){
					//return a.localeCompare(b)*sortMode;
					return _localeCompare(a,b)*sortMode;
				}else{
					return (a - b)*sortMode;
				}
			});
			for(i=0,j=courseListSortArray.length;i<j;i++){
				courseListSortArray[i].courseList.appendTo($selector);
			}
		};
		root.checkInterval=function(){
			if(globalVal.courseCheckInterval==undefined){
				$selector.attr('data-courseCheckInterval','true');
				globalVal.courseCheckInterval=setInterval(function(){
					if($selector.length<=0 || $selector.find('[data-courselistid]').length<=0){
						clearInterval(globalVal.courseCheckInterval);
						globalVal.courseCheckInterval=null;
						delete globalVal.courseCheckInterval;
					}
					var $courseList=$selector.find('li.courseList')
					$courseList.each(function(){
						$(this)[0].infoReset()
					});
				},1000);
			}
		};
		root.btnShow=function(ctrlArray){
			if(!isArray(ctrlArray)){
				ctrlArray = Array.prototype.slice.call(arguments);
			};
			var $courseList=$selector.find('li.courseList');
			for(var i=0,j=ctrlArray.length;i<j;i++){
				var myCtrl=ctrlArray[i];
				$selector.find('[data-courselistid="'+myCtrl.id+'"]').find('.btnBox').html(myCtrl.content);
			};

			return root;
		};
		root.serverTime=function(){
			var time=(dateNow()).getTime()+root.jetlag;
			return time
		};
		root.creat=function(courseListArray,jetlag){
			if(jetlag==undefined){
				jetlag=0;
			}
			root.jetlag=jetlag;
			root.writeList(courseListArray);

			return root;
		}
	}
	return $selector[0].courseList;
}
//
//
//
//请求消息列表
function addRequestList(selector){
	var root={
		returnListHtml:function(myInfoObj){
			var myHtml= '<li class="requestList" data-requestid="'+myInfoObj.id+'">'+
						'<div class="requestIcon"><img src="'+myInfoObj.imgsrc+'"/></div>'+
						'<div class="requestInfo">'+
						'<div class="requestTitle">'+myInfoObj.title+'</div>'+
						'<div class="requestTips">'+myInfoObj.tips+'</div>'+
						'</div>'+
						'<div class="requestConfirm">'+myInfoObj.confirm+'</div>'+
						'</li>';
			return myHtml;
		},
		removeSameList:function(sameId){
			var $selector=$(selector);
			$selector.find('[data-requestid="'+sameId+'"]').remove();
		},
		writeList:function(requestsArray){
			var $selector=$(selector);
			var requestsHtml='';
			var oldIdList={};
			for(var j=0,onl=$selector.children('.requestList').length; j<onl; j++){
				var myid=$selector.children('.requestList').eq(j).attr('data-requestid');
				oldIdList[myid]=myid;
			}
			for(var i=0,nl=requestsArray.length; i<nl; i++){
				var myid=oldIdList[requestsArray[i].id];
				if(myid!=undefined){
					root.removeSameList(myid);
				}
				requestsHtml=root.returnListHtml(requestsArray[i])+requestsHtml;
			}
			$selector.prepend(requestsHtml);
		},
		start:function(requestsArray){
			if(!isArray(requestsArray)){
				requestsArray = Array.prototype.slice.call(arguments);
			};
			root.writeList(requestsArray);
		}
	}
	return root;
}
//radio checkbox
function inputPoint(){
	//为label追加icon的div
	function addIcon(){
		$('.radio').prepend('<div class="icon"></div>');
		$('.checkbox').prepend('<div class="icon"></div>');
	}
	//addIcon();
	//遍历radio状态并修改相应样式
	function radioPress(){
		var selector=$('.radio');
		for(var i=0; i<selector.length; i++){
			if(selector.eq(i).children('input[type="radio"]').prop('checked')){
				selector.eq(i).addClass('checked');
			}else{
				selector.eq(i).removeClass('checked');
			}
		}
	}
	//在点击后触发上述操作
	$('body').on('click','.radio>input[type="radio"]',function(){
		radioPress();
	})
	//初始化遍历radio
	radioPress();


	//遍历checkbox状态并修改相应样式
	function checkboxPress(){
		var selector=$('.checkbox');
		for(var i=0; i<selector.length; i++){
			if(selector.eq(i).children('input[type="checkbox"]').prop('checked')){
				selector.eq(i).addClass('checked');
			}else{
				selector.eq(i).removeClass('checked');
			}
		}
	}
	//在点击后触发上述操作
	$('body').on('click','.checkbox>input[type="checkbox"]',function(){
		checkboxPress();
	})
	//初始化遍历checkbox
	checkboxPress();
}
function selectBox(selector){
	var root={
		creat:function(){
			var $selectors=$(selector);
			for(var i=0,j=$selectors.length; i<j; i++){
				var $select=$selectors.eq(i);
				root.write($select).start();
			}
		},//find end
		write:function($select){
			if($select[0].selectBox==undefined){
				$select[0].selectBox={};
				var w_root=$select[0].selectBox;
				w_root.start=function(){
					//初始化selected
					if($select.children('[selected]').length<1){
						$select.children('option').eq(0).attr('selected',true);
					}

					//建立一个空的字符串变量用于存放要生成的html代码
					var selectBoxHtml = '<span class="selectBox" >'+
											'<div class="arrow"></div>';

						//根据selectSN来设定selectedBox来决定selectBox默认显示的值
						selectBoxHtml+= 	'<span class="selectedBox">'+
												$select.children('option[selected]').html()+
											'</span>'+
										'</span>';

					//为源select后面加入模拟div的html,添加标记,隐藏
					$select.after(selectBoxHtml).hide();
					w_root.selectBox=$select.next('.selectBox');
					w_root.selectBox.attr('style',$select.attr('data-style')).on('click','.selectedBox,.arrow',function(){
						w_root.option().show();
					})
				};
				w_root.option=function(){
					var o_root={
						show:function(){
							var $selectBox=w_root.selectBox;
							var optionLen=$select.children('option').length;

							//为selectBox插入opitonBox
							var optionBoxHtml = '<div class="optionBoxBg"></div>'+
												'<div class="optionBox">'+
													'<ul class="optionListBox">';

								//遍历源select的所有option
								for(var i=0; i<optionLen; i++){
									var myOption=$select.children('option').eq(i);
									//为opitonBox插入optionList
									optionBoxHtml+='<li class="optionList" data-value="'+myOption.val()+'" >'+myOption.html()+'</li>';
								}

								optionBoxHtml+= 	'</ul>'+
												'</div>';
								optionBoxHtml+='<div class="optionTopWhite"></div>';
							//之所以不是放到body底部是因为滚动条会导致option无法和select对齐
							$selectBox.append(optionBoxHtml);


							//默认列数
							var optionCol=1;
							if($select.attr('data-col')!=undefined){
								optionCol=$select.attr('data-col');
							}
							//最大显示行数
							var optionRowShow=6;
							if($select.attr('data-row')!=undefined){
								optionRowShow=$select.attr('data-row');
							}
							//默认行数
							var optionRow=Math.ceil(optionLen/optionCol);


							var selectBox_w=$selectBox.outerWidth();
							var selectBox_h=$selectBox.outerHeight();

							var optionBox=$selectBox.children('.optionBox');
							var optionListBox=optionBox.children('.optionListBox');
							var optionList=optionListBox.children('.optionList');

							var optionList_w=selectBox_w-2;
							var optionBox_w=optionList_w*optionCol+2;

							if(optionRow>optionRowShow){
								var optionList_h=optionList.eq(0).outerHeight();
								var optionBox_h=optionList_h*optionRowShow+'px';
								if(optionCol>1){
									optionBox_w+=18;
								}
							}else{
								var optionBox_h='';
							}
							//定义optionBox的宽高位置
							optionBox.css({
								'width':optionBox_w+'px',
								'height':optionBox_h,
								'top':(selectBox_h-2)+'px'
							});
							optionListBox.css({
								'width':(optionBox_w)+'px'
							});
							optionList.css({
								'width':optionList_w+'px',
								'float':'left'
							});
							$('.optionTopWhite').css({
								'width':(selectBox_w-2)+'px',
								'top':(selectBox_h-2)+'px'
							});
							optionList.one('click',function(){
								//获取自身所处排序的顺序
								var $this=$(this)
								var o_sn=optionList.index($this);
								//修改源select的选中状态

								if($select.children('option').index($select.children('option[selected]'))!=o_sn){
									$select.children('option').eq(o_sn).attr('selected',true).siblings('option').removeAttr('selected');
									$select.val($this.attr('data-value'));
									$selectBox.children('.selectedBox').html($this.html());
									$select.change();
								}
								o_root.remove();

							});
							//为selectBg绑定click,让其在鼠标点击其他位置隐藏optionBox
							$selectBox.children('.optionBoxBg').on('click',function(){
								o_root.remove();
							})
						},
						remove:function(){
							w_root.selectBox.children('.optionBoxBg').remove();
							w_root.selectBox.children('.optionBox').remove();
							w_root.selectBox.children('.optionTopWhite').remove();
						}
					}
					return o_root;
				};//option end
				w_root.val=function(v){
					$select.children('option[value="'+v+'"]').attr('selected',true).siblings().removeAttr('selected');;
					$select.val(v);
					w_root.selectBox.children('.selectedBox').html($select.children('[selected]').html());
				}
				return w_root;
			}// if end
			return $select[0].selectBox;
		},//creat end
		val:function(v){
			var $selectors=$(selector);
			for(var i=0,j=$selectors.length; i<j; i++){
				var $select=$selectors.eq(i);
				$select[0].selectBox.val(v);
			}
		}
	}
	return root;
}
//文本框提示
function inputTips(inputID){
	$('body').on('focus',inputID,function(){
		var $this=$(this);
		if($this.value()==''){
			$this.val('');
			$this.addClass('focus');
			pwdTipsCtrl($this,'hide');
		}
	})
	$('body').on('blur',inputID,function(){
		var $this=$(this);
		if($this.value()!=''){
		 	$this.addClass('focus');
			pwdTipsCtrl($this,'hide');
		}else{
			formClear($this);
		}
	})
}
function pwdTipsCtrl($this,mode){
	var $thisPrev=$this.prev();
	if($this.attr('type')!='password'){
		return false;
	}
	if($thisPrev.hasClass('inputTips')){
		switch (mode){
			case 'show':
				$thisPrev.css({
					'color':''
				})
				break;
			case 'hide':
				$thisPrev.css({
					'color':'transparent'
				})
				break;
		}

	}
}
//清空文本框内容
function formClear(target){
	var $target=$(target)
	if($target.is('input,textarea')){
		var $input=$target;
	}else{
		var $input=$target.find('input[type="text"],input[type="password"],textarea');
	}
	$input.each(function(){
		var $this=$(this);

		var mydefaultValue=$this.attr('data-tips');
		if(mydefaultValue==undefined){
			mydefaultValue='';
		}
		$this.val(mydefaultValue);

		$this.removeClass('focus');
		pwdTipsCtrl($this,'show');
	})
}
function inputHasVal(inputDom){
	var $this=$(inputDom);
	var myVal=$this.val();
	var defaultValue=$this.attr('data-tips');
	if(myVal=='' || myVal==defaultValue){
		return false;
	}else{
		return true;
	}
}




//tab选项卡
function TabBox(){
	$('body').on('click','.tabBox .tabBar .tab',function(){
		var $this=$(this);
		var $tabBox=$this.closest('.tabBox');
		var $tabBar=$this.parent('.tabBar');
		var $tabCon=$tabBar.siblings('.tabCon')
		var sn=$tabBar.children('.tab').index($this);
		$this.addClass('checked').siblings('.tab').removeClass('checked');
		$tabCon.children('.con:eq('+sn+')').addClass('checked').siblings('.con').removeClass('checked');
	})
}



//弹出框
function PopUpBox (config){
	/*read me
	 *
	 *
	 *
	 *




	id: "myAlertTest" //string popupboxid  undefined为时间戳
	type: "alert"//类型: alert confirm form tips mark wait
	bgShow: true //显示背景遮蔽
	bgClose: true//true 可以点击背景关闭
	callback: {//交互回调  e为new PopUpBox自身
		yes:function(e){},//确定
		no:function(e){},//取消
		close:function(e){},//关闭
		clickBox:function(e){}//点击popupbox
	}
	closeBtn: true//true 显示关闭按钮
	position: {//定位  只定义宽高会让popupbox居中
		width:
		height:
		left:
		top:
		margin-left:
		margin-top:
	}
	title: "aaal"//string 标题
	content: "测试一个alert"//string 内容
	submitBtn: "alert"//string 提交按钮类型  默认 alert 单个按钮,  submit  多个按钮, false 隐藏按钮
	submitBtnText: {//按钮文字
		yes://确定按钮文字
		no://取消按钮文字
	}
	show:false//是否new popupbox之后立即显示






	*/





	//config=(c==undefined?{}:c);
	if(config==undefined){
		config={};
	}
	//设置config未定义的值
	function setConfig(type,value){
		function setting(t,v){
			var rootParent=this;
			for(var i=0,len=t.length;i<len;i++){
				if(i<len-1){
					if(rootParent[''+t[i]]==undefined){
						rootParent[''+t[i]]={};
					}
					rootParent=rootParent[''+t[i]];
				}else{
					if(rootParent[''+t[i]]==undefined){
						rootParent[''+t[i]]=v;
					}
				}
			}
		}
		setting.call(config,type,value);
	}
	var root=this;


	//写入弹出框
	var myPopUpBox=document.createElement('div');
	root.myPopUpBox=myPopUpBox;

	myPopUpBox.className='popUpWin'+' '+config.type;
	myPopUpBox.setAttribute('tabindex',0);


	if(config.style!=undefined){
		if(typeof(config.style)=='string'){
			myPopUpBox.className+=' '+config.style
		}else{
			$(myPopUpBox).css(config.style);
		}
	}


	setConfig(['type'],'alert');
	setConfig(['submitBtnText','yes'],'确定');
	setConfig(['submitBtnText','no'],'取消');
	switch (config.type){
		case 'alert':
			setConfig(['title'],'提示');
			setConfig(['bgShow'],true);
			setConfig(['bgClose'],true);
			setConfig(['closeBtn'],true);
			setConfig(['submitBtn'],'alert');
			break;
		case 'confirm':
			setConfig(['title'],'提示');
			setConfig(['bgShow'],true);
			setConfig(['bgClose'],true);
			setConfig(['closeBtn'],true);
			setConfig(['submitBtn'],'confirm');
			break;
		case 'html':
			setConfig(['title'],'提示');
			setConfig(['bgShow'],true);
			setConfig(['bgClose'],true);
			setConfig(['closeBtn'],true);
			setConfig(['submitBtn'],'confirm');
			break;
		case 'tips':
			setConfig(['bgShow'],false);
			setConfig(['bgClose'],false);
			setConfig(['closeBtn'],false);
			setConfig(['submitBtn'],false);
			break;
		case 'mark':
			setConfig(['bgShow'],false);
			setConfig(['bgClose'],false);
			setConfig(['closeBtn'],false);
			setConfig(['submitBtn'],false);
			break;
		case 'menu':
			setConfig(['title'],'');
			setConfig(['bgShow'],false);
			setConfig(['bgClose'],true);
			setConfig(['closeBtn'],false);
			setConfig(['submitBtn'],false);
			break;
		case 'wait':
			setConfig(['bgShow'],true);
			setConfig(['bgClose'],false);
			setConfig(['closeBtn'],false);
			var loadingHtml='';
			for(var i=0;i<12;i++){
				loadingHtml+='<div class="element e'+(i+1)+'"><div class="line"></div></div>'
			}
			setConfig(['content'],'<div class="icon_load">'+loadingHtml+'</div>');
			setConfig(['submitBtn'],false);
			break;
	}

	//判断是否显示背景层
	if(config.bgShow){
		var myPopUpBoxBg=document.createElement('div');
		myPopUpBoxBg.className='popUpBoxBg';
		myPopUpBox.appendChild(myPopUpBoxBg);
	}

	//如若隐藏背景还可点击他处关闭，则阻止自身对于body的点击冒泡事件
	if(!config.bgShow && config.bgClose){
		$(myPopUpBox).on('click',function(e){
			e.stopPropagation();
		})
	}


	//创建前景层
	var myPopUpBoxFg=document.createElement('div');
	myPopUpBoxFg.className='popUpBoxFg';
	myPopUpBox.appendChild(myPopUpBoxFg);



	//追加title
	if(config.title!=undefined && config.title!=''){
		var myPopUpBoxTitle=document.createElement('div');
		myPopUpBoxTitle.className='popUpBoxTitle';
		myPopUpBoxTitle.innerHTML=config.title;
		myPopUpBoxFg.appendChild(myPopUpBoxTitle);
		root.title=myPopUpBoxTitle;
	}



	//追加content
	if(config.content!=undefined){
		var myPopUpBoxContent=document.createElement('div');
		myPopUpBoxContent.className='popUpBoxContent';

		switch(config.type){
			case 'html':
				if(config.content){
					$(myPopUpBoxContent).load(config.content,function(res,status,xhr){});
				}
				break;
			case 'menu':
				var menuBtn=function (btnArr){
					var menuBtnBox=document.createElement('ul');
					menuBtnBox.className='menuBtnBox';
					for(var i=0,len=btnArr.length;i<len;i++){
						(function(){
							var btnObjI=btnArr[i];
							var myBtn=document.createElement('li');
							myBtn.className='menuBtn';
							myBtn.setAttribute('data-sn',i);
							myBtn.innerHTML=btnObjI.text;
							$(myBtn).on('click',function(e){
								if(btnObjI.callback(root,this,e)!=false){
									root.close();
								}
							});
							menuBtnBox.appendChild(myBtn);
						})()
					}
					myPopUpBoxContent.appendChild(menuBtnBox);
				}
				if(typeof config.content=='string'){
					myPopUpBoxContent.innerHTML='<div class="menuTipsBox"><div class="menuTips">'+config.content+'</div></div>';
				}else{
					menuBtn(config.content);
				}

				var myArrow=document.createElement('div');
				myArrow.className='menuArrow';
				myPopUpBoxContent.appendChild(myArrow);

				break;
			default:
				myPopUpBoxContent.innerHTML=config.content;
				break;
		}

		myPopUpBoxFg.appendChild(myPopUpBoxContent);
		root.content=myPopUpBoxContent;
	}


	var clickSubmitYes=function(){
		if(config.callback!=undefined && config.callback.yes!=undefined){
			if(config.callback.yes(root)!=false){
				root.close();
			};
		}else{
			root.close();
		};
	}
	$(myPopUpBoxContent).on('click','.btn.yes',clickSubmitYes);


	var clickSubmitNo=function(){
		if(config.callback!=undefined && config.callback.no!=undefined){
			if(config.callback.no(root)!=false){
				root.close();
			};
		}else{
			root.close();
		};
	}
	$(myPopUpBoxContent).on('click','.btn.no',clickSubmitNo);


	if(config.submitBtn!=false){
		var mySubmitBtnBox=document.createElement('div');
		mySubmitBtnBox.className='submitBtnBox'+' '+config.submitBtn;

		var myBtnYesCol=document.createElement('div');
		myBtnYesCol.className='col';

		var myBtnYes=document.createElement('span');
		root.myBtnYes=myBtnYes;
		myBtnYes.className='btn yes';
		myBtnYes.innerHTML=config.submitBtnText.yes;

		myBtnYesCol.appendChild(myBtnYes);
		mySubmitBtnBox.appendChild(myBtnYesCol);

		$(myBtnYes).on('click',clickSubmitYes);

		//取消按钮及回调
		if(config.submitBtn=='confirm'){
			var myBtnNoCol=document.createElement('div');
			myBtnNoCol.className='col';

			var myBtnNo=document.createElement('span');
			root.myBtnNo=myBtnNo;
			myBtnNo.className='btn no';
			myBtnNo.innerHTML=config.submitBtnText.no;

			myBtnNoCol.appendChild(myBtnNo);
			mySubmitBtnBox.appendChild(myBtnNoCol);


			$(myBtnNo).on('click',clickSubmitNo);
		}
		myPopUpBoxFg.appendChild(mySubmitBtnBox);
	}


	//追加close按钮
	if(config.closeBtn){
		var myCloseBtn=document.createElement('input');
		myCloseBtn.type='button';
		myCloseBtn.value='×';
		myCloseBtn.className='close';
		myPopUpBoxFg.appendChild(myCloseBtn);
		$(myCloseBtn).on('click',function(){
			root.close();
		});
	}


	//窗口点击回调
	if(config.callback!=undefined && config.callback.clickBox!=undefined){
		$(myPopUpBox).on('click',function(){
			config.callback.clickBox(root);
		},false);
	}



	var setPosition=function (){
		var $myShowBox;
		if(config.bgShow){
			$(myPopUpBox).css({
			    'width': '100%',
			    'height': '100%',
			    'left': 0,
			    'top': 0
			});
			$(myPopUpBoxFg).css({
			    'position': 'absolute'
			});
			$myShowBox=$(myPopUpBoxFg);
		}else{
			$myShowBox=$(myPopUpBox);
		}
		if(config.position==undefined){
			$myShowBox.css({
				//'position':'absolute',
				'left':'50%',
				'top':'50%',
				'margin-left':-$myShowBox.outerWidth()/2,
				'margin-top':-$myShowBox.outerHeight()/2
			})
		}else{
			$myShowBox.css(config.position);
			if(config.position.left==undefined && config.position.top==undefined){
				$myShowBox.css({
					//'position':'absolute',
					'left':'50%',
					'top':'50%',
					'margin-left':-$myShowBox.outerWidth()/2,
					'margin-top':-$myShowBox.outerHeight()/2
				})
			}else{
				$myShowBox.css({
					//'position':'absolute'
				})
			}
		}
	}
	root.setPosition=setPosition;
	root.isShow=false;
	root.closeTimeout={
		start:function(){
			if(config.time){
				clearTimeout(root.closeTimeout.value);
				root.closeTimeout.value=setTimeout(function(){
					root.close();
				},config.time);
			};
		},
		clear:function(){
			if(config.time){
				clearTimeout(root.closeTimeout.value);
			};
		}
	};
	root.show=function(obj,popType){
		root.closeTimeout.start();
		if(root.isShow){
			return true;
		}else{
			root.isShow=true;
		}
		$('html').css({'overflow': 'hidden'})
		if(config.id){
			myPopUpBox.id=config.id;
		}else{
			myPopUpBox.id='popUpBox_'+(new Date).getTime();
		}
		document.body.appendChild(myPopUpBox);
		setPosition();

		if(config.callback!=undefined && config.callback.showAfter!=undefined){
			config.callback.showAfter(root);
		}

		//如若定时，则设置定时关闭
		//myPopUpBox.focus();

		//如若可以点击背景关闭
		if(config.bgClose){
			if(config.bgShow){//如果背景存在则针对背景绑定事件
				$(myPopUpBoxBg).one('click',function(){
					root.close();
				});
			}else{//否则则针对body绑定关闭事件
				$(myPopUpBox).on('mousedown',function(e){
					e.stopPropagation();
				})
				$('body').on('mousedown',function bodyClick(e){
					if(root.isShow){
						root.close();
						$('body').unbind('mousedown',bodyClick)
					}
				});

			}

		}
	}
	root.close=function (){
		if(!root.isShow){
			return false;
		}else{
			root.isShow=false;
		}
		if(config.callback!=undefined && config.callback.close!=undefined){
			config.callback.close(root);
		}
		if(document.getElementById(myPopUpBox.id)!=null){
			document.body.removeChild(myPopUpBox);
		}
		if($('.popUpBox').length<1){
			$('html').css({'overflow': ''});
		}
		root.closeTimeout.clear();
	};
	if(config.keyClose){
		$(myPopUpBox).on('keyup',function(e){
			if(e.keyCode==27){
				root.close();
			}
		})
	}
	if(config.keySubmit){
		$(myPopUpBox).on('keyup',function(e){
			if(e.keyCode==13){
				clickSubmitYes();
			}
		})
	}
	config.show && root.show();
}

function _Tips(str){
	var myTips=new PopUpBox({
		'type':'tips',
		'style':'black',
		'show':true,
		'content':str,
		'time':3000
	});
}
function _Alert(tt,ct,cb){
	addStudent=new PopUpBox({
		'type':'alert',
		'show':true,
		'style':'white',
		'title':tt,
		'content':ct,
		'submitBtnText':{
			'yes':'确定'
		},
		'callback':{
			'yes':function(){
				if(cb){
					cb();
				}
			}
		},
		'position':{
			'width':400
		}
	});
}
var pageLoading=new PopUpBox({
		type:'wait',
		style:'fullscreen white'
	}
)
//
//
//
//图片加载监控
function reloadImg(img,interval){
	var $this=$(img);
	!interval && (interval=3000);
	var times= $this.attr('data-times')?$this.attr('data-times'):0;
	var src=$this.attr('src').replace(/\?.*/,'');//如果存在参数，则去掉参数
	var imgSrc_loading='images/imgloading.gif';
	var imgSrc_loadfailed='images/imgloadfailed.png';
	var timesMax=10;
	$this.addClass('reloading');
	if(times<timesMax){
		$this.attr('src',imgSrc_loading);
		setTimeout(function(){
			times++;
			$this.attr('data-times',times);
			src=src+'?'+(dateNow()).getTime();
			$this.attr('src',src);
		},interval)
	}else{
		$this.addClass('loadFailed');
		$this.attr('src',imgSrc_loadfailed);
	}
}
//
//
//
//
//弹出框旧版
var popUpBox=function (selector){
	var myPopUpBoxOld={
		tips:function(obj){
			var myPopUpBox=new PopUpBox({
				'type':'tips',
				'style':obj.boxClass,
				'bgShow':obj.bgShow,
				'show':true,
				'content':obj.content,
				'time':obj.time?obj.time:2000,
				'callback':{
					'yes':obj.callback?obj.callback:undefined,
					'close':obj.close?obj.close:undefined
				}
			})
		},
		alert:function(obj){
			var myPopUpBox=new PopUpBox({
				'type':'alert',
				'style':obj.boxClass,
				'show':true,
				'bgShow':obj.bgShow,
				'title':obj.title,
				'content':obj.content,
				'submitBtnText':{
					'yes':obj.uiText && obj.uiText.btnYes?obj.uiText.btnYes:undefined,
					'no':obj.uiText && obj.uiText.btnNo?obj.uiText.btnNo:undefined
				},
				'callback':{
					'yes':obj.callback?obj.callback:undefined,
					'close':obj.close?obj.close:undefined
				}
			})
		},
		form:function(obj){
			var myPopUpBox=new PopUpBox({
				'type':'confirm',
				'style':obj.boxClass,
				'show':true,
				'bgShow':obj.bgShow,
				'bgClose':false,
				'submitBtn':obj.submitBtn,
				'title':obj.title==undefined?'':obj.title,
				'content':obj.content,
				'submitBtnText':{
					'yes':obj.uiText && obj.uiText.btnYes?obj.uiText.btnYes:undefined,
					'no':obj.uiText && obj.uiText.btnNo?obj.uiText.btnNo:undefined
				},
				'callback':{
					'yes':obj.callback?obj.callback:undefined,
					'close':obj.close?obj.close:undefined
				},
				'position':obj.position?obj.position:undefined
			})
		},
		mark:function(obj){
			var myClientRect=obj.target[0].getBoundingClientRect();
			//console.log(obj.content);
			var myPopUpBox=new PopUpBox({
				'type':'menu',
				'style':'arrowDown black_alpha80',
				'show':true,
				'content':obj.content,
				'position':{
					'left':myClientRect.left,
					'top':myClientRect.top
				}
			})
		}
	}
	return myPopUpBoxOld;
}
//修改签名失败
function blurSaveFalse(inputID,tipsText){
	$this=$(inputID);
	popUpBox().tips({
		content:tipsText
	}).setTipsSize({
//		left:$this.offset().left+$this.width(),
//		top:$this.offset().top+10,
//		'background-color':'transparent',
		color:'#f00'
	})
}
//修改签名成功
function blurSaveTrue(inputID,tipsText){
	var $this=$(inputID);
	popUpBox().tips({
		content:tipsText
	}).setTipsSize({
//		left:$this.offset().left+$this.width(),
//		top:$this.offset().top+10,
//		'background-color':'transparent',
		color:'#0f0'
	})
	$this.css({
		'border-color':''
	})
}

//按钮倒计时
function btnCD (selector){
	var $selector=$(selector);
	if($selector[0].btnCD==undefined){
		$selector[0].btnCD={};
		var root=$selector[0].btnCD;
		root.btn=$selector;
		root.locktime=0;
		root.lock=function(lockMaxTime,callback){
			root.lockMaxTime=lockMaxTime;
			if(root.btn.is('button')){
				root.dVal=root.btn.html();
			}else{
				root.dVal=root.btn.val();
			}
			if(callback){
				root.unlockfun=callback;
			}
			root.btn.attr('disabled',true);
			root.lockcd();
			root.timer=setInterval(function(){
				root.lockcd();
			},1000);
		}
		root.reText=function(text){
			if(root.btn.is('button')){
				root.btn.html(text);
			}else{
				root.btn.val(text);
			}
		}
		root.lockcd=function(t){
			if(t!=undefined){
				root.lockMaxTime=0;
			}
			if(root.lockMaxTime>0){
				root.reText(root.dVal+' ('+root.lockMaxTime+')');
				root.lockMaxTime--;
			}else{
				root.reText(root.dVal);
				root.btn.attr('disabled',false);
				clearInterval(root.timer);
				if(root.unlockfun){
					root.unlockfun();
				}
			}
		}

	}
	return $selector[0].btnCD;
}
function DisableImageDrag(){
	$('body').on('dragstart','img',function(ev){
		ev.preventDefault();
	})
}
function ScoreStar(changeCallback){
	$('body').on('click','.scoreStar.edit .star',function(){
		var $this=$(this);
		var $scoreStar=$this.parent('.scoreStar');
		var myEq=$scoreStar.children('.star').index($this);
		var myVal=myEq+1;
		$scoreStar.children('li.val').children('input').val(myVal)
		$scoreStar.removeClass('check1 check2 check3 check4 check5');
		$scoreStar.addClass('check'+(myVal));
		if(changeCallback){
			changeCallback($scoreStar,myVal);
		}
	})
}



function resetViewport(webWidth){
	var myDefaultWidth=$(window).width();
	if(screen.width<screen.height){
		var msw=screen.width;
		var msh=screen.height;
	}else{
		var msw=screen.height;
		var msh=screen.width;
	}

	if($(window).width()>$(window).height()){
		var mrh=myDefaultWidth;
		var mrw=mrh*msw/msh;
	}else{
		var mrw=myDefaultWidth;
		var mrh=mrw*msh/msw;

	}
	function setViewport(){
		var myWidth;
		if($(window).width()>$(window).height()){
			//alert('横屏');
			//myWidth=mrh;//采用横屏宽度
			myWidth=mrw;//采用竖屏宽度的80%以解决屏幕高度不够的问题,建议页面使用自适应来提高体验
		}else{
			//alert('竖屏');
			myWidth=mrw;
		}
		var myscale=myWidth/webWidth;
		$('meta[name="viewport"]').attr('content','width='+webWidth+'.1, initial-scale='+myscale+', minimum-scale='+myscale+', maximum-scale='+myscale)
	}

	setViewport();
	function orientationChange() {
		setTimeout(function(){
			setViewport()
		},500)
	}
	//window.onorientationchange = orientationChange;
}
resetViewport(320);

$(document).ready(function(){

	inputTips('input[data-tips]');
	inputPoint();
	selectBox('select').creat();
	DisableImageDrag();
	TabBox();

	//禁止拖拽
	document.body.addEventListener('dragstart', function (e) {
	   e.preventDefault();
	}, false);
})
