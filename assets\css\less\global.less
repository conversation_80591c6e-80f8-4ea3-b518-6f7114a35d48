*{
    outline:0;
    box-sizing: border-box;
    -webkit-user-select: none;
    -webkit-appearance: none;//remove ios style
    -webkit-tap-highlight-color: transparent;//remove tap black color
    cursor: default;
}

*::-webkit-scrollbar {
	width: 8px;
	height: 8px;
	background-color: transparent;
	right: 5px;
}

*::-webkit-scrollbar-track{
	background-color: transparent;
}
*::-webkit-scrollbar-thumb {
	background-color: rgba(145,145,155,0);
	background-clip: content-box;
	border:10px solid transparent;
	border-radius: 999px;
	-webkit-transition:.2s;
}
*:hover::-webkit-scrollbar-thumb {
	background-color: rgba(145,145,155,255);
	border:1px solid transparent;
}

//*::-webkit-scrollbar-corner {
//background: transparent;
//}


body,html {
    margin: 0;
    //background-color: #fff;
	background-color: transparent;
    font-family:@base-font-family;
    font-size: @base-font-size;
    line-height: 1.5;
    color: @base-font-color;

    height: 100%;
    min-height: 100%;
//  -webkit-tap-highlight-color:rgba(0,0,0,0);  //取消移动设备点击效果
//  -webkit-font-smoothing:antialiased; //苹果设备平滑字体
}
input,textarea,pre{
    font-family:@base-font-family;
    resize: none;
    -webkit-user-select:auto;
}
//图片禁止拖动
img {
	-webkit-user-select: none;
}
ul, li, dl, dt, dd, p, h1, h2, h3, h4, h5, h6 {
    margin: 0;
    padding: 0;
    list-style: none;
    display: block;
}
p{
    padding-top: 5px;
    padding-bottom: 10px;
}
a {
    color: @base-font-color;
    cursor:pointer;
}
a {
    text-decoration:none;
}
a:hover{
    text-decoration: underline;
}
img{
    border:0;
}
.radius-left{
	border-top-right-radius:0 !important;
	border-bottom-right-radius:0 !important;
}
.radius-right{
	border-top-left-radius:0 !important;
	border-bottom-left-radius:0 !important;
}
