androidx.activity:activity-compose:1.9.0
androidx.activity:activity-ktx:1.9.0
androidx.activity:activity:1.9.0
androidx.annotation:annotation-experimental:1.4.1
androidx.annotation:annotation-jvm:1.9.1
androidx.appcompat:appcompat-resources:1.6.1
androidx.appcompat:appcompat:1.6.1
androidx.arch.core:core-common:2.2.0
androidx.arch.core:core-runtime:2.2.0
androidx.asynclayoutinflater:asynclayoutinflater:1.0.0
androidx.autofill:autofill:1.1.0
androidx.biometric:biometric-ktx:1.2.0-alpha03
androidx.biometric:biometric:1.2.0-alpha03
androidx.browser:browser:1.5.0
androidx.cardview:cardview:1.0.0
androidx.collection:collection-jvm:1.4.4
androidx.collection:collection-ktx:1.4.4
androidx.compose.animation:animation-android:1.7.4
androidx.compose.animation:animation-core-android:1.7.4
androidx.compose.foundation:foundation-android:1.7.4
androidx.compose.foundation:foundation-layout-android:1.7.4
androidx.compose.material3:material3-android:1.3.0
androidx.compose.material3:material3-window-size-class-android:1.3.0
androidx.compose.material:material-android:1.7.4
androidx.compose.material:material-icons-core-android:1.7.4
androidx.compose.material:material-icons-extended-android:1.7.4
androidx.compose.material:material-navigation:1.7.0-beta01
androidx.compose.material:material-ripple-android:1.7.4
androidx.compose.runtime:runtime-android:1.7.4
androidx.compose.runtime:runtime-livedata:1.7.4
androidx.compose.runtime:runtime-saveable-android:1.7.4
androidx.compose.ui:ui-android:1.7.4
androidx.compose.ui:ui-geometry-android:1.7.4
androidx.compose.ui:ui-graphics-android:1.7.4
androidx.compose.ui:ui-text-android:1.7.4
androidx.compose.ui:ui-tooling-android:1.7.4
androidx.compose.ui:ui-tooling-data-android:1.7.4
androidx.compose.ui:ui-tooling-preview-android:1.7.4
androidx.compose.ui:ui-unit-android:1.7.4
androidx.compose.ui:ui-util-android:1.7.4
androidx.compose.ui:ui-viewbinding:1.7.4
androidx.concurrent:concurrent-futures:1.1.0
androidx.constraintlayout:constraintlayout-compose:1.0.1
androidx.constraintlayout:constraintlayout-core:1.0.4
androidx.constraintlayout:constraintlayout:2.1.4
androidx.coordinatorlayout:coordinatorlayout:1.2.0
androidx.core:core-ktx:1.13.1
androidx.core:core-splashscreen:1.0.0
androidx.core:core:1.13.1
androidx.cursoradapter:cursoradapter:1.0.0
androidx.customview:customview-poolingcontainer:1.0.0
androidx.customview:customview:1.1.0
androidx.databinding:databinding-adapters:7.1.0
androidx.databinding:databinding-common:8.0.0
androidx.databinding:databinding-ktx:7.1.0
androidx.databinding:databinding-runtime:8.0.0
androidx.databinding:viewbinding:8.4.2
androidx.datastore:datastore-core:1.0.0
androidx.datastore:datastore-preferences-core:1.0.0
androidx.datastore:datastore-preferences:1.0.0
androidx.datastore:datastore:1.0.0
androidx.documentfile:documentfile:1.0.0
androidx.drawerlayout:drawerlayout:1.1.1
androidx.dynamicanimation:dynamicanimation:1.0.0
androidx.emoji2:emoji2-views-helper:1.3.0
androidx.emoji2:emoji2:1.3.0
androidx.exifinterface:exifinterface:1.3.7
androidx.fragment:fragment-ktx:1.6.2
androidx.fragment:fragment:1.6.2
androidx.graphics:graphics-path:1.0.1
androidx.interpolator:interpolator:1.0.0
androidx.legacy:legacy-support-core-ui:1.0.0
androidx.legacy:legacy-support-core-utils:1.0.0
androidx.legacy:legacy-support-v4:1.0.0
androidx.lifecycle:lifecycle-common-java8:2.8.3
androidx.lifecycle:lifecycle-common-jvm:2.8.3
androidx.lifecycle:lifecycle-extensions:2.2.0
androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3
androidx.lifecycle:lifecycle-livedata-core:2.8.3
androidx.lifecycle:lifecycle-livedata-ktx:2.8.3
androidx.lifecycle:lifecycle-livedata:2.8.3
androidx.lifecycle:lifecycle-process:2.8.3
androidx.lifecycle:lifecycle-runtime-android:2.8.3
androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3
androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3
androidx.lifecycle:lifecycle-runtime:2.8.3
androidx.lifecycle:lifecycle-service:2.8.3
androidx.lifecycle:lifecycle-viewmodel-android:2.8.3
androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3
androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3
androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3
androidx.lifecycle:lifecycle-viewmodel:2.8.3
androidx.loader:loader:1.0.0
androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
androidx.media3:media3-common:1.5.1
androidx.media3:media3-container:1.5.1
androidx.media3:media3-database:1.5.1
androidx.media3:media3-datasource:1.5.1
androidx.media3:media3-decoder:1.5.1
androidx.media3:media3-exoplayer-hls:1.5.1
androidx.media3:media3-exoplayer:1.5.1
androidx.media3:media3-extractor:1.5.1
androidx.media:media:1.4.3
androidx.metrics:metrics-performance:1.0.0-alpha04
androidx.multidex:multidex:2.0.1
androidx.navigation:navigation-common-ktx:2.8.2
androidx.navigation:navigation-common:2.8.2
androidx.navigation:navigation-compose:2.8.2
androidx.navigation:navigation-fragment-ktx:2.8.2
androidx.navigation:navigation-fragment:2.8.2
androidx.navigation:navigation-runtime-ktx:2.8.2
androidx.navigation:navigation-runtime:2.8.2
androidx.navigation:navigation-ui-ktx:2.8.2
androidx.navigation:navigation-ui:2.8.2
androidx.palette:palette:1.0.0
androidx.preference:preference-ktx:1.2.0
androidx.preference:preference:1.2.0
androidx.print:print:1.0.0
androidx.profileinstaller:profileinstaller:1.3.1
androidx.recyclerview:recyclerview:1.2.1
androidx.resourceinspection:resourceinspection-annotation:1.0.1
androidx.room:room-common:2.4.3
androidx.room:room-ktx:2.4.3
androidx.room:room-runtime:2.4.3
androidx.savedstate:savedstate-ktx:1.2.1
androidx.savedstate:savedstate:1.2.1
androidx.security:security-crypto:1.1.0-alpha05
androidx.slidingpanelayout:slidingpanelayout:1.2.0
androidx.sqlite:sqlite-framework:2.2.0
androidx.sqlite:sqlite:2.2.0
androidx.startup:startup-runtime:1.1.1
androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
androidx.test.espresso:espresso-idling-resource:3.3.0
androidx.tracing:tracing:1.0.0
androidx.transition:transition:1.4.1
androidx.vectordrawable:vectordrawable-animated:1.1.0
androidx.vectordrawable:vectordrawable:1.1.0
androidx.versionedparcelable:versionedparcelable:1.1.1
androidx.viewpager2:viewpager2:1.0.0
androidx.viewpager:viewpager:1.0.0
androidx.window:window:1.0.0
cn.bingoogolapple:bga-qrcode-core:1.3.7
cn.bingoogolapple:bga-qrcode-zxing:1.3.7
cn.eeo.android.base:network-http:2.1.1
cn.eeo.android.base:network-service:2.1.1
cn.eeo.android.base:network-socket:1.7.14
cn.eeo.android.base:network-traceroute:1.2.2
cn.eeo.android.business.protocol:edb:1.5.3
cn.eeo.android.business.protocol:instruction:1.5.3
cn.eeo.android.business:account:2.0.21
cn.eeo.android.business:basic:2.2.0
cn.eeo.android.business:clouddisk:2.1.0
cn.eeo.android.business:cluster:2.1.6
cn.eeo.android.business:course:2.1.0
cn.eeo.android.business:drawing-processor:2.1.1
cn.eeo.android.business:epush:1.5.0
cn.eeo.android.business:homework:3.1.2
cn.eeo.android.business:liveroom:2.1.1
cn.eeo.android.business:microlecture:2.0.0
cn.eeo.android.business:offlineres:1.1.1-alpha.0
cn.eeo.android.business:player-assistor:1.2.1
cn.eeo.android.business:router:2.0.0
cn.eeo.android.business:shaperecognition:0.1.2
cn.eeo.android.business:startup:2.1.14
cn.eeo.android.business:tablet:1.0.24
cn.eeo.android.business:xpush-core:1.2.1
cn.eeo.android.business:xpush-honor:1.0.1
cn.eeo.android.business:xpush-huawei:1.4.0
cn.eeo.android.business:xpush-oppo:1.4.0
cn.eeo.android.business:xpush-vivo:1.3.0
cn.eeo.android.business:xpush-xiaomi:1.3.0
cn.eeo.android.core:bezier:1.2.0
cn.eeo.android.core:codeblock:1.0.0
cn.eeo.android.core:common:2.1.0
cn.eeo.android.core:datamonitor:2.1.0
cn.eeo.android.core:encryption:1.0.9
cn.eeo.android.core:file-upload:2.0.0
cn.eeo.android.core:initialize:1.1.0
cn.eeo.android.core:jpegturbo:1.0.1
cn.eeo.android.core:json-parse:1.0.0
cn.eeo.android.core:logger:1.2.4
cn.eeo.android.core:movehelper:1.0.6
cn.eeo.android.core:rocket:1.2.4
cn.eeo.android.core:security:1.0.4
cn.eeo.android.core:shapekit:1.1.0
cn.eeo.android.core:social:1.8.1
cn.eeo.android.core:tinypinyin:1.0.1
cn.eeo.android.core:tools:2.1.0
cn.eeo.android.data:classroom:2.0.7
cn.eeo.android.data:cloudspace:2.0.0
cn.eeo.android.data:cluster:2.1.0
cn.eeo.android.data:core:1.2.1
cn.eeo.android.data:course:2.1.0
cn.eeo.android.data:lms:2.1.0
cn.eeo.android.data:microlecture:1.0.0
cn.eeo.android.data:startup:1.2.1
cn.eeo.android.widget:audiotoolkit:1.13.3
cn.eeo.android.widget:boxing:2.0.1
cn.eeo.android.widget:calendarview:1.2.0
cn.eeo.android.widget:camera:2.0.1
cn.eeo.android.widget:commonview:2.1.4
cn.eeo.android.widget:drawingboard:2.1.1
cn.eeo.android.widget:drawview:1.8.1
cn.eeo.android.widget:emoj:1.1.3
cn.eeo.android.widget:exolibrary:2.1.3
cn.eeo.android.widget:guideview:1.0.0
cn.eeo.android.widget:hybrid-webview:1.0.19
cn.eeo.android.widget:ijklib:1.2.4
cn.eeo.android.widget:media-muxer:1.6.13
cn.eeo.android.widget:newcalendarview:1.1.0
cn.eeo.android.widget:pdf:2.1.9
cn.eeo.android.widget:photoedit:2.0.2
cn.eeo.android.widget:ruler:0.1.4
cn.eeo.android.widget:screenrecord:1.7.0
cn.eeo.android.widget:theme:1.1.1
cn.eeo.android.widget:ucrop:2.0.0
cn.eeo.android.widget:uicomponent:1.0.2
cn.eeo.android.widget:upgrade:2.0.0
cn.eeo.android.widget:video-cache:2.1.0
cn.eeo.android.widget:videocodec:2.3.5
cn.eeo.android.widget:widgetlib:2.0.1
com.afollestad.assent:core:3.0.5
com.afollestad.material-dialogs:bottomsheets:3.3.0
com.afollestad.material-dialogs:core:3.3.0
com.afollestad.material-dialogs:datetime2:3.3.0
com.afollestad.material-dialogs:files:3.3.0
com.afollestad.material-dialogs:input:3.3.0
com.afollestad.material-dialogs:lifecycle:3.3.0
com.afollestad.material-dialogs:picker:3.3.0
com.afollestad:date-picker:0.6.1
com.afollestad:viewpagerdots:1.0.0
com.airbnb.android:lottie:5.2.0
com.airbnb.android:mavericks-common:3.0.2
com.airbnb.android:mavericks-compose:3.0.2
com.airbnb.android:mavericks:3.0.2
com.airbnb.android:showkase-annotation:1.0.0-beta18
com.alibaba:arouter-annotation:1.0.6
com.alibaba:arouter-api:1.5.8
com.amplitude:android-sdk:2.32.2
com.ashokvarma.android:bottom-navigation-bar:2.2.0
com.caverock:androidsvg-aar:1.4
com.chensl.rotatephotoview:rotatephotoview:1.0.5
com.eeoa.third-party:alipaysdk:15.8.12
com.eeoa.third-party:oauth-sdk:4.0.7
com.eeoa.third-party:oppo-push:3.1.0
com.eeoa.third-party:vivo-push:*******
com.eeoa.third-party:weibo:12.5.0
com.eeoa.third-party:xiaomi-push:5.6.2
com.excellence:ffmpeg-armv7a:1.2.2
com.fasterxml.jackson.core:jackson-annotations:2.8.0
com.fasterxml.jackson.core:jackson-core:2.8.9
com.fasterxml.jackson.core:jackson-databind:2.8.9
com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.15
com.github.MasayukiSuda:Mp4Composer-android:v0.4.1
com.github.anilbeesetti.nextlib:nextlib-media3ext:0.8.4
com.github.anilbeesetti.nextlib:nextlib-mediainfo:0.8.4
com.github.bumptech.glide:annotations:5.0.0-rc01
com.github.bumptech.glide:compose:1.0.0-beta01
com.github.bumptech.glide:disklrucache:5.0.0-rc01
com.github.bumptech.glide:gifdecoder:5.0.0-rc01
com.github.bumptech.glide:glide:5.0.0-rc01
com.github.bumptech.glide:ktx:1.0.0-beta01
com.github.bumptech.glide:recyclerview-integration:5.0.0-rc01
com.github.hackware1993:MagicIndicator:1.7.0
com.github.iwgang:simplifyspan:2.2
com.github.open-android:pinyin4j:2.5.0
com.github.stephenc.jcip:jcip-annotations:1.0-1
com.github.yyued:SVGAPlayer-Android:2.6.1
com.google.accompanist:accompanist-drawablepainter:0.32.0
com.google.accompanist:accompanist-flowlayout:0.28.0
com.google.accompanist:accompanist-navigation-animation:0.28.0
com.google.accompanist:accompanist-systemuicontroller:0.30.0
com.google.accompanist:accompanist-webview:0.28.0
com.google.android.exoplayer:exoplayer-common:2.18.1
com.google.android.exoplayer:exoplayer-core:2.18.1
com.google.android.exoplayer:exoplayer-dash:2.18.1
com.google.android.exoplayer:exoplayer-database:2.18.1
com.google.android.exoplayer:exoplayer-datasource:2.18.1
com.google.android.exoplayer:exoplayer-decoder:2.18.1
com.google.android.exoplayer:exoplayer-extractor:2.18.1
com.google.android.exoplayer:exoplayer-hls:2.18.1
com.google.android.exoplayer:exoplayer-rtsp:2.18.1
com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1
com.google.android.exoplayer:exoplayer-ui:2.18.1
com.google.android.exoplayer:exoplayer:2.18.1
com.google.android.flexbox:flexbox:3.0.0
com.google.android.gms:play-services-auth-api-phone:18.0.1
com.google.android.gms:play-services-auth-base:18.0.4
com.google.android.gms:play-services-auth:20.6.0
com.google.android.gms:play-services-base:18.0.1
com.google.android.gms:play-services-basement:18.2.0
com.google.android.gms:play-services-fido:20.0.1
com.google.android.gms:play-services-identity:18.0.1
com.google.android.gms:play-services-maps:18.0.2
com.google.android.gms:play-services-tasks:18.0.1
com.google.android.gms:play-services-wallet:19.1.0
com.google.android.instantapps:instantapps:1.1.0
com.google.android.material:material:1.8.0
com.google.code.gson:gson:2.10.1
com.google.crypto.tink:tink-android:1.7.0
com.google.dagger:dagger:2.45
com.google.errorprone:error_prone_annotations:2.36.0
com.google.guava:failureaccess:1.0.2
com.google.guava:guava:33.3.1-android
com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
com.google.zxing:core:3.3.3
com.hihonor.mcs:push:7.0.61.303
com.huawei.agconnect:agconnect-core:1.8.1.300
com.huawei.android.hms:security-base:1.2.0.306
com.huawei.android.hms:security-encrypt:1.2.0.307
com.huawei.android.hms:security-ssl:1.2.0.307
com.huawei.hmf:tasks:1.5.2.206
com.huawei.hms:availableupdate:6.8.0.300
com.huawei.hms:base:6.8.0.300
com.huawei.hms:baselegacyapi:6.8.0.300
com.huawei.hms:device:6.8.0.300
com.huawei.hms:dynamic-api:1.0.24.300
com.huawei.hms:hatool:6.11.0.301
com.huawei.hms:log:6.8.0.300
com.huawei.hms:ml-computer-agc-inner:3.8.0.303
com.huawei.hms:ml-computer-commonutils-inner:3.8.0.303
com.huawei.hms:ml-computer-sdkbase-inner:3.8.0.303
com.huawei.hms:network-common:7.0.2.301
com.huawei.hms:network-framework-compat:7.0.2.301
com.huawei.hms:network-grs:7.0.2.301
com.huawei.hms:opendevice:6.9.0.300
com.huawei.hms:push:6.9.0.300
com.huawei.hms:scan:2.12.0.301
com.huawei.hms:stats:6.8.0.300
com.huawei.hms:ui:6.8.0.300
com.iqiyi.xcrash:xcrash-android-lib:3.1.0
com.jakewharton:disklrucache:2.0.2
com.nimbusds:nimbus-jose-jwt:9.21
com.parse.bolts:bolts-tasks:1.4.0
com.paypal.android.platform:auth-sdk-thirdParty:1.0.3
com.paypal.android.sdk:data-collector:3.13.0
com.paypal.android:PayPalPartnerAuth:1.7.5
com.paypal.checkout:android-sdk:0.8.7
com.qcloud.cos:cos-android:5.9.8
com.qcloud.cos:cos-ktx:5.6.5
com.qcloud.cos:qcloud-foundation:1.5.51
com.sensorsdata.analytics.android:SensorsAnalyticsSDK:6.6.9
com.sensorsdata.analytics.android:advert:0.2.2
com.sensorsdata.analytics.android:autoTrack:0.2.2
com.sensorsdata.analytics.android:common:0.2.2
com.sensorsdata.analytics.android:core:0.2.2
com.sensorsdata.analytics.android:encrypt:0.2.2
com.sensorsdata.analytics.android:exposure:0.2.2
com.sensorsdata.analytics.android:push:0.2.2
com.sensorsdata.analytics.android:visual:0.2.2
com.sensorsdata.analytics.android:webview:0.2.2
com.squareup.okhttp3:logging-interceptor:4.10.0
com.squareup.okhttp3:okhttp:4.12.0
com.squareup.okio:okio-jvm:3.9.0
com.squareup.picasso:picasso:2.8
com.squareup.retrofit2:converter-gson:2.9.0
com.squareup.retrofit2:converter-scalars:2.9.0
com.squareup.retrofit2:retrofit:2.9.0
com.squareup.wire:wire-runtime:2.3.0-RC1
com.stripe:financial-connections:20.22.0
com.stripe:link:20.22.0
com.stripe:payments-core:20.22.0
com.stripe:payments-model:20.22.0
com.stripe:payments-ui-core:20.22.0
com.stripe:paymentsheet:20.22.0
com.stripe:stripe-3ds2-android:6.1.7
com.stripe:stripe-android:20.22.0
com.stripe:stripe-core:20.22.0
com.stripe:stripe-ui-core:20.22.0
com.tencent.mars:mars-xlog:1.2.6
com.tencent.mm.opensdk:wechat-sdk-android-without-mta:6.8.0
commons-codec:commons-codec:1.11
io.coil-kt:coil-base:2.7.0
io.coil-kt:coil-compose-base:2.7.0
io.coil-kt:coil-compose:2.7.0
io.coil-kt:coil-gif:2.7.0
io.coil-kt:coil-svg:2.7.0
io.coil-kt:coil:2.7.0
io.github.raamcosta.compose-destinations:animations-core:1.11.7
io.github.raamcosta.compose-destinations:core:1.11.7
io.insert-koin:koin-android:3.2.0
io.insert-koin:koin-core-jvm:3.2.0
io.jsonwebtoken:jjwt:0.9.0
io.netty:netty-all:4.1.86.Final
io.netty:netty-buffer:4.1.86.Final
io.netty:netty-codec-dns:4.1.86.Final
io.netty:netty-codec-haproxy:4.1.86.Final
io.netty:netty-codec-http2:4.1.86.Final
io.netty:netty-codec-http:4.1.86.Final
io.netty:netty-codec-memcache:4.1.86.Final
io.netty:netty-codec-mqtt:4.1.86.Final
io.netty:netty-codec-redis:4.1.86.Final
io.netty:netty-codec-smtp:4.1.86.Final
io.netty:netty-codec-socks:4.1.86.Final
io.netty:netty-codec-stomp:4.1.86.Final
io.netty:netty-codec-xml:4.1.86.Final
io.netty:netty-codec:4.1.86.Final
io.netty:netty-common:4.1.86.Final
io.netty:netty-handler-proxy:4.1.86.Final
io.netty:netty-handler-ssl-ocsp:4.1.86.Final
io.netty:netty-handler:4.1.86.Final
io.netty:netty-resolver-dns-classes-macos:4.1.86.Final
io.netty:netty-resolver-dns-native-macos:4.1.86.Final
io.netty:netty-resolver-dns:4.1.86.Final
io.netty:netty-resolver:4.1.86.Final
io.netty:netty-transport-classes-epoll:4.1.86.Final
io.netty:netty-transport-classes-kqueue:4.1.86.Final
io.netty:netty-transport-native-epoll:4.1.86.Final
io.netty:netty-transport-native-kqueue:4.1.86.Final
io.netty:netty-transport-native-unix-common:4.1.86.Final
io.netty:netty-transport-rxtx:4.1.86.Final
io.netty:netty-transport-sctp:4.1.86.Final
io.netty:netty-transport-udt:4.1.86.Final
io.netty:netty-transport:4.1.86.Final
io.reactivex.rxjava2:rxandroid:2.1.1
io.reactivex.rxjava2:rxjava:2.2.12
io.sentry:sentry-android-core:7.1.0
io.sentry:sentry-android-fragment:7.1.0
io.sentry:sentry-android-navigation:7.1.0
io.sentry:sentry-android-ndk:7.1.0
io.sentry:sentry-android-okhttp:7.1.0
io.sentry:sentry-android-sqlite:7.1.0
io.sentry:sentry-android:7.1.0
io.sentry:sentry-compose-android:7.1.0
io.sentry:sentry-kotlin-extensions:7.1.0
io.sentry:sentry-okhttp:7.1.0
io.sentry:sentry:7.1.0
javax.inject:javax.inject:1
javax.validation:validation-api:1.1.0.Final
jp.wasabeef:blurry:4.0.0
me.panpf:sketch:2.7.4
me.zhanghai.android.materialprogressbar:library:1.6.1
net.lingala.zip4j:zip4j:2.11.1
org.ahocorasick:ahocorasick:0.3.0
org.bouncycastle:bcprov-jdk15to18:1.69
org.greenrobot:eventbus-java:3.3.1
org.greenrobot:eventbus:3.3.1
org.jacoco:org.jacoco.agent:0.8.7
org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.0
org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.0
org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.0
org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.0
org.jetbrains.kotlin:kotlin-stdlib:2.1.0
org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1
org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1
org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:1.8.1
org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1
org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.3
org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.6.3
org.jetbrains:annotations:23.0.0
org.jfrog.cardinalcommerce.gradle:cardinalmobilesdk:2.2.7-2
org.mp4parser:isoparser:1.9.41
org.mp4parser:muxer:1.9.41
org.nanohttpd:nanohttpd:2.3.1
org.reactivestreams:reactive-streams:1.0.2
org.slf4j:slf4j-api:1.7.24
pl.droidsonroids.gif:android-gif-drawable:1.2.25
pl.droidsonroids.relinker:relinker:1.4.1