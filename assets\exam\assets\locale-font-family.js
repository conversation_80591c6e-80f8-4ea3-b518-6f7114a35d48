(function () {
  var lang = navigator.language;
  // 需要特殊处理的多语言
  var languageList = ['vi', 'ko'];
  var langFontFamily = {
    vi: '"system-ui", "Segoe UI", "Noto Sans", "Roboto", Aria<PERSON>, sans-serif',
    ko: '"system-ui", "Noto Sans KR", "Malgun Gothic", "Apple SD GothicNeo", Arial, sans-serif'
  };
  // 将浏览器的语言代码转换为短形式
  var shortLang = lang.split('-')[0];
  if (languageList.includes(shortLang)) {
    var style = document.createElement('style');
    style.type = 'text/css';
    style.innerHTML = `
    * {font-family: ${langFontFamily[shortLang]} !important;}
  `;
    document.head.appendChild(style);
  }
})();
