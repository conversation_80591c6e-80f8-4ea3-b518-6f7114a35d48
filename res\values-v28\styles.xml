<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.Theme.AppCompat" parent="@style/Base.V28.Theme.AppCompat" />
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V28.Theme.AppCompat.Light" />
    <style name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="@style/ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:backgroundDimAmount">0.32</item>
        <item name="android:checkedTextViewStyle">@style/Widget.MaterialComponents.CheckedTextView</item>
        <item name="android:dialogCornerRadius">@null</item>
        <item name="alertDialogStyle">@style/MaterialAlertDialog.MaterialComponents</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarNeutralButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="materialAlertDialogBodyTextStyle">@style/MaterialAlertDialog.MaterialComponents.Body.Text</item>
        <item name="materialAlertDialogButtonSpacerVisibility">@integer/mtrl_view_invisible</item>
    </style>
    <style name="EdgeToEdgeFloatingDialogTheme" parent="@android:style/Theme.DeviceDefault.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowElevation">0.0dip</item>
        <item name="android:windowClipToOutline">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">@integer/m3c_window_layout_in_display_cutout_mode</item>
    </style>
    <style name="SplashTheme" parent="@style/AppTheme">
        <item name="android:windowBackground">@drawable/logo_splash</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.BodyLarge" parent="@style/TextAppearance.AppCompat.Body2">
        <item name="android:textSize">16.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.03125</item>
        <item name="android:lineHeight">24.0sp</item>
        <item name="fontFamily">sans-serif</item>
        <item name="lineHeight">24.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.BodyMedium" parent="@style/TextAppearance.AppCompat.Body1">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.01785714</item>
        <item name="android:lineHeight">20.0sp</item>
        <item name="fontFamily">sans-serif</item>
        <item name="lineHeight">20.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.BodySmall" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">12.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.03333333</item>
        <item name="android:lineHeight">16.0sp</item>
        <item name="fontFamily">sans-serif</item>
        <item name="lineHeight">16.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.DisplayLarge" parent="@style/TextAppearance.AppCompat.Display3">
        <item name="android:textSize">57.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">-0.00438596</item>
        <item name="android:lineHeight">64.0sp</item>
        <item name="fontFamily">sans-serif</item>
        <item name="lineHeight">64.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.DisplayMedium" parent="@style/TextAppearance.AppCompat.Display2">
        <item name="android:textSize">45.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="android:lineHeight">52.0sp</item>
        <item name="fontFamily">sans-serif</item>
        <item name="lineHeight">52.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.DisplaySmall" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textSize">36.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="android:lineHeight">44.0sp</item>
        <item name="fontFamily">sans-serif</item>
        <item name="lineHeight">44.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.HeadlineLarge" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textSize">32.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="android:lineHeight">40.0sp</item>
        <item name="fontFamily">sans-serif</item>
        <item name="lineHeight">40.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.HeadlineMedium" parent="@style/TextAppearance.AppCompat.Headline">
        <item name="android:textSize">28.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="android:lineHeight">36.0sp</item>
        <item name="fontFamily">sans-serif</item>
        <item name="lineHeight">36.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.HeadlineSmall" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">24.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="android:lineHeight">32.0sp</item>
        <item name="fontFamily">sans-serif</item>
        <item name="lineHeight">32.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.LabelLarge" parent="@style/TextAppearance.AppCompat.Body1">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.00714286</item>
        <item name="android:lineHeight">20.0sp</item>
        <item name="fontFamily">sans-serif-medium</item>
        <item name="lineHeight">20.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.LabelMedium" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">12.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.04166667</item>
        <item name="android:lineHeight">16.0sp</item>
        <item name="fontFamily">sans-serif-medium</item>
        <item name="lineHeight">16.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.LabelSmall" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">11.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.04545455</item>
        <item name="android:lineHeight">16.0sp</item>
        <item name="fontFamily">sans-serif-medium</item>
        <item name="lineHeight">16.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.TitleLarge" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">22.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="android:lineHeight">28.0sp</item>
        <item name="fontFamily">sans-serif</item>
        <item name="lineHeight">28.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.TitleMedium" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">16.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.009375</item>
        <item name="android:lineHeight">24.0sp</item>
        <item name="fontFamily">sans-serif-medium</item>
        <item name="lineHeight">24.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.TitleSmall" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.00714286</item>
        <item name="android:lineHeight">20.0sp</item>
        <item name="fontFamily">sans-serif-medium</item>
        <item name="lineHeight">20.0sp</item>
    </style>
    <style name="Widget.Material3.AppBarLayout" parent="@style/Widget.MaterialComponents.AppBarLayout.Surface">
        <item name="android:outlineSpotShadowColor">@android:color/transparent</item>
        <item name="android:outlineAmbientShadowColor">@android:color/transparent</item>
        <item name="liftOnScroll">true</item>
    </style>
    <style name="Widget.Material3.BottomNavigationView" parent="@style/Base.Widget.Material3.BottomNavigationView">
        <item name="android:outlineSpotShadowColor">@android:color/transparent</item>
        <item name="android:outlineAmbientShadowColor">@android:color/transparent</item>
    </style>
    <style name="Widget.Material3.SearchBar" parent="@android:style/Widget">
        <item name="android:textAppearance">@style/TextAppearance.Material3.SearchBar</item>
        <item name="android:minHeight">@dimen/m3_searchbar_height</item>
        <item name="android:paddingStart">@dimen/m3_searchbar_padding_start</item>
        <item name="android:outlineSpotShadowColor">@android:color/transparent</item>
        <item name="android:outlineAmbientShadowColor">@android:color/transparent</item>
        <item name="defaultMarginsEnabled">true</item>
        <item name="defaultScrollFlagsEnabled">true</item>
        <item name="elevation">@dimen/m3_searchbar_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="hideNavigationIcon">false</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Material3.Search</item>
        <item name="maxButtonHeight">@dimen/m3_searchbar_height</item>
        <item name="shapeAppearance">?shapeAppearanceMediumComponent</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.Material3.SearchBar</item>
    </style>
    <style name="Widget.Material3.Toolbar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="android:outlineSpotShadowColor">@android:color/transparent</item>
        <item name="android:outlineAmbientShadowColor">@android:color/transparent</item>
        <item name="contentInsetStartWithNavigation">0.0dip</item>
        <item name="subtitleTextAppearance">?textAppearanceTitleMedium</item>
        <item name="titleTextAppearance">?textAppearanceTitleLarge</item>
    </style>
    <style name="green_button_style" parent="@style/eo_base_button_style">
        <item name="android:textSize">14.0dip</item>
        <item name="android:textColor">@color/button_text_color</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/btn_main_green_bg_selector</item>
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">44.0dip</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/default_font</item>
        <item name="android:textFontWeight">500</item>
    </style>
    <style name="Base.V28.Theme.AppCompat" parent="@style/Base.V26.Theme.AppCompat">
        <item name="dialogCornerRadius">?android:dialogCornerRadius</item>
    </style>
    <style name="Base.V28.Theme.AppCompat.Light" parent="@style/Base.V26.Theme.AppCompat.Light">
        <item name="dialogCornerRadius">?android:dialogCornerRadius</item>
    </style>
</resources>
