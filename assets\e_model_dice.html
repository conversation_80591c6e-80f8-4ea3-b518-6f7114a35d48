<!--
  ~ Copyright (C) 2017 Facishare Technology Co., Ltd. All Rights Reserved.
  -->
<!DOCTYPE html>

<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Model Dice</title>
<link href="css/style.css" rel="stylesheet" type="text/css">
<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
<script src="js/jquery.min.js"></script>
<!--<script src="js/lang.config.js"></script>
<script src="js/lang.data.js"></script>
<script src="js/style.js"></script>-->
</head>

<body style="background-color: #2c2c2c; position: absolute; width: 100%; height: 100%; left: 0; top: 0; overflow: hidden;">

<!--<input type="button" onclick="myDicBox.diceBoxNumber(1)" value="1" />
<input type="button" onclick="myDicBox.diceBoxNumber(2)" value="2" />
<input type="button" onclick="myDicBox.diceBoxNumber(3)" value="3" />
<input type="button" onclick="myDicBox.diceBoxNumber(4)" value="4" />
<input type="button" onclick="myDicBox.diceBoxNumber(5)" value="5" />
<input type="button" onclick="myDicBox.diceBoxNumber(6)" value="6" />
<input type="button" onclick="myDicBox.diceBoxNumber('r')" value="r" />
<input type="button" onclick="myDicBox.rolling(true)" value="play" />-->
<script type="text/javascript">
	function DiceBox(config){
		var root=this;
		root.mouse={
			press:false,
			down:{
				x:0,
				y:0
			},
			move:{
				x:0,
				y:0
			}
		}
		root.rollRound={
			x:0,
			y:0
		}
		root.dom=document.createElement('div');
		root.$dom=$(root.dom)
		root.dom.className='diceBox';
		root.dom.innerHTML='<div class="faceBox" data-num="x"></div>'+
							'<div class="faceBox" data-num="y"></div>'+
							'<div class="faceBox" data-num="z"></div>'+
							'<div class="faceBox" data-num="x1"></div>'+
							'<div class="faceBox" data-num="x2"></div>'+
							'<div class="faceBox" data-num="y1"></div>'+
							'<div class="faceBox" data-num="y2"></div>'+
							'<div class="faceBox" data-num="z1"></div>'+
							'<div class="faceBox" data-num="z2"></div>'+
							'<div class="faceBox" data-num="1"></div>'+
							'<div class="faceBox" data-num="2"></div>'+
							'<div class="faceBox" data-num="3"></div>'+
							'<div class="faceBox" data-num="4"></div>'+
							'<div class="faceBox" data-num="5"></div>'+
							'<div class="faceBox" data-num="6"></div>';
		root.diceBoxRoll=function (rx,ry,hasAnimate){
			root.rollRound.x++;
			root.rollRound.y++;
			if(hasAnimate==false){
				root.$dom.css({
					'-webkit-transition': 'none'
				});
			}else{
				root.$dom.css({
					'-webkit-transition': ''
				});
			}
			root.$dom.css({
				'-webkit-transform': 'perspective(6em) rotateX('+(root.rollRound.x*360+rx)+'deg) rotateY('+(root.rollRound.y*360+ry)+'deg)'
			})
		}
		var rollingInterval;
		root.rolling=function (isPlay){
			var rollspeed=800;
			if(isPlay){
				root.$dom.css({
					'-webkit-transition': rollspeed+'ms all linear'
				});
				root.diceBoxRoll(0,0)
				rollingInterval=setInterval(function(){
					//console.log("rolling");
					root.diceBoxRoll(0,0)
				},rollspeed)
			}else{
				clearInterval(rollingInterval);
				root.$dom.css({
					'-webkit-transition': rollspeed*3+'ms all ease-out'
				});
			}
		}
		root.diceBoxNumber=function (n,hasAnimate){
			if(n=='r'){
				n=Math.ceil(Math.random()*6);
			}
			n=n+'';

			rollRandom={
				x:15-Math.floor(Math.random()*30),
				y:15-Math.floor(Math.random()*30)
			}
			root.rolling(false);
			switch(n){
				case '1':
					root.diceBoxRoll(rollRandom.x+0,rollRandom.y+0,hasAnimate)
					break;
				case '2':
					root.diceBoxRoll(rollRandom.x+0,rollRandom.y-90,hasAnimate)
					break;
				case '3':
					root.diceBoxRoll(rollRandom.x+0,rollRandom.y-180,hasAnimate)
					break;
				case '4':
					root.diceBoxRoll(rollRandom.x+0,rollRandom.y+90,hasAnimate)
					break;
				case '5':
					root.diceBoxRoll(rollRandom.x+90,rollRandom.y+0,hasAnimate)
					break;
				case '6':
					root.diceBoxRoll(rollRandom.x-90,rollRandom.y+0,hasAnimate)
					break;
			}
		}
		/*root.$dom.on('mousedown',function(e){
			root.mouse.press=true;
			root.mouse.down.x=e.pageX;
			root.mouse.down.y=e.pageY;
		})
		root.$dom.on('mousemove',function(e){
			if(root.mouse.press){
				root.mouse.move.x=e.pageX;
				root.mouse.move.y=e.pageY;
				root.$dom.css({
					'-webkit-transform': 'perspective(650px) rotateX('+(-(root.mouse.move.y-root.mouse.down.y))+'deg) rotateY('+(root.mouse.move.x-root.mouse.down.x)+'deg)'
				})
			}

		})*/
		function diceResize(){
			var winWidth=$(window).width();
			var winHeight=$(window).height();
			var diceSize=winWidth<winHeight?winWidth:winHeight;
			root.$dom.css({
				'font-size':diceSize*2/4
			})
		}
		diceResize();
		$(window).on('resize',function(){
			diceResize();
		})
	}

	var myDicBox=new DiceBox();
	document.body.appendChild(myDicBox.dom);

	$(document).ready(function(){
		window.myDiceWindow.onDocReady();
	})

</script>

</body>

</html>