<?xml version="1.0" encoding="utf-8"?>
<selector
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <item android:state_enabled="true" android:state_hovered="true">
        <objectAnimator android:interpolator="?motionEasingEmphasizedInterpolator" android:duration="?motionDurationMedium4" android:valueTo="@dimen/m3_card_elevated_hovered_z" android:valueType="floatType" android:propertyName="translationZ" android:startDelay="?motionDurationMedium1" />
    </item>
    <item android:state_enabled="true" app:state_dragged="true">
        <objectAnimator android:interpolator="?motionEasingEmphasizedInterpolator" android:duration="?motionDurationMedium4" android:valueTo="@dimen/m3_card_elevated_dragged_z" android:valueType="floatType" android:propertyName="translationZ" android:startDelay="?motionDurationMedium1" />
    </item>
    <item>
        <set>
            <objectAnimator android:interpolator="?motionEasingEmphasizedInterpolator" android:duration="?motionDurationLong2" android:valueTo="0.0dip" android:valueType="floatType" android:propertyName="translationZ" />
        </set>
    </item>
    <item>
        <objectAnimator android:duration="0" android:valueTo="0.0dip" android:valueType="floatType" android:propertyName="translationZ" />
    </item>
</selector>
