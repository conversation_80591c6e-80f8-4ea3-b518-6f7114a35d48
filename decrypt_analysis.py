#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ClassIn .crypt文件解密分析工具
基于逆向分析的解密尝试
"""

import os
import struct
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import hashlib

class ClassInDecryptor:
    def __init__(self):
        # 基于分析得出的可能密钥
        self.possible_keys = [
            b'classin_h5player_key_2023',  # 可能的密钥1
            b'eeo_encryption_key_v1.0.9',  # 基于版本信息
            b'cn.eeo.classin.h5player',    # 基于包名
        ]
        
    def xor_decode(self, data, key=0x10):
        """XOR解码 - 基于发现的混淆算法"""
        return bytes(b ^ key for b in data)
    
    def analyze_file_header(self, filepath):
        """分析文件头部结构"""
        print(f"\n=== 分析文件: {filepath} ===")
        
        if not os.path.exists(filepath):
            print(f"文件不存在: {filepath}")
            return None
            
        with open(filepath, 'rb') as f:
            # 读取文件头
            header = f.read(64)
            file_size = os.path.getsize(filepath)
            
            print(f"文件大小: {file_size} bytes")
            print("文件头分析:")
            
            # 检查是否有明显的加密标识
            magic_bytes = header[:4]
            print(f"Magic bytes: {magic_bytes.hex()}")
            
            # 尝试识别加密算法
            if len(header) >= 16:
                # 检查是否是AES块大小的倍数
                if file_size % 16 == 0:
                    print("文件大小是16的倍数，可能使用AES加密")
                
                # 分析可能的IV
                possible_iv = header[:16]
                print(f"可能的IV: {possible_iv.hex()}")
                
            return header
    
    def try_aes_decrypt(self, data, key, iv=None):
        """尝试AES解密"""
        try:
            # 确保密钥长度
            if len(key) < 32:
                key = hashlib.sha256(key).digest()
            elif len(key) > 32:
                key = key[:32]
            
            # 如果没有提供IV，使用文件头作为IV
            if iv is None:
                iv = data[:16]
                encrypted_data = data[16:]
            else:
                encrypted_data = data
            
            # 尝试不同的AES模式
            modes = [AES.MODE_CBC, AES.MODE_ECB]
            
            for mode in modes:
                try:
                    if mode == AES.MODE_ECB:
                        cipher = AES.new(key, mode)
                    else:
                        cipher = AES.new(key, mode, iv)
                    
                    decrypted = cipher.decrypt(encrypted_data)
                    
                    # 检查解密结果是否合理
                    if self.is_valid_decryption(decrypted):
                        return decrypted
                        
                except Exception as e:
                    continue
                    
        except Exception as e:
            pass
            
        return None
    
    def is_valid_decryption(self, data):
        """检查解密结果是否有效"""
        if len(data) < 10:
            return False
            
        # 检查是否包含常见的文件头
        common_headers = [
            b'<!DOCTYPE',  # HTML
            b'<html',      # HTML
            b'function',   # JavaScript
            b'var ',       # JavaScript
            b'(function',  # JavaScript
            b'PK',         # ZIP
            b'\x1f\x8b',   # GZIP
        ]
        
        for header in common_headers:
            if data.startswith(header):
                return True
                
        # 检查是否主要是可打印字符
        printable_count = sum(1 for b in data[:100] if 32 <= b <= 126 or b in [9, 10, 13])
        if printable_count > 80:  # 80%以上是可打印字符
            return True
            
        return False
    
    def decrypt_file(self, filepath, output_path=None):
        """尝试解密文件"""
        print(f"\n=== 尝试解密: {filepath} ===")
        
        if not os.path.exists(filepath):
            print(f"文件不存在: {filepath}")
            return False
            
        with open(filepath, 'rb') as f:
            data = f.read()
        
        # 分析文件头
        self.analyze_file_header(filepath)
        
        # 尝试不同的解密方法
        decrypted_data = None
        
        # 方法1: 简单XOR
        print("\n尝试XOR解密...")
        xor_result = self.xor_decode(data)
        if self.is_valid_decryption(xor_result):
            print("XOR解密成功!")
            decrypted_data = xor_result
        
        # 方法2: AES解密
        if decrypted_data is None:
            print("\n尝试AES解密...")
            for i, key in enumerate(self.possible_keys):
                print(f"尝试密钥 {i+1}: {key}")
                result = self.try_aes_decrypt(data, key)
                if result:
                    print(f"AES解密成功! 使用密钥: {key}")
                    decrypted_data = result
                    break
        
        # 方法3: 组合解密 (先AES后XOR)
        if decrypted_data is None:
            print("\n尝试组合解密 (AES + XOR)...")
            for key in self.possible_keys:
                aes_result = self.try_aes_decrypt(data, key)
                if aes_result:
                    xor_result = self.xor_decode(aes_result)
                    if self.is_valid_decryption(xor_result):
                        print("组合解密成功!")
                        decrypted_data = xor_result
                        break
        
        # 保存解密结果
        if decrypted_data:
            if output_path is None:
                output_path = filepath.replace('.crypt', '_decrypted')
            
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            
            print(f"解密文件已保存: {output_path}")
            
            # 显示解密内容的前几行
            try:
                preview = decrypted_data[:500].decode('utf-8', errors='ignore')
                print(f"\n解密内容预览:\n{preview}")
            except:
                print(f"\n解密内容 (hex): {decrypted_data[:100].hex()}")
            
            return True
        else:
            print("解密失败!")
            return False

def main():
    """主函数"""
    decryptor = ClassInDecryptor()
    
    # 要解密的文件列表
    files_to_decrypt = [
        'assets/h5player.crypt',
        'assets/h5player_v10.crypt',
        'assets/codeEditorBundle.crypt'
    ]
    
    print("ClassIn .crypt文件解密工具")
    print("=" * 50)
    
    for filepath in files_to_decrypt:
        if os.path.exists(filepath):
            decryptor.decrypt_file(filepath)
        else:
            print(f"文件不存在: {filepath}")

if __name__ == "__main__":
    main()
