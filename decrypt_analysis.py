#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ClassIn 流媒体加密分析工具
专门分析M3U8和HLS流媒体的加密解密逻辑
"""

import os
import struct
import re
import requests
from urllib.parse import urljoin, urlparse
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad, pad
import hashlib
import binascii

class M3U8StreamAnalyzer:
    """M3U8流媒体加密分析器"""

    def __init__(self):
        # HLS标准加密方法
        self.hls_methods = ['AES-128', 'AES-192', 'AES-256', 'SAMPLE-AES']

        # 可能的密钥来源
        self.key_sources = [
            'EXT-X-KEY',  # 标准HLS密钥
            'custom_key', # 自定义密钥
        ]

        # ClassIn可能使用的密钥
        self.classin_keys = [
            b'classin_hls_key_2023',
            b'eeo_stream_key_v1.0.9',
            b'cn.eeo.classin.stream',
            b'hls_encryption_key',
        ]

    def analyze_m3u8_file(self, m3u8_path_or_url):
        """分析M3U8文件的加密信息"""
        print(f"\n=== 分析M3U8文件: {m3u8_path_or_url} ===")

        # 读取M3U8内容
        if m3u8_path_or_url.startswith('http'):
            try:
                response = requests.get(m3u8_path_or_url)
                content = response.text
                base_url = '/'.join(m3u8_path_or_url.split('/')[:-1]) + '/'
            except Exception as e:
                print(f"无法下载M3U8文件: {e}")
                return None
        else:
            if not os.path.exists(m3u8_path_or_url):
                print(f"M3U8文件不存在: {m3u8_path_or_url}")
                return None
            with open(m3u8_path_or_url, 'r', encoding='utf-8') as f:
                content = f.read()
            base_url = os.path.dirname(m3u8_path_or_url) + '/'

        print("M3U8内容分析:")
        print("-" * 50)

        # 分析加密信息
        encryption_info = self.parse_encryption_info(content)

        # 分析分段信息
        segments = self.parse_segments(content, base_url)

        return {
            'content': content,
            'base_url': base_url,
            'encryption': encryption_info,
            'segments': segments
        }

    def parse_encryption_info(self, m3u8_content):
        """解析M3U8中的加密信息"""
        encryption_info = {
            'method': None,
            'key_uri': None,
            'iv': None,
            'key_format': None,
            'key_format_versions': None,
            'is_encrypted': False
        }

        # 查找EXT-X-KEY标签
        key_pattern = r'#EXT-X-KEY:(.+)'
        key_matches = re.findall(key_pattern, m3u8_content)

        if key_matches:
            encryption_info['is_encrypted'] = True
            for key_line in key_matches:
                print(f"发现加密标签: #EXT-X-KEY:{key_line}")

                # 解析加密方法
                method_match = re.search(r'METHOD=([^,\s]+)', key_line)
                if method_match:
                    encryption_info['method'] = method_match.group(1)
                    print(f"加密方法: {encryption_info['method']}")

                # 解析密钥URI
                uri_match = re.search(r'URI="([^"]+)"', key_line)
                if uri_match:
                    encryption_info['key_uri'] = uri_match.group(1)
                    print(f"密钥URI: {encryption_info['key_uri']}")

                # 解析IV
                iv_match = re.search(r'IV=0x([A-Fa-f0-9]+)', key_line)
                if iv_match:
                    encryption_info['iv'] = iv_match.group(1)
                    print(f"IV: {encryption_info['iv']}")

                # 解析密钥格式
                keyformat_match = re.search(r'KEYFORMAT="([^"]+)"', key_line)
                if keyformat_match:
                    encryption_info['key_format'] = keyformat_match.group(1)
                    print(f"密钥格式: {encryption_info['key_format']}")
        else:
            print("未发现标准HLS加密标签")
            # 检查是否有自定义加密标识
            if self.detect_custom_encryption(m3u8_content):
                encryption_info['is_encrypted'] = True
                encryption_info['method'] = 'CUSTOM'
                print("检测到可能的自定义加密")

        return encryption_info

    def detect_custom_encryption(self, content):
        """检测自定义加密标识"""
        custom_patterns = [
            r'#EEO-ENCRYPT',
            r'#CLASSIN-KEY',
            r'#CUSTOM-ENCRYPT',
            r'\.crypt',
            r'encrypted=true'
        ]

        for pattern in custom_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        return False

    def parse_segments(self, m3u8_content, base_url):
        """解析M3U8中的分段信息"""
        segments = []
        lines = m3u8_content.strip().split('\n')

        current_segment = {}
        for line in lines:
            line = line.strip()

            if line.startswith('#EXTINF:'):
                # 分段时长信息
                duration_match = re.search(r'#EXTINF:([0-9.]+)', line)
                if duration_match:
                    current_segment['duration'] = float(duration_match.group(1))

            elif line and not line.startswith('#'):
                # 分段文件URL
                if line.startswith('http'):
                    current_segment['url'] = line
                else:
                    current_segment['url'] = urljoin(base_url, line)

                current_segment['filename'] = os.path.basename(line)
                segments.append(current_segment.copy())
                current_segment = {}

        print(f"发现 {len(segments)} 个视频分段")
        return segments

    def get_encryption_key(self, key_uri, base_url):
        """获取加密密钥"""
        if not key_uri:
            return None

        try:
            # 构建完整的密钥URL
            if key_uri.startswith('http'):
                key_url = key_uri
            else:
                key_url = urljoin(base_url, key_uri)

            print(f"尝试获取密钥: {key_url}")

            # 下载密钥
            response = requests.get(key_url)
            if response.status_code == 200:
                key_data = response.content
                print(f"成功获取密钥，长度: {len(key_data)} 字节")
                print(f"密钥 (hex): {key_data.hex()}")
                return key_data
            else:
                print(f"密钥下载失败，状态码: {response.status_code}")

        except Exception as e:
            print(f"获取密钥时出错: {e}")

        # 尝试使用预设的ClassIn密钥
        print("尝试使用预设密钥...")
        for key in self.classin_keys:
            print(f"尝试密钥: {key}")
            # 确保密钥长度为16字节（AES-128）
            if len(key) < 16:
                key = key + b'\x00' * (16 - len(key))
            elif len(key) > 16:
                key = key[:16]
            return key

        return None

    def decrypt_ts_segment(self, segment_data, key, iv=None, method='AES-128'):
        """解密TS分段"""
        if not key:
            return segment_data

        try:
            # 处理IV
            if iv is None:
                # 如果没有提供IV，使用分段序号作为IV（HLS标准）
                iv = b'\x00' * 16
            elif isinstance(iv, str):
                iv = binascii.unhexlify(iv)

            # 确保IV长度为16字节
            if len(iv) < 16:
                iv = iv + b'\x00' * (16 - len(iv))
            elif len(iv) > 16:
                iv = iv[:16]

            print(f"解密参数 - 方法: {method}, 密钥长度: {len(key)}, IV长度: {len(iv)}")

            # AES解密
            if method in ['AES-128', 'AES-192', 'AES-256']:
                cipher = AES.new(key, AES.MODE_CBC, iv)

                # 确保数据长度是16的倍数
                if len(segment_data) % 16 != 0:
                    padding_length = 16 - (len(segment_data) % 16)
                    segment_data = segment_data + b'\x00' * padding_length

                decrypted_data = cipher.decrypt(segment_data)

                # 移除PKCS7填充
                try:
                    decrypted_data = unpad(decrypted_data, 16)
                except ValueError:
                    # 如果unpad失败，可能不是标准填充，直接返回
                    pass

                return decrypted_data

        except Exception as e:
            print(f"解密TS分段时出错: {e}")

        return segment_data

    def analyze_and_decrypt_stream(self, m3u8_path_or_url, output_dir='decrypted_stream'):
        """分析并解密整个流媒体"""
        print(f"\n=== 开始分析和解密流媒体 ===")

        # 分析M3U8文件
        m3u8_info = self.analyze_m3u8_file(m3u8_path_or_url)
        if not m3u8_info:
            return False

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 如果没有加密，直接返回
        if not m3u8_info['encryption']['is_encrypted']:
            print("流媒体未加密，无需解密")
            return True

        # 获取解密密钥
        encryption = m3u8_info['encryption']
        key = self.get_encryption_key(encryption['key_uri'], m3u8_info['base_url'])

        if not key:
            print("无法获取解密密钥")
            return False

        # 解密各个分段
        print(f"\n开始解密 {len(m3u8_info['segments'])} 个分段...")

        decrypted_segments = []
        for i, segment in enumerate(m3u8_info['segments']):
            print(f"解密分段 {i+1}/{len(m3u8_info['segments'])}: {segment['filename']}")

            try:
                # 下载分段
                response = requests.get(segment['url'])
                if response.status_code != 200:
                    print(f"下载分段失败: {segment['url']}")
                    continue

                segment_data = response.content
                print(f"分段大小: {len(segment_data)} 字节")

                # 生成IV（如果没有指定）
                iv = encryption['iv']
                if not iv and encryption['method'] in ['AES-128', 'AES-192', 'AES-256']:
                    # 使用分段序号作为IV（HLS标准做法）
                    iv = struct.pack('>QQ', 0, i)

                # 解密分段
                decrypted_data = self.decrypt_ts_segment(
                    segment_data, key, iv, encryption['method']
                )

                # 保存解密后的分段
                output_filename = f"segment_{i:04d}.ts"
                output_path = os.path.join(output_dir, output_filename)

                with open(output_path, 'wb') as f:
                    f.write(decrypted_data)

                decrypted_segments.append({
                    'original': segment,
                    'decrypted_path': output_path,
                    'success': True
                })

                print(f"分段解密成功: {output_path}")

            except Exception as e:
                print(f"解密分段时出错: {e}")
                decrypted_segments.append({
                    'original': segment,
                    'decrypted_path': None,
                    'success': False,
                    'error': str(e)
                })

        # 生成解密后的M3U8文件
        self.generate_decrypted_m3u8(m3u8_info, decrypted_segments, output_dir)

        success_count = sum(1 for seg in decrypted_segments if seg['success'])
        print(f"\n解密完成: {success_count}/{len(decrypted_segments)} 个分段成功")

        return success_count > 0

    def generate_decrypted_m3u8(self, m3u8_info, decrypted_segments, output_dir):
        """生成解密后的M3U8播放列表"""
        output_m3u8_path = os.path.join(output_dir, 'decrypted_playlist.m3u8')

        with open(output_m3u8_path, 'w', encoding='utf-8') as f:
            f.write("#EXTM3U\n")
            f.write("#EXT-X-VERSION:3\n")
            f.write("#EXT-X-TARGETDURATION:10\n")

            for i, seg_info in enumerate(decrypted_segments):
                if seg_info['success']:
                    duration = seg_info['original'].get('duration', 10.0)
                    filename = os.path.basename(seg_info['decrypted_path'])
                    f.write(f"#EXTINF:{duration},\n")
                    f.write(f"{filename}\n")

            f.write("#EXT-X-ENDLIST\n")

        print(f"生成解密后的M3U8文件: {output_m3u8_path}")

class ClassInDecryptor:
    def __init__(self):
        # 基于分析得出的可能密钥
        self.possible_keys = [
            b'classin_h5player_key_2023',  # 可能的密钥1
            b'eeo_encryption_key_v1.0.9',  # 基于版本信息
            b'cn.eeo.classin.h5player',    # 基于包名
        ]
        
    def xor_decode(self, data, key=0x10):
        """XOR解码 - 基于发现的混淆算法"""
        return bytes(b ^ key for b in data)
    
    def analyze_file_header(self, filepath):
        """分析文件头部结构"""
        print(f"\n=== 分析文件: {filepath} ===")
        
        if not os.path.exists(filepath):
            print(f"文件不存在: {filepath}")
            return None
            
        with open(filepath, 'rb') as f:
            # 读取文件头
            header = f.read(64)
            file_size = os.path.getsize(filepath)
            
            print(f"文件大小: {file_size} bytes")
            print("文件头分析:")
            
            # 检查是否有明显的加密标识
            magic_bytes = header[:4]
            print(f"Magic bytes: {magic_bytes.hex()}")
            
            # 尝试识别加密算法
            if len(header) >= 16:
                # 检查是否是AES块大小的倍数
                if file_size % 16 == 0:
                    print("文件大小是16的倍数，可能使用AES加密")
                
                # 分析可能的IV
                possible_iv = header[:16]
                print(f"可能的IV: {possible_iv.hex()}")
                
            return header
    
    def try_aes_decrypt(self, data, key, iv=None):
        """尝试AES解密"""
        try:
            # 确保密钥长度
            if len(key) < 32:
                key = hashlib.sha256(key).digest()
            elif len(key) > 32:
                key = key[:32]
            
            # 如果没有提供IV，使用文件头作为IV
            if iv is None:
                iv = data[:16]
                encrypted_data = data[16:]
            else:
                encrypted_data = data
            
            # 尝试不同的AES模式
            modes = [AES.MODE_CBC, AES.MODE_ECB]
            
            for mode in modes:
                try:
                    if mode == AES.MODE_ECB:
                        cipher = AES.new(key, mode)
                    else:
                        cipher = AES.new(key, mode, iv)
                    
                    decrypted = cipher.decrypt(encrypted_data)
                    
                    # 检查解密结果是否合理
                    if self.is_valid_decryption(decrypted):
                        return decrypted
                        
                except Exception as e:
                    continue
                    
        except Exception as e:
            pass
            
        return None
    
    def is_valid_decryption(self, data):
        """检查解密结果是否有效"""
        if len(data) < 10:
            return False
            
        # 检查是否包含常见的文件头
        common_headers = [
            b'<!DOCTYPE',  # HTML
            b'<html',      # HTML
            b'function',   # JavaScript
            b'var ',       # JavaScript
            b'(function',  # JavaScript
            b'PK',         # ZIP
            b'\x1f\x8b',   # GZIP
        ]
        
        for header in common_headers:
            if data.startswith(header):
                return True
                
        # 检查是否主要是可打印字符
        printable_count = sum(1 for b in data[:100] if 32 <= b <= 126 or b in [9, 10, 13])
        if printable_count > 80:  # 80%以上是可打印字符
            return True
            
        return False
    
    def decrypt_file(self, filepath, output_path=None):
        """尝试解密文件"""
        print(f"\n=== 尝试解密: {filepath} ===")
        
        if not os.path.exists(filepath):
            print(f"文件不存在: {filepath}")
            return False
            
        with open(filepath, 'rb') as f:
            data = f.read()
        
        # 分析文件头
        self.analyze_file_header(filepath)
        
        # 尝试不同的解密方法
        decrypted_data = None
        
        # 方法1: 简单XOR
        print("\n尝试XOR解密...")
        xor_result = self.xor_decode(data)
        if self.is_valid_decryption(xor_result):
            print("XOR解密成功!")
            decrypted_data = xor_result
        
        # 方法2: AES解密
        if decrypted_data is None:
            print("\n尝试AES解密...")
            for i, key in enumerate(self.possible_keys):
                print(f"尝试密钥 {i+1}: {key}")
                result = self.try_aes_decrypt(data, key)
                if result:
                    print(f"AES解密成功! 使用密钥: {key}")
                    decrypted_data = result
                    break
        
        # 方法3: 组合解密 (先AES后XOR)
        if decrypted_data is None:
            print("\n尝试组合解密 (AES + XOR)...")
            for key in self.possible_keys:
                aes_result = self.try_aes_decrypt(data, key)
                if aes_result:
                    xor_result = self.xor_decode(aes_result)
                    if self.is_valid_decryption(xor_result):
                        print("组合解密成功!")
                        decrypted_data = xor_result
                        break
        
        # 保存解密结果
        if decrypted_data:
            if output_path is None:
                output_path = filepath.replace('.crypt', '_decrypted')
            
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            
            print(f"解密文件已保存: {output_path}")
            
            # 显示解密内容的前几行
            try:
                preview = decrypted_data[:500].decode('utf-8', errors='ignore')
                print(f"\n解密内容预览:\n{preview}")
            except:
                print(f"\n解密内容 (hex): {decrypted_data[:100].hex()}")
            
            return True
        else:
            print("解密失败!")
            return False

def main():
    """主函数"""
    print("ClassIn 流媒体加密分析工具")
    print("=" * 60)
    print("1. .crypt文件解密")
    print("2. M3U8流媒体分析和解密")
    print("=" * 60)

    # M3U8流媒体分析
    print("\n【M3U8流媒体分析】")
    m3u8_analyzer = M3U8StreamAnalyzer()

    # 示例M3U8文件分析（如果存在）
    sample_m3u8_files = [
        'test.m3u8',
        'playlist.m3u8',
        'stream.m3u8'
    ]

    found_m3u8 = False
    for m3u8_file in sample_m3u8_files:
        if os.path.exists(m3u8_file):
            print(f"发现M3U8文件: {m3u8_file}")
            m3u8_analyzer.analyze_and_decrypt_stream(m3u8_file)
            found_m3u8 = True
            break

    if not found_m3u8:
        print("未发现本地M3U8文件，显示分析示例...")

        # 创建一个示例M3U8内容进行分析
        sample_m3u8_content = """#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXT-X-KEY:METHOD=AES-128,URI="https://example.com/key.bin",IV=0x12345678901234567890123456789012
#EXTINF:10.0,
segment001.ts
#EXTINF:10.0,
segment002.ts
#EXT-X-ENDLIST"""

        print("示例M3U8内容分析:")
        encryption_info = m3u8_analyzer.parse_encryption_info(sample_m3u8_content)
        print(f"加密信息: {encryption_info}")

    print("\n" + "=" * 60)

    # .crypt文件解密
    print("\n【.crypt文件解密】")
    decryptor = ClassInDecryptor()

    # 要解密的文件列表
    files_to_decrypt = [
        'assets/h5player.crypt',
        'assets/h5player_v10.crypt',
        'assets/codeEditorBundle.crypt'
    ]

    for filepath in files_to_decrypt:
        if os.path.exists(filepath):
            decryptor.decrypt_file(filepath)
        else:
            print(f"文件不存在: {filepath}")

    print("\n" + "=" * 60)
    print("分析完成！")

    # 提供使用说明
    print("\n使用说明:")
    print("1. 对于M3U8流媒体:")
    print("   - 将M3U8文件放在当前目录")
    print("   - 或者修改代码中的URL进行在线分析")
    print("2. 对于.crypt文件:")
    print("   - 确保文件在assets目录下")
    print("   - 工具会自动尝试多种解密方法")

if __name__ == "__main__":
    main()
