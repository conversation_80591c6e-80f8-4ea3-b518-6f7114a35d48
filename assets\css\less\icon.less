.icon{
    position: relative;
    display: inline-block;
    width: 24px;
    height: 24px;
    top: 7px;
    margin-top: -12px;
    background-repeat: no-repeat;
    background-position: center center;
    &.pencil{
        background-image: url(../images/icon_pencil.png);
    }
    &.checkmark{
        background-image: url(../images/icon_checkmark.png);
    }
    &.mark_right{
        background-image: url(../images/icon_mark_right.png);
    }
    &.clock_gray{
        background-image: url(../images/icon_clock_gray.png);
    }
    &.prize_gray{
        background-image: url(../images/icon_prize_gray.png);
    }
}

@-webkit-keyframes iconload{
    0%{
        background-position: 0 0;;
    }
    100%{
        background-position: -280px 0;
    }
}
.icon_load{
	width: 40px;
	height: 40px;
	background-image: url(../images/icon_loading.png);
	background-position: 0 0;
	opacity: .7;
	-webkit-animation: iconload .5s steps(7) infinite;
}
//@-webkit-keyframes tusiji {
//0% {background-position:0px -0%;}
//100% {background-position:0px -400%;}
//}
//div.tusiji {
//	height: 35px;
//	width: 32px;
//	-webkit-animation: tusiji 400ms steps(4) infinite;
//	background: url("http://www.web-tinker.com/share/兔斯基揉脸.png");
//}