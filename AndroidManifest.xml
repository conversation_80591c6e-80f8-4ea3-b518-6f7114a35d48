<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" android:compileSdkVersion="35" android:compileSdkVersionCodename="15" package="cn.eeo.classin" platformBuildVersionCode="35" platformBuildVersionName="15">
    <uses-feature android:name="android.hardware.camera"/>
    <uses-feature android:name="android.hardware.camera.autofocus"/>
    <uses-permission android:maxSdkVersion="30" android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:maxSdkVersion="28" android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.READ_CONTACTS"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:maxSdkVersion="32" android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS"/>
    <uses-permission android:name="com.bbk.launcher2.permission.ENTER_VIRTUAL_SYSTEM"/>
    <uses-permission android:name="com.hzsoft.permissiom.MDM"/>
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL"/>
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE"/>
    <uses-permission android:name="com.hihonor.android.launcher.permission.CHANGE_BADGE"/>
    <uses-feature android:glEsVersion="0x00020000" android:required="true"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.RUN_USER_INITIATED_JOBS"/>
    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT"/>
    <queries>
        <package android:name="com.guoshi.httpcanary.premium"/>
        <package android:name="com.tencent.mm"/>
        <package android:name="com.tencent.mobileqq"/>
        <package android:name="com.sina.weibo"/>
        <package android:name="com.facebook.katana"/>
        <package android:name="com.twitter.android"/>
        <package android:name="com.linkedin.android"/>
        <package android:name="com.whatsapp"/>
        <package android:name="com.android.mms"/>
        <package android:name="com.oneplus.mms"/>
        <package android:name="com.eg.android.AlipayGphone"/>
        <package android:name="com.eg.android.AlipayGphoneRC"/>
        <package android:name="hk.alipay.wallet"/>
        <intent>
            <action android:name="android.intent.action.VIEW"/>
            <data android:scheme="http"/>
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW"/>
            <category android:name="android.intent.category.BROWSABLE"/>
            <data android:scheme="https"/>
        </intent>
        <package android:name="com.android.chrome"/>
        <package android:name="com.google.android.apps.maps"/>
        <intent>
            <action android:name="com.hihonor.push.action.BIND_PUSH_SERVICE"/>
        </intent>
        <intent>
            <action android:name="*"/>
            <data android:scheme="tbopen"/>
        </intent>
        <intent>
            <action android:name="com.huawei.hms.core.aidlservice"/>
        </intent>
        <intent>
            <action android:name="com.huawei.hms.core"/>
        </intent>
        <package android:name="com.huawei.hff"/>
        <package android:name="com.huawei.hms"/>
        <package android:name="com.huawei.hwid"/>
        <package android:name="com.huawei.hwid.tv"/>
        <package android:name="com.huawei.works"/>
    </queries>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.USE_BIOMETRIC"/>
    <uses-permission android:name="android.permission.USE_FINGERPRINT"/>
    <permission android:name="cn.eeo.classin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" android:protectionLevel="signature"/>
    <uses-permission android:name="cn.eeo.classin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"/>
    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE"/>
    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE"/>
    <permission android:name="cn.eeo.classin.permission.MIPUSH_RECEIVE" android:protectionLevel="signature"/>
    <uses-permission android:name="cn.eeo.classin.permission.MIPUSH_RECEIVE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE"/>
    <supports-screens android:anyDensity="true" android:largeScreens="true" android:normalScreens="true" android:resizeable="true" android:smallScreens="true"/>
    <uses-permission android:name="android.permission.FLASHLIGHT"/>
    <uses-feature android:name="android.hardware.camera.flash" android:required="false"/>
    <permission android:name="cn.eeo.classin.permission.PROCESS_PUSH_MSG" android:protectionLevel="signature"/>
    <permission android:name="cn.eeo.classin.permission.PUSH_PROVIDER" android:protectionLevel="signature"/>
    <permission android:name="cn.eeo.classin.permission.PUSH_WRITE_PROVIDER" android:protectionLevel="signature"/>
    <uses-permission android:name="cn.eeo.classin.permission.PROCESS_PUSH_MSG"/>
    <uses-permission android:name="cn.eeo.classin.permission.PUSH_PROVIDER"/>
    <uses-permission android:name="com.hihonor.push.permission.READ_PUSH_NOTIFICATION_INFO"/>
    <application android:allowAudioPlaybackCapture="true" android:allowBackup="true" android:appComponentFactory="androidx.core.app.CoreComponentFactory" android:configChanges="orientation" android:extractNativeLibs="true" android:hardwareAccelerated="true" android:hasFragileUserData="true" android:icon="@mipmap/ic_launcher" android:label="@string/app_name" android:largeHeap="true" android:name="com.stub.StubApp" android:networkSecurityConfig="@xml/network_security_config" android:requestLegacyExternalStorage="true" android:resizeableActivity="false" android:roundIcon="@mipmap/ic_launcher_round" android:supportsRtl="false" android:theme="@style/AppTheme" android:usesCleartextTraffic="true">
        <activity android:name="cn.eeo.classin.ui.module.main.GrowthActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="singleTask" android:name="cn.eeo.classin.ui.module.main.MainActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="singleTask" android:name="cn.eeo.classin.ui.module.main.PadMainActivity" android:screenOrientation="landscape"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTask" android:name="com.eeo.eophotoedit.HomeWorkImageEditorActivity" android:screenOrientation="portrait" android:theme="@style/EditorTheme"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:name="cn.eeo.classin.ui.module.common.EOWebViewActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize" android:name="com.yalantis.ucrop.UCropActivity" android:screenOrientation="behind" android:theme="@style/AppTheme"/>
        <activity android:exported="true" android:name="cn.eeo.classin.ui.module.main.ReceiveSharePreActivity">
            <intent-filter>
                <action android:name="android.intent.action.SEND"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.presentation"/>
                <data android:mimeType="application/vnd.ms-powerpoint"/>
                <data android:mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.document"/>
                <data android:mimeType="application/msword"/>
                <data android:mimeType="application/excel"/>
                <data android:mimeType="application/x-msexcel"/>
                <data android:mimeType="application/vnd.ms-excel"/>
                <data android:mimeType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
                <data android:mimeType="text/comma-separated-values"/>
                <data android:mimeType="application/pdf"/>
                <data android:mimeType="audio/*"/>
                <data android:mimeType="video/*"/>
                <data android:mimeType="image/*"/>
                <data android:mimeType="text/*"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <data android:mimeType="application/vnd.openxmlformats-officedocument.presentationml.presentation"/>
                <data android:mimeType="application/vnd.ms-powerpoint"/>
                <data android:mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.document"/>
                <data android:mimeType="application/msword"/>
                <data android:mimeType="application/excel"/>
                <data android:mimeType="application/x-msexcel"/>
                <data android:mimeType="application/vnd.ms-excel"/>
                <data android:mimeType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
                <data android:mimeType="text/comma-separated-values"/>
                <data android:mimeType="application/pdf"/>
                <data android:mimeType="audio/*"/>
                <data android:mimeType="video/*"/>
                <data android:mimeType="image/*"/>
                <data android:mimeType="text/*"/>
            </intent-filter>
        </activity>
        <activity android:configChanges="keyboardHidden|orientation" android:label="@string/app_name" android:launchMode="singleTask" android:name="cn.eeo.classin.ui.module.main.ReceiveSharingActivity" android:screenOrientation="behind"/>
        <meta-data android:name="io.sentry.auto-init" android:value="false"/>
        <meta-data android:name="io.sentry.traces.activity.auto-finish.enable" android:value="true"/>
        <meta-data android:name="io.sentry.anr.timeout-interval-millis" android:value="5000"/>
        <meta-data android:name="android.max_aspect" android:value="2.4"/>
        <meta-data android:name="EasyGoClient" android:value="true"/>
        <meta-data android:name="LandscapeForPad" android:value="true"/>
        <meta-data android:name="VivoMultiWindow" android:value="true"/>
        <meta-data android:name="UMENG_CHANNEL" android:value="OFFICIAL"/>
        <meta-data android:name="MIPUSH_APPID" android:value="\ 2882303761517855282"/>
        <meta-data android:name="MIPUSH_APPKEY" android:value="\ 5681785598282"/>
        <meta-data android:name="com.vivo.push.api_secret" android:value="8561bb27-bb21-4c41-a21b-1b0e44062100"/>
        <meta-data android:name="com.vivo.push.api_key" android:value="6b60ded8-492f-44e0-a83d-767a06a4c76c"/>
        <meta-data android:name="com.vivo.push.app_id" android:value="18599"/>
        <meta-data android:name="com.oppo.push.api_secret" android:value="16a4eB1B2a24EF1A218a7419513212C8"/>
        <meta-data android:name="com.oppo.push.api_key" android:value="9Guum8LmWbgG8o0o0s08gsW04"/>
        <meta-data android:name="com.oppo.push.app_id" android:value="3682761"/>
        <provider android:authorities="cn.eeo.classin.file.provider" android:exported="false" android:grantUriPermissions="true" android:name="androidx.core.content.FileProvider">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/boxing_file_provider"/>
        </provider>
        <receiver android:exported="false" android:name="ezy.sdk3rd.social.platforms.twitter.TwitterResultReceiver">
            <intent-filter>
                <action android:name="com.twitter.sdk.android.tweetcomposer.UPLOAD_SUCCESS"/>
                <action android:name="com.twitter.sdk.android.tweetcomposer.UPLOAD_FAILURE"/>
                <action android:name="com.twitter.sdk.android.tweetcomposer.TWEET_COMPOSE_CANCEL"/>
            </intent-filter>
        </receiver>
        <uses-library android:name="org.apache.http.legacy" android:required="false"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.homework.ui.HomeWorkActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.homework.ui.HomeWorkDataExportActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.exam.CreateExamActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.exam.ExamViewActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.exam.ExamAnswerActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.homework.ui.HomeWorkSelectImageActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.homework.ui.GrowthHomeworkActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize" android:name="cn.eeo.homework.ui.HomeWorkImageEditorActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize" android:exported="false" android:name="cn.eeo.homework.ui.ExamImageEditorActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize" android:exported="false" android:name="cn.eeo.homework.ui.ClockImageEditorActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTop" android:name="cn.eeo.lms.lesson.LmsCreateLessonActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity" android:windowSoftInputMode="stateAlwaysHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTop" android:name="cn.eeo.lms.ui.activity.CreateUnitActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateVisible"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTop" android:name="cn.eeo.lms.ui.activity.UpdateUnitActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateVisible"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.material.CreateMaterialActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateVisible"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.discuss.CreateDiscussActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateVisible"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.ui.activity.TeachingActivityCreateActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateVisible"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTop" android:name="cn.eeo.lms.ui.activity.TeachingActivityPublishActivity" android:screenOrientation="portrait" android:windowSoftInputMode="stateAlwaysHidden"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize" android:name="cn.eeo.lms.ui.activity.TeachingActivityDetailActivity" android:screenOrientation="behind" android:windowSoftInputMode="stateAlwaysHidden|adjustPan"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.base.CommonViewActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.gauge.GaugeCommonActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.gauge.AbilityModelDetailActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.gauge.SelectCompetencyActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.ui.activity.TeacherReviewActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.record.CreateRecordActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateVisible"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.appraise.AppraiseSettingActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.ui.activity.TeachingActivityStudentViewActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.score.ScoreDetailActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.homework.HomeworkDataActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.answersheet.AnswerSheetActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.clock.ClockRankActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.clock.ClockStudentShareActivity" android:screenOrientation="portrait" android:theme="@style/ActivityTransparentStyle" android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.ui.activity.ShareSocialActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.ui.activity.WebViewWindowActivity" android:screenOrientation="portrait" android:theme="@style/ActivityWebViewWindowStyle" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.lms.coursedetail.activity.CourseDetailActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <meta-data android:name="cn.eeo.router.RouterInitializer" android:value="cn.eeo.initialize"/>
        <activity android:exported="false" android:name="cn.eeo.startup.ui.ChooseBindAccountActivity" android:screenOrientation="portrait"/>
        <activity android:exported="false" android:name="cn.eeo.startup.ui.PasswordInputActivity" android:screenOrientation="portrait"/>
        <activity android:exported="false" android:name="cn.eeo.startup.ui.VerifyCodePreActivity" android:screenOrientation="portrait"/>
        <activity android:exported="true" android:name="cn.eeo.startup.ui.LogoSplashActivity" android:theme="@style/SplashTheme" android:windowSoftInputMode="stateHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.CREATE_SHORTCUT"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="deeplink" android:scheme="classin"/>
            </intent-filter>
        </activity>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.startup.ui.IntroduceActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTask" android:name="cn.eeo.startup.ui.LoginActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.startup.ui.SelectCountryCodeActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.startup.ui.VerifyCodeInputActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.startup.ui.SimpleLoginActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.startup.ui.ClassInSchemeFromActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:exported="true" android:label="@string/app_name" android:launchMode="singleTask" android:name="cn.eeo.startup.ui.ClassInSchemeActivity" android:screenOrientation="portrait" android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="www.eeo.cn" android:pathPrefix="/enterclass" android:scheme="classin"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="www.eeo.cn" android:pathPrefix="/s" android:scheme="https"/>
            </intent-filter>
        </activity>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="singleTask" android:name="cn.eeo.startup.newui.LoginActivity" android:screenOrientation="behind" android:windowSoftInputMode="adjustNothing|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:exported="false" android:name="cn.eeo.account.SelectVersionWebActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.settings.InfoDetailActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.settings.PersonalInventoryActivity" android:screenOrientation="portrait"/>
        <activity android:name="cn.eeo.account.settings.SecrecyPermissionActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.ResetSecurityPasswordActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:name="cn.eeo.account.AuthInfoSubmitActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.SetNewPhoneActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.AuthenticationActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.AccountHintActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.AboutClassInActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.SignatureActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTask" android:name="cn.eeo.account.CompleteInformationActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="orientation" android:name="cn.eeo.account.DeviceManagerActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.FeedBackActivity" android:screenOrientation="portrait" android:windowSoftInputMode="stateHidden"/>
        <activity android:configChanges="orientation" android:launchMode="singleTask" android:name="cn.eeo.account.MyQrCodeActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.PersonalInfoActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.PersonalInfoCommonActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.PersonalSettingActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.AccountActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.AccountBindActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.PhotoViewActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="orientation" android:name="cn.eeo.account.QRCodeLoginActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.SelectCityActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.SelectCountryActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.SelectStateActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.SettingActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.VerifiedAccountActivity" android:screenOrientation="portrait"/>
        <activity android:name="cn.eeo.account.ModifyInfoActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.phoneaccount.UpdatePasswordActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.settings.PushManagerActivity" android:screenOrientation="portrait"/>
        <activity android:name="cn.eeo.account.settings.SecrecyActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.QrScannerActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.account.AdvancedSettingsActivity" android:screenOrientation="portrait"/>
        <activity android:name="cn.eeo.account.settings.PermissionDetailActivity" android:screenOrientation="portrait"/>
        <activity android:exported="true" android:name="cn.eeo.account.YoungModeActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:name="cn.eeo.account.VersionSelectionActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:name="cn.eeo.account.RechargeActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:name="cn.eeo.account.PersonalVersionAdActivity" android:screenOrientation="portrait" android:theme="@style/TranslucentStyle"/>
        <activity android:configChanges="keyboardHidden|orientation" android:exported="false" android:name="cn.eeo.cluster.classin.ui.FragmentContainerActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.contacts.ui.SearchActivity" android:screenOrientation="portrait" android:windowSoftInputMode="stateVisible"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.contacts.ui.CreateClassActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.contacts.ui.AddFriendsActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="orientation" android:name="cn.eeo.cluster.contacts.ui.PhoneContactActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTop" android:name="cn.eeo.cluster.contacts.ui.PersonalDetailActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity" android:windowSoftInputMode="stateAlwaysHidden|adjustPan"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTop" android:name="cn.eeo.cluster.contacts.ui.AssistantPersonalDetailActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.contacts.ui.PersonDetailMoreActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.contacts.ui.AssistantPersonalDetailMoreActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="orientation" android:name="cn.eeo.cluster.contacts.ui.ModifyNameActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.contacts.ui.VerifyMessageActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity" android:windowSoftInputMode="stateVisible"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTask" android:name="cn.eeo.cluster.contacts.ui.MessageCenterActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.classin.ui.NetDisableActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="singleTask" android:name="cn.eeo.cluster.classin.ui.ChatRoomActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity" android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTask" android:name="cn.eeo.cluster.classin.ui.AssistantChatRoomActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.contacts.ui.ClassInfoActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.classin.ui.TransponderTargetActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.contacts.ui.ClassSelectActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.contacts.ui.ClassShareInfoActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.classin.ui.ClassMembersActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.contacts.ui.ClassInfoManagementActivity" android:screenOrientation="portrait"/>
        <activity android:name="cn.eeo.cluster.contacts.ui.ClassEntryActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.contacts.ui.ClassMemberDeleteActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTop" android:name="cn.eeo.cluster.contacts.ui.ClassCardInfoActivity" android:screenOrientation="portrait" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="orientation" android:name="cn.eeo.cluster.contacts.ui.ClassInfoChangeAvatarActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="orientation" android:name="cn.eeo.cluster.classin.ui.FakeChatRoomActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTask" android:name="cn.eeo.cluster.classin.ui.MediaRecordActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cluster.classin.ui.PersonSimpleActivity" android:screenOrientation="portrait" android:windowSoftInputMode="stateAlwaysHidden"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize|uiMode" android:name="cn.eeo.cluster.contacts.ui.SelectSchoolContactActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize|uiMode" android:launchMode="singleTop" android:name="cn.eeo.cluster.classin.ui.PostNoticeActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity" android:windowSoftInputMode="adjustNothing|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize|uiMode" android:name="cn.eeo.cluster.classin.ui.NoticeDetailActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize|uiMode" android:name="cn.eeo.cluster.classin.ui.ClassNoRestrictionActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize|uiMode" android:launchMode="singleInstance" android:name="cn.eeo.cluster.classin.ui.StudyRoomActivity" android:screenOrientation="landscape" android:taskAffinity=".studyRoom"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTask" android:name="cn.eeo.cluster.classin.ui.NoticeListActivity" android:screenOrientation="portrait"/>
        <activity android:exported="false" android:name="cn.eeo.cloud.select.SelectSpaceFolderActivity" android:screenOrientation="behind"/>
        <activity android:exported="false" android:name="cn.eeo.cloud.select.SelectSpaceFileActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:exported="false" android:name="cn.eeo.cloud.res_mall.ResMallMineActivity"/>
        <activity android:exported="false" android:name="cn.eeo.clouddisk.ui.PreviewFileDetailTransitionActivity" android:screenOrientation="behind"/>
        <activity android:name="cn.eeo.clouddisk.ui.ShareEDocTeamListActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:name="cn.eeo.clouddisk.ui.ShareEDocActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.clouddisk.ui.CloudActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cloud.file.CloudActivity" android:screenOrientation="behind" android:windowSoftInputMode="adjustNothing"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.clouddisk.ui.SelectCloudFileActivity" android:theme="@style/Theme.DialogActivity"/>
        <activity android:name="cn.eeo.clouddisk.ui.SelectFolderActivity"/>
        <activity android:name="cn.eeo.cloud.select.NewSelectFolderActivity"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTop" android:name="cn.eeo.clouddisk.ui.PhotoPreviewActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize|uiMode" android:launchMode="singleTop" android:name="cn.eeo.clouddisk.ui.PreviewFileDetailActivity"/>
        <activity android:name="cn.eeo.clouddisk.ui.AuthorizeActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cloud.authorize.AuthorizeActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cloud.res_mall.ResourceMallActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.clouddisk.ui.SelectCloudDiskPathActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize|uiMode" android:launchMode="singleTop" android:name="cn.eeo.clouddisk.ui.NativeVideoPreviewActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTask" android:name="cn.eeo.clouddisk.ui.FileTypeAudioActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTask" android:name="cn.eeo.cloud.select.SelectCloudActivity" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.cloud.organization.common.OrganizationCommonActivity" android:screenOrientation="behind" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <meta-data android:name="cn.eeo.cloud.CloudInitializer" android:value="cn.eeo.initialize"/>
        <receiver android:exported="false" android:name="cn.eeo.clouddisk.receiver.AudioPlayerReceiver">
            <intent-filter>
                <action android:name="eeo.play_audio.close"/>
                <action android:name="eeo.play_audio.pause"/>
                <action android:name="eeo.play_audio.play"/>
                <action android:name="eeo.play_audio.stop"/>
            </intent-filter>
        </receiver>
        <service android:foregroundServiceType="mediaPlayback" android:name="cn.eeo.clouddisk.service.AudioPlayerService"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.ReportImageShareActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.CourseListActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.CreatePublicCourseActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity" android:windowSoftInputMode="stateAlwaysHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.LessonDetailActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity" android:windowSoftInputMode="stateAlwaysHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.LessonDetailShareActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity" android:windowSoftInputMode="stateAlwaysHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.ChooseAssistTeacherActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity" android:windowSoftInputMode="stateAlwaysHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.CoursesActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.SelectLessonStudentDetailActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity" android:windowSoftInputMode="stateAlwaysHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTop" android:name="cn.eeo.course.ui.CreateCourseActivity" android:screenOrientation="portrait" android:theme="@style/Theme.DialogActivity" android:windowSoftInputMode="stateAlwaysHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:launchMode="singleTask" android:name="cn.eeo.course.ui.ChooseTeacherActivity" android:screenOrientation="behind" android:theme="@style/Theme.DialogActivity"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.LearnReportActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.LessonRecordVideoActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.GrowthNotesActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="singleTop" android:name="cn.eeo.course.ui.LessonRecordVideoPlayActivity" android:screenOrientation="landscape"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.GrowthSearchCourseActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.GrowthCourseActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.course.ui.TeachModeActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.microlecture.ui.MicroLectureActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.microlecture.ui.MicroLectureCollectionDetailActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.microlecture.ui.MicroLectureDraftsActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.microlecture.ui.CreateMicroLectureCollectionActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.microlecture.ui.CreateMicroLectureActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:name="cn.eeo.microlecture.ui.MicroLecturePreviewActivity"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:name="cn.eeo.microlecture.ui.MicroLectureDetailActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.microlecture.ui.ChooseCollectionActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.microlecture.ui.MicroLectureSortActivity" android:screenOrientation="portrait"/>
        <activity android:name="cn.eeo.microlecture.ui.MicroLectureCollectionIntroActivity" android:screenOrientation="portrait" android:windowSoftInputMode="stateAlwaysVisible"/>
        <activity android:configChanges="keyboardHidden|orientation" android:name="cn.eeo.microlecture.ui.MicroLectureCollectionInviteActivity" android:screenOrientation="portrait"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:name="cn.eeo.microlecture.ui.CourseCutActivity" android:screenOrientation="portrait" android:windowSoftInputMode="stateAlwaysVisible"/>
        <activity android:name="cn.eeo.microlecture.ui.UploadVideoActivity" android:screenOrientation="portrait" android:windowSoftInputMode="stateAlwaysVisible"/>
        <activity android:exported="false" android:name="cn.eeo.tablet.ui.activity.PadDialogStyleActivity" android:screenOrientation="landscape" android:theme="@style/Theme.DialogActivity" android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:exported="false" android:name="cn.eeo.tablet.ui.activity.PadPortraitActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
        <meta-data android:name="cn.eeo.ClassroomServiceInitializer" android:value="cn.eeo.initialize"/>
        <meta-data android:name="cn.eeo.CloudSpaceServiceInitializer" android:value="cn.eeo.initialize"/>
        <meta-data android:name="cn.eeo.CourseServiceInitializer" android:value="cn.eeo.initialize"/>
        <meta-data android:name="cn.eeo.ClusterServiceInitializer" android:value="cn.eeo.initialize"/>
        <meta-data android:name="cn.eeo.MicroLectureServiceInitializer" android:value="cn.eeo.initialize"/>
        <service android:name="cn.eeo.file.uploader.service.FileUploadJobService" android:permission="android.permission.BIND_JOB_SERVICE"/>
        <meta-data android:name="cn.eeo.file.FileInitializer" android:value="cn.eeo.initialize"/>
        <meta-data android:name="cn.eeo.protocol.ProtocolInitializer" android:value="cn.eeo.initialize"/>
        <meta-data android:name="cn.eeo.NetworkServiceInitializer" android:value="cn.eeo.initialize"/>
        <provider android:authorities="cn.eeo.classin.file.provider" android:exported="false" android:grantUriPermissions="true" android:name="cn.eeo.commonview.OpenFileProvider">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/filepaths"/>
        </provider>
        <service android:name="com.vector.update_app.service.DownloadService"/>
        <provider android:authorities="cn.eeo.classin.fileProvider" android:exported="false" android:grantUriPermissions="true" android:name="com.vector.update_app.UpdateFileProvider">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/new_app_file_paths"/>
        </provider>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize" android:launchMode="singleTop" android:name="com.bilibili.boxing_impl.ui.BoxingActivity" android:screenOrientation="behind" android:theme="@style/Boxing.AppTheme.NoActionBar"/>
        <activity android:launchMode="singleTop" android:name="com.bilibili.boxing_impl.ui.BoxingViewActivity" android:screenOrientation="behind" android:theme="@style/Boxing.AppTheme.NoActionBar"/>
        <activity android:name="com.bilibili.boxing_impl.ui.BoxingBottomSheetActivity" android:screenOrientation="behind" android:theme="@style/Boxing.AppTheme.NoActionBar"/>
        <activity android:launchMode="singleTask" android:name="com.eeo.eophotoedit.TextInputActivity" android:theme="@style/EditorTheme.EditorInputTheme" android:windowSoftInputMode="stateAlwaysVisible|adjustResize"/>
        <receiver android:exported="false" android:name="cn.eeo.widgetlib.widget.CourseSmallWidget">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data android:name="android.appwidget.provider" android:resource="@xml/course_small_widget_info"/>
        </receiver>
        <receiver android:exported="false" android:name="cn.eeo.widgetlib.widget.CourseMediumWidget">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
                <action android:name="cn.eeo.widget.action.REQUEST_COURSE_LIST"/>
            </intent-filter>
            <meta-data android:name="android.appwidget.provider" android:resource="@xml/course_medium_widget_info"/>
        </receiver>
        <service android:enabled="true" android:exported="true" android:foregroundServiceType="shortService" android:name="cn.eeo.widgetlib.support.HttpService"/>
        <meta-data android:name="cn.eeo.json.JsonInitializer" android:value="cn.eeo.initialize"/>
        <meta-data android:name="cn.eeo.tools.ToolsInitializer" android:value="cn.eeo.initialize"/>
        <service android:name="cn.eeo.fileupload.service.UploadFileJobService" android:permission="android.permission.BIND_JOB_SERVICE"/>
        <meta-data android:name="cn.eeo.widget.theme.initialize.ThemeInitializer" android:value="cn.eeo.initialize"/>
        <service android:name="cn.eeo.offline.service.OfflineResJobService" android:permission="android.permission.BIND_JOB_SERVICE"/>
        <meta-data android:name="cn.eeo.offline.OfflineResInitializer" android:value="cn.eeo.initialize"/>
        <meta-data android:name="cn.eeo.liveroom.LiveRoomInitializer" android:value="cn.eeo.initialize"/>
        <activity android:configChanges="fontScale|fontWeightAdjustment|keyboard|keyboardHidden|layoutDirection|locale|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:launchMode="singleTask" android:name="cn.eeo.liveroom.ClassRoomActivity" android:screenOrientation="landscape" android:theme="@style/FullScreenTheme" android:windowSoftInputMode="adjustNothing"/>
        <activity android:configChanges="keyboard|keyboardHidden|navigation|orientation|screenSize" android:launchMode="singleTask" android:name="cn.eeo.liveroom.EvaluateActivity" android:screenOrientation="landscape" android:theme="@style/FullScreenTheme" android:windowSoftInputMode="adjustNothing"/>
        <activity android:configChanges="keyboard|keyboardHidden|navigation|orientation|screenSize" android:launchMode="singleTask" android:name="cn.eeo.liveroom.ClassEdbActivity" android:screenOrientation="landscape" android:theme="@style/FullScreenTheme" android:windowSoftInputMode="adjustNothing"/>
        <activity android:configChanges="keyboard|keyboardHidden|navigation|orientation|screenSize" android:launchMode="singleTask" android:name="cn.eeo.liveroom.CheckInActivity" android:screenOrientation="landscape" android:theme="@style/FullScreenTheme" android:windowSoftInputMode="adjustNothing"/>
        <activity android:configChanges="keyboard|keyboardHidden|navigation|orientation|screenSize" android:launchMode="singleTask" android:name="cn.eeo.liveroom.ClassRoomDelegateActivity" android:theme="@style/OverlayActivity"/>
        <activity android:configChanges="keyboard|keyboardHidden|navigation|orientation|screenSize" android:launchMode="singleTop" android:name="cn.eeo.liveroom.widget.overlay.OverlayPermissionActivity" android:screenOrientation="landscape"/>
        <service android:name="cn.eeo.liveroom.widget.overlay.OverlayService"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:exported="true" android:launchMode="singleInstance" android:name="ezy.sdk3rd.social.platforms.weixin.WXCallbackActivity" android:taskAffinity="cn.eeo.classin" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <activity-alias android:exported="true" android:name="cn.eeo.classin.wxapi.WXEntryActivity" android:targetActivity="ezy.sdk3rd.social.platforms.weixin.WXCallbackActivity" android:taskAffinity="cn.eeo.classin"/>
        <activity-alias android:exported="true" android:name="cn.eeo.classin.wxapi.WXPayEntryActivity" android:targetActivity="ezy.sdk3rd.social.platforms.weixin.WXCallbackActivity" android:taskAffinity="cn.eeo.classin"/>
        <activity android:exported="true" android:launchMode="singleTask" android:name="com.tencent.tauth.AuthActivity" android:noHistory="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="tencent1106915945"/>
            </intent-filter>
        </activity>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:name="com.tencent.connect.common.AssistActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <activity android:exported="false" android:name="com.stripe.android.paymentsheet.PaymentSheetActivity" android:theme="@style/StripePaymentSheetDefaultTheme" android:windowSoftInputMode="adjustResize"/>
        <activity android:exported="false" android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity" android:theme="@style/StripePaymentSheetDefaultTheme" android:windowSoftInputMode="adjustResize"/>
        <activity android:exported="false" android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity" android:theme="@style/StripePaymentSheetDefaultTheme"/>
        <activity android:exported="false" android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity" android:theme="@style/StripePaymentSheetDefaultTheme"/>
        <activity android:exported="false" android:launchMode="singleTop" android:name="com.paypal.pyplcheckout.home.view.activities.PYPLHomeActivity" android:screenOrientation="portrait" android:taskAffinity="com.pyplcheckout.task" android:theme="@style/PYPLAppTheme"/>
        <activity android:exported="true" android:launchMode="singleTask" android:name="com.paypal.pyplcheckout.home.view.activities.PYPLInitiateCheckoutActivity" android:screenOrientation="portrait" android:theme="@style/AppFullScreenTheme">
            <intent-filter android:autoVerify="true">
                <data android:host="paypalxo" android:scheme="cn.eeo.classin"/>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
            </intent-filter>
        </activity>
        <activity android:excludeFromRecents="true" android:exported="true" android:name="com.paypal.openid.RedirectUriReceiverActivity" android:screenOrientation="portrait" android:theme="@style/PYPLAppTheme">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="paypalpay" android:scheme="nativexo"/>
            </intent-filter>
        </activity>
        <activity android:exported="false" android:launchMode="singleTop" android:name="com.paypal.pyplcheckout.threeds.ThreeDS20Activity" android:screenOrientation="portrait" android:taskAffinity="com.pyplcheckout.threeds" android:theme="@style/PYPLAppTheme"/>
        <activity android:exported="false" android:label="@string/link" android:name="com.stripe.android.link.LinkActivity" android:theme="@style/LinkBaseTheme" android:windowSoftInputMode="adjustResize"/>
        <activity android:exported="true" android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="link-accounts" android:pathPrefix="/cn.eeo.classin/authentication_return" android:scheme="stripe-auth"/>
                <data android:host="link-native-accounts" android:pathPrefix="/cn.eeo.classin/authentication_return" android:scheme="stripe-auth"/>
                <data android:host="link-accounts" android:path="/cn.eeo.classin/success" android:scheme="stripe-auth"/>
                <data android:host="link-accounts" android:path="/cn.eeo.classin/cancel" android:scheme="stripe-auth"/>
                <data android:host="native-redirect" android:pathPrefix="/cn.eeo.classin" android:scheme="stripe-auth"/>
                <data android:host="auth-redirect" android:pathPrefix="/cn.eeo.classin" android:scheme="stripe"/>
            </intent-filter>
        </activity>
        <activity android:exported="false" android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity" android:theme="@style/StripeDefaultTheme"/>
        <activity android:exported="false" android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity" android:theme="@style/StripeDefaultTheme" android:windowSoftInputMode="adjustResize"/>
        <provider android:authorities="cn.eeo.classin.financialconnections-init" android:exported="false" android:multiprocess="true" android:name="com.stripe.android.financialconnections.appinitializer.FinancialConnectionsInitializer"/>
        <activity android:name="com.paypal.authcore.authentication.TokenActivity"/>
        <activity android:exported="false" android:launchMode="singleTask" android:name="com.paypal.openid.AuthorizationManagementActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <activity android:exported="false" android:name="com.stripe.android.ui.core.cardscan.CardScanActivity" android:theme="@style/StripePaymentSheetDefaultTheme"/>
        <activity android:exported="false" android:name="com.stripe.android.view.AddPaymentMethodActivity" android:theme="@style/StripeDefaultTheme"/>
        <activity android:exported="false" android:name="com.stripe.android.view.PaymentMethodsActivity" android:theme="@style/StripeDefaultTheme"/>
        <activity android:exported="false" android:name="com.stripe.android.view.PaymentFlowActivity" android:theme="@style/StripeDefaultTheme"/>
        <activity android:exported="false" android:name="com.stripe.android.view.PaymentAuthWebViewActivity" android:theme="@style/StripeDefaultTheme" android:windowSoftInputMode="adjustResize"/>
        <activity android:exported="false" android:name="com.stripe.android.view.PaymentRelayActivity" android:theme="@style/StripeTransparentTheme"/>
        <activity android:exported="false" android:launchMode="singleTask" android:name="com.stripe.android.payments.StripeBrowserLauncherActivity" android:theme="@style/StripeTransparentTheme"/>
        <activity android:exported="true" android:launchMode="singleTask" android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity" android:theme="@style/StripeTransparentTheme">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="payment_return_url" android:path="/cn.eeo.classin" android:scheme="stripesdk"/>
            </intent-filter>
        </activity>
        <activity android:exported="false" android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity" android:theme="@style/StripeDefaultTheme"/>
        <activity android:exported="false" android:name="com.stripe.android.googlepaylauncher.StripeGooglePayActivity" android:theme="@style/StripeGooglePayDefaultTheme"/>
        <activity android:exported="false" android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity" android:theme="@style/StripeGooglePayDefaultTheme"/>
        <activity android:exported="false" android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity" android:theme="@style/StripeGooglePayDefaultTheme"/>
        <activity android:exported="false" android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity" android:theme="@style/PayLauncherDefaultTheme"/>
        <activity android:exported="false" android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity" android:theme="@style/StripeTransparentTheme"/>
        <activity android:exported="false" android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity" android:theme="@style/Stripe3DS2Theme"/>
        <activity android:excludeFromRecents="true" android:exported="false" android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <service android:exported="true" android:name="com.google.android.gms.auth.api.signin.RevocationBoundService" android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION" android:visibleToInstantApps="true"/>
        <activity android:exported="false" android:name="com.google.android.gms.common.api.GoogleApiActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <activity android:exported="true" android:name="androidx.compose.ui.tooling.PreviewActivity"/>
        <provider android:authorities="cn.eeo.classin.SentryPerformanceProvider" android:exported="false" android:initOrder="200" android:name="io.sentry.android.core.SentryPerformanceProvider"/>
        <provider android:authorities="cn.eeo.classin.androidx-startup" android:exported="false" android:name="androidx.startup.InitializationProvider">
            <meta-data android:name="androidx.emoji2.text.EmojiCompatInitializer" android:value="androidx.startup"/>
            <meta-data android:name="androidx.lifecycle.ProcessLifecycleInitializer" android:value="androidx.startup"/>
            <meta-data android:name="androidx.profileinstaller.ProfileInstallerInitializer" android:value="androidx.startup"/>
        </provider>
        <uses-library android:name="androidx.window.extensions" android:required="false"/>
        <uses-library android:name="androidx.window.sidecar" android:required="false"/>
        <meta-data android:name="com.google.android.gms.version" android:value="@integer/google_play_services_version"/>
        <activity android:exported="true" android:name="com.xuexiang.xpush.huawei.DeeplinkActivity" android:theme="@android:style/Theme.Translucent">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="cn.eeo.classin" android:path="/deeplink" android:scheme="eeoscheme"/>
            </intent-filter>
        </activity>
        <service android:exported="false" android:name="com.xuexiang.xpush.huawei.HuaweiPushReceiver">
            <intent-filter>
                <action android:name="com.huawei.push.action.MESSAGING_EVENT"/>
            </intent-filter>
        </service>
        <meta-data android:name="XPush_HuaweiPush_1002" android:value="@string/xpush_huawei_client_name"/>
        <activity android:exported="true" android:launchMode="singleTask" android:name="cn.eeo.xpush.honor.DeeplinkActivity" android:theme="@android:style/Theme.Translucent">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="cn.eeo.classin.honor.push" android:path="/deeplink" android:scheme="eeoscheme"/>
            </intent-filter>
        </activity>
        <service android:exported="false" android:name="cn.eeo.xpush.honor.MyHonorMsgService">
            <intent-filter>
                <action android:name="com.hihonor.push.action.MESSAGING_EVENT"/>
            </intent-filter>
        </service>
        <meta-data android:name="com.hihonor.push.app_id" android:value="104414586"/>
        <service android:exported="true" android:name="cn.eeo.xpush.oppo.CompatibleDataMessageService" android:permission="com.coloros.mcs.permission.SEND_MCS_MESSAGE">
            <intent-filter>
                <action android:name="com.coloros.mcs.action.RECEIVE_MCS_MESSAGE"/>
            </intent-filter>
        </service>
        <service android:exported="true" android:name="cn.eeo.xpush.oppo.DataMessageService" android:permission="com.heytap.mcs.permission.SEND_PUSH_MESSAGE">
            <intent-filter>
                <action android:name="com.heytap.mcs.action.RECEIVE_MCS_MESSAGE"/>
                <action android:name="com.heytap.msp.push.RECEIVE_MCS_MESSAGE"/>
            </intent-filter>
        </service>
        <activity android:excludeFromRecents="true" android:exported="true" android:name="cn.eeo.xpush.oppo.OPPOPushActivity">
            <intent-filter>
                <action android:name="cn.eeo.classin.push.internal"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <service android:exported="true" android:name="com.vivo.push.sdk.service.CommandClientService"/>
        <activity android:exported="false" android:name="com.vivo.push.sdk.LinkProxyClientActivity" android:screenOrientation="portrait" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <receiver android:exported="true" android:name="cn.eeo.xpush.vivo.VIVOPushReceiver">
            <intent-filter>
                <action android:name="com.vivo.pushclient.action.RECEIVE"/>
            </intent-filter>
        </receiver>
        <activity android:enabled="true" android:excludeFromRecents="true" android:exported="true" android:launchMode="singleInstance" android:name="com.xiaomi.mipush.sdk.NotificationClickedActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <meta-data android:name="supportStyle" android:value="scene|voip"/>
        </activity>
        <receiver android:exported="true" android:name="com.xuexiang.xpush.xiaomi.XiaoMiPushReceiver">
            <intent-filter>
                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.MESSAGE_ARRIVED"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.ERROR"/>
            </intent-filter>
        </receiver>
        <service android:enabled="true" android:exported="false" android:name="com.xiaomi.push.service.XMJobService" android:permission="android.permission.BIND_JOB_SERVICE" android:process=":pushservice"/>
        <service android:enabled="true" android:name="com.xiaomi.push.service.XMPushService" android:process=":pushservice"/>
        <service android:enabled="true" android:exported="true" android:name="com.xiaomi.mipush.sdk.PushMessageHandler" android:permission="com.xiaomi.xmsf.permission.MIPUSH_RECEIVE"/>
        <service android:enabled="true" android:name="com.xiaomi.mipush.sdk.MessageHandleService"/>
        <receiver android:exported="true" android:name="com.xiaomi.push.service.receivers.NetworkStatusReceiver">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </receiver>
        <receiver android:exported="false" android:name="com.xiaomi.push.service.receivers.PingReceiver" android:process=":pushservice">
            <intent-filter>
                <action android:name="com.xiaomi.push.PING_TIMER"/>
            </intent-filter>
        </receiver>
        <meta-data android:name="XPush_MIPush_1003" android:value="@string/xpush_xiaomi_client_name"/>
        <receiver android:exported="false" android:name="cn.eeo.epush.EeoPushReceiver">
            <intent-filter>
                <action android:name="com.xuexiang.xpush.core.action.RECEIVE_CONNECT_STATUS_CHANGED"/>
                <action android:name="com.xuexiang.xpush.core.action.RECEIVE_NOTIFICATION"/>
                <action android:name="com.xuexiang.xpush.core.action.RECEIVE_NOTIFICATION_CLICK"/>
                <action android:name="com.xuexiang.xpush.core.action.RECEIVE_MESSAGE"/>
                <action android:name="com.xuexiang.xpush.core.action.RECEIVE_COMMAND_RESULT"/>
                <category android:name="cn.eeo.classin"/>
            </intent-filter>
        </receiver>
        <receiver android:exported="false" android:name="com.xuexiang.xpush.core.receiver.impl.XPushReceiver">
            <intent-filter>
                <action android:name="com.xuexiang.xpush.core.action.RECEIVE_CONNECT_STATUS_CHANGED"/>
                <action android:name="com.xuexiang.xpush.core.action.RECEIVE_NOTIFICATION"/>
                <action android:name="com.xuexiang.xpush.core.action.RECEIVE_NOTIFICATION_CLICK"/>
                <action android:name="com.xuexiang.xpush.core.action.RECEIVE_MESSAGE"/>
                <action android:name="com.xuexiang.xpush.core.action.RECEIVE_COMMAND_RESULT"/>
                <category android:name="cn.eeo.classin"/>
            </intent-filter>
        </receiver>
        <service android:enabled="true" android:exported="true" android:foregroundServiceType="mediaProjection" android:name="com.eeo.screenrecoder.service.ScreenService" android:process=":ScreenService">
            <intent-filter>
                <action android:name="com.eeo.screenrecoder.action.init"/>
            </intent-filter>
        </service>
        <activity android:configChanges="keyboardHidden|layoutDirection|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:excludeFromRecents="true" android:launchMode="singleTask" android:name="com.eeo.screenrecoder.permission.EEOCapturePermissionActivity" android:screenOrientation="landscape" android:theme="@style/AppTheme.Transparent"/>
        <service android:exported="false" android:foregroundServiceType="microphone" android:name="com.eeo.audiotoolkit.services.AudioRecordService"/>
        <meta-data android:name="cn.eeo.initialize.SimpleInitializer" android:value="cn.eeo.initialize"/>
        <service android:directBootAware="true" android:exported="false" android:name="androidx.room.MultiInstanceInvalidationService"/>
        <receiver android:directBootAware="false" android:enabled="true" android:exported="true" android:name="androidx.profileinstaller.ProfileInstallReceiver" android:permission="android.permission.DUMP">
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION"/>
            </intent-filter>
        </receiver>
        <provider android:authorities="cn.eeo.classin.com.squareup.picasso" android:exported="false" android:name="com.squareup.picasso.PicassoProvider"/>
        <activity android:exported="true" android:name="com.xuexiang.xpush.core.XPushNotificationClickActivity" android:theme="@android:style/Theme.Translucent">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <data android:host="com.xuexiang.xpush" android:path="/notification" android:scheme="xpush"/>
            </intent-filter>
        </activity>
        <activity android:configChanges="orientation|screenSize" android:exported="false" android:name="com.sina.weibo.sdk.web.WebActivity" android:windowSoftInputMode="adjustResize"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:exported="true" android:launchMode="singleTask" android:name="com.sina.weibo.sdk.share.ShareTransActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter>
                <action android:name="com.sina.weibo.sdk.action.ACTION_SDK_REQ_ACTIVITY"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <provider android:authorities="cn.eeo.classin.fileprovider" android:exported="false" android:grantUriPermissions="true" android:name="com.sina.weibo.sdk.content.FileProvider">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/filepaths"/>
        </provider>
        <activity android:configChanges="density|fontScale|keyboard|keyboardHidden|layoutDirection|locale|navigation|orientation|screenLayout|screenSize|smallestScreenSize" android:exported="false" android:name="com.alipay.sdk.app.H5PayActivity" android:theme="@android:style/Theme.NoTitleBar"/>
        <activity android:configChanges="density|fontScale|keyboard|keyboardHidden|layoutDirection|locale|navigation|orientation|screenLayout|screenSize|smallestScreenSize" android:exported="false" android:name="com.alipay.sdk.app.H5AuthActivity" android:theme="@android:style/Theme.NoTitleBar"/>
        <activity android:configChanges="density|fontScale|keyboard|keyboardHidden|layoutDirection|locale|navigation|orientation|screenLayout|screenSize|smallestScreenSize" android:exported="true" android:launchMode="singleInstance" android:name="com.alipay.sdk.app.PayResultActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <activity android:configChanges="density|fontScale|keyboard|keyboardHidden|layoutDirection|locale|navigation|orientation|screenLayout|screenSize|smallestScreenSize" android:exported="true" android:launchMode="singleTask" android:name="com.alipay.sdk.app.AlipayResultActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <activity android:configChanges="density|fontScale|keyboard|keyboardHidden|layoutDirection|locale|navigation|orientation|screenLayout|screenSize|smallestScreenSize" android:exported="false" android:name="com.alipay.sdk.app.H5OpenAuthActivity" android:screenOrientation="behind" android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity android:configChanges="density|fontScale|keyboard|keyboardHidden|layoutDirection|locale|navigation|orientation|screenLayout|screenSize|smallestScreenSize" android:exported="false" android:name="com.alipay.sdk.app.APayEntranceActivity" android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <meta-data android:name="huawei_module_scankit_local" android:value="10320300"/>
        <meta-data android:name="huawei_module_scankit_sdk_version" android:value="scan:2.12.0.301"/>
        <meta-data android:name="com.huawei.hms.client.service.name:scan" android:value="scan:2.12.0.301"/>
        <meta-data android:name="com.huawei.hms.min_api_level:scan:huawei_module_scankit" android:value="1"/>
        <meta-data android:name="com.huawei.hms.min_api_level:scan:hmscore" android:value="1"/>
        <activity android:configChanges="navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:launchMode="singleTask" android:name="com.huawei.hms.hmsscankit.ScanKitActivity"/>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:launchMode="singleTop" android:name="com.cmic.gen.sdk.view.GenLoginAuthActivity" android:screenOrientation="unspecified" android:theme="@style/txTheme"/>
        <meta-data android:name="sdkVersion" android:value="3100"/>
        <meta-data android:name="MIPUSH_SDK_VERSION_CODE" android:value="50602"/>
        <meta-data android:name="MIPUSH_SDK_VERSION_NAME" android:value="5_6_2-C"/>
        <provider android:authorities="cn.eeo.classin.MLInitializerProvider" android:exported="false" android:name="com.huawei.hms.mlsdk.common.provider.MLInitializerProvider"/>
        <receiver android:directBootAware="true" android:exported="true" android:name="com.huawei.hms.support.api.push.PushMsgReceiver" android:permission="cn.eeo.classin.permission.PROCESS_PUSH_MSG">
            <intent-filter>
                <action android:name="com.huawei.intent.action.PUSH_DELAY_NOTIFY"/>
                <action android:name="com.huawei.intent.action.PUSH"/>
            </intent-filter>
        </receiver>
        <receiver android:directBootAware="true" android:exported="true" android:name="com.huawei.hms.support.api.push.PushReceiver" android:permission="cn.eeo.classin.permission.PROCESS_PUSH_MSG">
            <intent-filter>
                <action android:name="com.huawei.android.push.intent.REGISTRATION"/>
                <action android:name="com.huawei.android.push.intent.RECEIVE"/>
            </intent-filter>
        </receiver>
        <service android:directBootAware="true" android:enabled="true" android:exported="true" android:name="com.huawei.hms.support.api.push.service.HmsMsgService" android:process=":pushservice">
            <intent-filter>
                <action android:name="com.huawei.push.msg.NOTIFY_MSG"/>
                <action android:name="com.huawei.push.msg.PASSBY_MSG"/>
            </intent-filter>
        </service>
        <provider android:authorities="cn.eeo.classin.huawei.push.provider" android:exported="true" android:name="com.huawei.hms.support.api.push.PushProvider" android:readPermission="cn.eeo.classin.permission.PUSH_PROVIDER" android:writePermission="cn.eeo.classin.permission.PUSH_WRITE_PROVIDER"/>
        <meta-data android:name="com.huawei.hms.client.service.name:push" android:value="push:6.9.0.300"/>
        <meta-data android:name="com.huawei.hms.min_api_level:push:push" android:value="1"/>
        <activity android:exported="false" android:name="com.huawei.hms.support.api.push.TransActivity"/>
        <provider android:authorities="cn.eeo.classin.aaidinitprovider" android:exported="false" android:initOrder="500" android:name="com.huawei.hms.aaid.InitProvider"/>
        <meta-data android:name="com.huawei.hms.client.service.name:opendevice" android:value="opendevice:6.9.0.300"/>
        <meta-data android:name="com.huawei.hms.min_api_level:opendevice:push" android:value="1"/>
        <meta-data android:name="com.huawei.hms.client.service.name:base" android:value="base:6.8.0.300"/>
        <meta-data android:name="com.huawei.hms.min_api_level:base:hmscore" android:value="1"/>
        <meta-data android:name="availableLoaded" android:value="yes"/>
        <provider android:authorities="cn.eeo.classin.AGCInitializeProvider" android:exported="false" android:name="com.huawei.agconnect.core.provider.AGConnectInitializeProvider"/>
        <service android:exported="false" android:name="com.huawei.agconnect.core.ServiceDiscovery"/>
        <meta-data android:name="com.hihonor.push.sdk_version" android:value="7.0.61.303"/>
        <meta-data android:name="com.huawei.hms.client.service.name:dynamic-api" android:value="dynamic-api:1.0.24.300"/>
        <meta-data android:name="com.huawei.hms.min_api_level:dynamic-api:huawei_module_dynamicloader" android:value="10"/>
        <provider android:authorities="cn.eeo.classin.CCInitProvider" android:enabled="true" android:exported="true" android:name="com.cardinalcommerce.a.setShadowLayer"/>
        <activity android:exported="false" android:name="com.cardinalcommerce.a.setTextLocale" android:theme="@style/CardinalSDKTheme.ActionBar"/>
        <activity android:exported="false" android:name="com.cardinalcommerce.a.setTextLocales" android:theme="@style/CardinalSDKTheme.ActionBar" android:windowSoftInputMode="stateAlwaysHidden|adjustResize"/>
        <provider android:authorities="cn.eeo.classin.SensorsDataContentProvider" android:enabled="true" android:exported="false" android:name="com.sensorsdata.analytics.android.sdk.data.SensorsDataContentProvider"/>
        <meta-data android:name="com.sensorsdata.analytics.android.MainProcessName" android:value="cn.eeo.classin"/>
        <meta-data android:name="com.sensorsdata.analytics.android.version" android:value="0.2.2"/>
        <meta-data android:name="aia-compat-api-min-version" android:value="1"/>
        <activity android:configChanges="fontScale|layoutDirection|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:excludeFromRecents="true" android:exported="false" android:hardwareAccelerated="true" android:name="com.huawei.hms.activity.BridgeActivity" android:screenOrientation="behind" android:theme="@style/Base_Translucent">
            <meta-data android:name="hwc-theme" android:value="androidhwext:style/Theme.Emui.Translucent"/>
        </activity>
        <activity android:configChanges="keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize" android:exported="false" android:name="com.huawei.hms.activity.EnableServiceActivity"/>
        <meta-data android:name="io.sentry.gradle-plugin-integrations" android:value="AppStartInstrumentation,ComposeInstrumentation,DatabaseInstrumentation,FileIOInstrumentation,LogcatInstrumentation,OkHttpInstrumentation"/>
        <meta-data android:name="com.huawei.hms.client.appid" android:value="appid=100317109"/>
    </application>
</manifest>
