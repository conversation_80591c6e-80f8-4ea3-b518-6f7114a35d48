<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>错误页面</title>
  <style>
    body {
        margin: 0;
        height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #ffffff;
        font-family: 'Segoe UI', sans-serif;
    }

    .container {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

<!--    .error-icon {-->
<!--        width: 80px;-->
<!--        height: 80px;-->
<!--        margin-bottom: 16px;-->
<!--        /* 此处需要替换为实际错误图标 */-->
<!--        background: url('data:image/svg+xml;utf8,<svg ...></svg>') no-repeat center;-->
<!--    }-->

    .error-text {
        color: #939393;
        font-size: 14px;
        margin-bottom: 40px;
        text-align: center;
    }

    .reload-button {
        width: 155px;
        height: 36px;
        background-color: #2EE066;
        color: #1a1a1a;
        border: none;
        border-radius: 18px;
        font-size: 15px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .reload-button:hover {
        background-color: #45a049;
    }
  </style>
</head>
<body>
<div class="container">
  <div class="error-icon"></div>
  <p class="error-text" id="error-message"></p>
  <button class="reload-button" id="reload-button"></button>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
      // 语言配置字典
      const i18n = {
          'zh-CN': {
              error: '页面存在错误，请刷新后重试',
              reload: '刷新'
          },
          'en': {
              error: 'Page error. Please refresh the page and try again.',
              reload: 'Refresh'
          },
          'zh-TW': { // 繁体中文
              error: '頁面存在錯誤，請重新整理後重試',
              reload: '重新整理'
          },
          'es': { // 西班牙语
              error: 'Error en la página. Actualiza e inténtalo de nuevo',
              reload: 'Recarga'
          },
          'ja': { // 日语
              error: 'ページにエラーがあります。更新してもう一度お試しください',
              reload: 'リフレッシュ'
          },
          'vi': { // 越南语
              error: 'Lỗi trang, vui lòng làm mới trang và thử lại',
              reload: 'Làm mới'
          },
          'id': { // 印度尼西亚语
              error: 'Terdapat kesalahan pada halaman. Silakan perbarui halaman dan coba lagi',
              reload: 'Refresh'
          },
          'ar': { // 阿拉伯语
              error: 'حدث خطأ في الصفحة، يرجى التحديث والمحاولة مرة أخرى',
              reload: 'تحديث',
              rtl: true
          },
          'fr': { // 法语
              error: 'Une erreur s\'est produite sur la page, veuillez actualiser et réessayer',
              reload: 'Actualiser'
          },
          'ru': { // 俄语
              error: 'На странице произошла ошибка, обновите страницу и повторите попытку',
              reload: 'Обновить'
          },
          'pt': { // 葡萄牙语
              error: 'Houve um erro na página, atualize e tente novamente',
              reload: 'Atualizar'
          },
          'ko': { // 韩语
              error: '페이지에 오류가 있습니다. 새로고침하고 다시 시도하세요',
              reload: '새로 고침'
          }
      };

      // 解析URL参数获取originalUrl
      const getQueryParam = (name) => {
          const urlParams = new URLSearchParams(window.location.search);
          return urlParams.get(name) || '';
      };

      // 获取语言参数
      const params = new URLSearchParams(window.location.search);
      const lang = params.get('lang') || 'en';

      const originalUrl = decodeURIComponent(getQueryParam('originalUrl'));

      // 绑定按钮点击事件
      document.querySelector('.reload-button').addEventListener('click', function() {
          if(originalUrl) {
              // 通过自定义协议触发原生回调
              window.location.href = `app://reload?url=${encodeURIComponent(originalUrl)}`;
          } else {
              window.location.reload();
          }
      });

      document.getElementById('error-message').textContent = i18n[lang].error;
      document.getElementById('reload-button').textContent = i18n[lang].reload;
  });
</script>
</body>
</html>