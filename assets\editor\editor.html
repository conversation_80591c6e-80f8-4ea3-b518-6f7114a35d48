<!doctype html><html lang="en"><head><meta charset="utf-8"/><title>eeo editor</title><meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport"/><link rel="prerender" href="./editor.css" as="style"/><link rel="prerender" href="./tinymce/skins/ui/oxide/skin.min.css" as="style"/><link rel="prerender" href="./tinymce/skins/ui/oxide/content.inline.min.css" as="style"/><link rel="preload" href="./tinymce/tinymce.min.js" as="script"/><link rel="preload" href="./tinymce/models/dom/model.min.js" as="script"/><link rel="preload" href="./tinymce/icons/default/icons.min.js" as="script"/><link rel="preload" href="./tinymce/plugins/wordcount/plugin.min.js" as="script"/><link rel="preload" href="./tinymce/themes/silver/theme.min.js" as="script"/><link rel="prerender" href="./editor.js" as="script"/><link href="./editor.css" rel="stylesheet"></head><body><div id="root"></div><script src="./tinymce/tinymce.min.js" defer="defer"></script><script>window.quill={};function handlePromise(promiseList){return promiseList.map(function(promise){return promise.then(function(res){return({status:"ok",res:res})},function(err){return({status:"not ok",err:err})})})}Promise.allSettled=function(promiseList){return Promise.all(handlePromise(promiseList))};</script><script defer="defer" src="./editor.js"></script></body></html>