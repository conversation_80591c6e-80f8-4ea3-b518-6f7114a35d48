{"nm": "å®ç¨¿å¨ç»", "ddd": 0, "h": 40, "w": 40, "meta": {"g": "LottieFiles Figma v51"}, "layers": [{"ty": 4, "nm": "Rectangle 34626395", "sr": 1, "st": 0, "op": 61.12, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.1, 4.52], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.1, 12.04], "t": 30}, {"s": [1.1, 4.52], "t": 60}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 30}, {"s": [100, 100], "t": 60}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [31.5, 20], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [31.5, 20], "t": 30}, {"s": [31.5, 20], "t": 60}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 30}, {"s": [0], "t": 60}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 30}, {"s": [100], "t": 60}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 7.93], [1.1, 9.03], [1.1, 9.03], [0, 7.93]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 22.98], [1.1, 24.08], [1.1, 24.08], [0, 22.98]]}], "t": 30}, {"s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 7.93], [1.1, 9.03], [1.1, 9.03], [0, 7.93]]}], "t": 60}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1137, 0.7255, 0.3294], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1137, 0.7255, 0.3294], "t": 30}, {"s": [0.1137, 0.7255, 0.3294], "t": 60}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 30}, {"s": [100], "t": 60}]}}], "ind": 1}, {"ty": 4, "nm": "Rectangle 34626394", "sr": 1, "st": 0, "op": 61.12, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.1, 3.02], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.1, 7.37], "t": 30}, {"s": [1.1, 3.02], "t": 60}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 30}, {"s": [100, 100], "t": 60}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8.5, 20], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8.5, 20], "t": 30}, {"s": [8.5, 20], "t": 60}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 30}, {"s": [0], "t": 60}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 30}, {"s": [100], "t": 60}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 4.93], [1.1, 6.03], [1.1, 6.03], [0, 4.93]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 13.64], [1.1, 14.74], [1.1, 14.74], [0, 13.64]]}], "t": 30}, {"s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 4.93], [1.1, 6.03], [1.1, 6.03], [0, 4.93]]}], "t": 60}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1137, 0.7255, 0.3294], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1137, 0.7255, 0.3294], "t": 30}, {"s": [0.1137, 0.7255, 0.3294], "t": 60}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 30}, {"s": [100], "t": 60}]}}], "ind": 2}, {"ty": 4, "nm": "Rectangle 34626393", "sr": 1, "st": 0, "op": 61.12, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.1, 10.43], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.1, 4.64], "t": 30}, {"s": [1.1, 10.43], "t": 60}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 30}, {"s": [100, 100], "t": 60}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [26.9, 20], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [26.9, 20], "t": 30}, {"s": [26.9, 20], "t": 60}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 30}, {"s": [0], "t": 60}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 30}, {"s": [100], "t": 60}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 19.76], [1.1, 20.86], [1.1, 20.86], [0, 19.76]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 8.18], [1.1, 9.28], [1.1, 9.28], [0, 8.18]]}], "t": 30}, {"s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 19.76], [1.1, 20.86], [1.1, 20.86], [0, 19.76]]}], "t": 60}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1137, 0.7255, 0.3294], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1137, 0.7255, 0.3294], "t": 30}, {"s": [0.1137, 0.7255, 0.3294], "t": 60}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 30}, {"s": [100], "t": 60}]}}], "ind": 3}, {"ty": 4, "nm": "Rectangle 34626392", "sr": 1, "st": 0, "op": 61.12, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.1, 6.37], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.1, 14.29], "t": 30}, {"s": [1.1, 6.37], "t": 60}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 30}, {"s": [100, 100], "t": 60}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [22.3, 20], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [22.3, 20], "t": 30}, {"s": [22.3, 20], "t": 60}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 30}, {"s": [0], "t": 60}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 30}, {"s": [100], "t": 60}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 11.64], [1.1, 12.74], [1.1, 12.74], [0, 11.64]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 27.48], [1.1, 28.58], [1.1, 28.58], [0, 27.48]]}], "t": 30}, {"s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 11.64], [1.1, 12.74], [1.1, 12.74], [0, 11.64]]}], "t": 60}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1137, 0.7255, 0.3294], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1137, 0.7255, 0.3294], "t": 30}, {"s": [0.1137, 0.7255, 0.3294], "t": 60}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 30}, {"s": [100], "t": 60}]}}], "ind": 4}, {"ty": 4, "nm": "Rectangle 34626391", "sr": 1, "st": 0, "op": 61.12, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.1, 14], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.1, 10.11], "t": 30}, {"s": [1.1, 14], "t": 60}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 30}, {"s": [100, 100], "t": 60}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17.7, 20], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [17.7, 20], "t": 30}, {"s": [17.7, 20], "t": 60}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 30}, {"s": [0], "t": 60}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 30}, {"s": [100], "t": 60}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 26.9], [1.1, 28], [1.1, 28], [0, 26.9]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 19.11], [1.1, 20.21], [1.1, 20.21], [0, 19.11]]}], "t": 30}, {"s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 26.9], [1.1, 28], [1.1, 28], [0, 26.9]]}], "t": 60}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1137, 0.7255, 0.3294], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1137, 0.7255, 0.3294], "t": 30}, {"s": [0.1137, 0.7255, 0.3294], "t": 60}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 30}, {"s": [100], "t": 60}]}}], "ind": 5}, {"ty": 4, "nm": "Rectangle 34626390", "sr": 1, "st": 0, "op": 61.12, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.1, 8.77], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.1, 2.69], "t": 30}, {"s": [1.1, 8.77], "t": 60}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 30}, {"s": [100, 100], "t": 60}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [13.1, 20], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [13.1, 20], "t": 30}, {"s": [13.1, 20], "t": 60}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 30}, {"s": [0], "t": 60}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 30}, {"s": [100], "t": 60}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 16.44], [1.1, 17.54], [1.1, 17.54], [0, 16.44]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 4.28], [1.1, 5.38], [1.1, 5.38], [0, 4.28]]}], "t": 30}, {"s": [{"c": true, "i": [[0, 0], [-0.61, 0], [0, 0], [0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61]], "o": [[0, -0.61], [0, 0], [0.61, 0], [0, 0], [0, 0.61], [0, 0], [-0.61, 0], [0, 0]], "v": [[0, 1.1], [1.1, 0], [1.1, 0], [2.2, 1.1], [2.2, 16.44], [1.1, 17.54], [1.1, 17.54], [0, 16.44]]}], "t": 60}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1137, 0.7255, 0.3294], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1137, 0.7255, 0.3294], "t": 30}, {"s": [0.1137, 0.7255, 0.3294], "t": 60}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 30}, {"s": [100], "t": 60}]}}], "ind": 6}], "v": "5.7.0", "fr": 60, "op": 60.12, "ip": 0, "assets": []}