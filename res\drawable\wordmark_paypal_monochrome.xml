<?xml version="1.0" encoding="utf-8"?>
<vector android:height="20.0dip" android:width="79.0dip" android:viewportWidth="79.0" android:viewportHeight="20.0"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillColor="#ffffffff" android:pathData="M74.6485,4.5144L72.8897,15.6643C72.8556,15.8803 73.0232,16.0756 73.2425,16.0756L75.0113,16.0756C75.3044,16.0756 75.5538,15.8632 75.5995,15.5747L77.3336,4.6252C77.3679,4.4091 77.2003,4.2137 76.9808,4.2137L75.0013,4.2137C74.8254,4.2137 74.6758,4.3413 74.6485,4.5144" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ffffffff" android:pathData="M38.8065,12.1437C38.6083,13.3121 37.6777,14.0963 36.4906,14.0963C35.8953,14.0963 35.419,13.9055 35.1126,13.5444C34.8091,13.1864 34.6948,12.6763 34.7911,12.1082C34.9758,10.9502 35.9215,10.1407 37.0907,10.1407C37.6733,10.1407 38.1465,10.3332 38.4587,10.6978C38.773,11.0649 38.8965,11.5785 38.8065,12.1437M41.6661,8.1637L39.6142,8.1637C39.4384,8.1637 39.2888,8.2911 39.2614,8.4643L39.1713,9.036L39.0278,8.8289C38.5833,8.1862 37.593,7.9714 36.604,7.9714C34.3372,7.9714 32.4005,9.6833 32.0236,12.0842C31.8275,13.282 32.106,14.4268 32.7874,15.2258C33.4137,15.9598 34.3074,16.2654 35.3718,16.2654C37.1991,16.2654 38.2126,15.0954 38.2126,15.0954L38.1211,15.6639C38.0866,15.88 38.2542,16.0756 38.4739,16.0756L40.3216,16.0756C40.6146,16.0756 40.864,15.8631 40.9098,15.5749L42.019,8.5752C42.0532,8.3591 41.8857,8.1637 41.6661,8.1637" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ffffffff" android:pathData="M59.9164,8.2128C59.6822,9.7456 58.5074,9.7456 57.3711,9.7456L56.7247,9.7456L57.1781,6.8837C57.2056,6.7108 57.3553,6.5834 57.5309,6.5834L57.8273,6.5834C58.6008,6.5834 59.3315,6.5834 59.708,7.0225C59.9333,7.2852 60.0015,7.6746 59.9164,8.2128M59.422,4.2138L55.1366,4.2138C54.8437,4.2138 54.5941,4.4262 54.5486,4.7147L52.8157,15.6642C52.7814,15.8801 52.9491,16.0755 53.1685,16.0755L55.3671,16.0755C55.5723,16.0755 55.7469,15.9268 55.7789,15.7249L56.2702,12.6208C56.3158,12.3323 56.5653,12.1199 56.8584,12.1199L58.2144,12.1199C61.0369,12.1199 62.666,10.7586 63.0916,8.0606C63.2833,6.8807 63.0993,5.9537 62.5451,5.3046C61.9355,4.5911 60.8554,4.2138 59.422,4.2138" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ffffffff" android:pathData="M52.5949,8.1637L50.5325,8.1637C50.3353,8.1637 50.1507,8.2612 50.04,8.424L47.1948,12.5994L45.9892,8.5869C45.9137,8.3359 45.6818,8.1637 45.4188,8.1637L43.3913,8.1637C43.1465,8.1637 42.9744,8.4038 43.0533,8.6347L45.3244,15.2774L43.1881,18.2808C43.0206,18.5164 43.1897,18.8425 43.4797,18.8425L45.5401,18.8425C45.7354,18.8425 45.9183,18.7471 46.0295,18.5873L52.8886,8.7226C53.0527,8.4866 52.8831,8.1637 52.5949,8.1637" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ffffffff" android:pathData="M69.3703,12.1437C69.1721,13.3121 68.2413,14.0963 67.0543,14.0963C66.459,14.0963 65.9827,13.9055 65.6763,13.5444C65.3728,13.1864 65.2586,12.6763 65.3549,12.1082C65.5396,10.9502 66.4852,10.1407 67.6544,10.1407C68.237,10.1407 68.7102,10.3332 69.0224,10.6978C69.3367,11.0649 69.4602,11.5785 69.3703,12.1437M72.2299,8.1637L70.178,8.1637C70.0022,8.1637 69.8525,8.2911 69.8252,8.4643L69.7348,9.036L69.5915,8.8289C69.147,8.1862 68.1567,7.9714 67.1678,7.9714C64.901,7.9714 62.9642,9.6833 62.5873,12.0842C62.3913,13.282 62.6696,14.4268 63.3512,15.2258C63.9775,15.9598 64.8709,16.2654 65.9356,16.2654C67.7629,16.2654 68.7763,15.0954 68.7763,15.0954L68.6846,15.6639C68.6503,15.88 68.8179,16.0756 69.0375,16.0756L70.8854,16.0756C71.1783,16.0756 71.4277,15.8631 71.4736,15.5749L72.5827,8.5752C72.617,8.3591 72.4494,8.1637 72.2299,8.1637" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ffffffff" android:pathData="M29.3526,8.2128C29.1184,9.7456 27.9436,9.7456 26.8075,9.7456L26.161,9.7456L26.6144,6.8837C26.6417,6.7108 26.7915,6.5834 26.9672,6.5834L27.2636,6.5834C28.037,6.5834 28.7677,6.5834 29.1443,7.0225C29.3696,7.2852 29.4378,7.6746 29.3526,8.2128M28.8581,4.2138L24.5729,4.2138C24.2799,4.2138 24.0303,4.4262 23.9847,4.7147L22.2519,15.6642C22.2179,15.8801 22.3854,16.0755 22.6048,16.0755L24.6509,16.0755C24.9438,16.0755 25.1932,15.8631 25.2389,15.5748L25.7065,12.6208C25.7521,12.3323 26.0015,12.1199 26.2947,12.1199L27.6507,12.1199C30.4731,12.1199 32.1023,10.7586 32.5279,8.0606C32.7195,6.8807 32.5356,5.9537 31.9813,5.3046C31.3718,4.5911 30.2917,4.2138 28.8581,4.2138" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#b3ffffff" android:pathData="M14.4409,4.7872C14.6716,3.3215 14.4393,2.3241 13.6437,1.4208C12.7678,0.4261 11.1853,-0 9.1607,-0L3.284,-0C2.8703,-0 2.5178,0.3 2.4535,0.7074L0.0062,16.1707C-0.0419,16.4759 0.1946,16.7519 0.5047,16.7519L4.1327,16.7519L3.8822,18.3342C3.84,18.6012 4.047,18.8426 4.3182,18.8426L7.3762,18.8426C7.7383,18.8426 8.0464,18.5801 8.1028,18.2237L8.1327,18.0688L8.7089,14.4285L8.7461,14.2274C8.8023,13.8712 9.1105,13.6086 9.4726,13.6086L9.93,13.6086C12.8926,13.6086 15.2124,12.4093 15.8901,8.9406C16.1734,7.4912 16.0269,6.2813 15.2779,5.4307C15.0514,5.1734 14.7694,4.9607 14.4409,4.7872" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#bfffffff" android:pathData="M14.4409,4.7872C14.6716,3.3215 14.4393,2.3241 13.6437,1.4208C12.7678,0.4261 11.1853,0 9.1607,0L3.284,0C2.8703,0 2.5178,0.3 2.4535,0.7074L0.0062,16.1707C-0.0419,16.4759 0.1946,16.7519 0.5047,16.7519L4.1327,16.7519L5.0441,10.9929L5.0158,11.1735C5.0803,10.7663 5.4297,10.4663 5.8433,10.4663L7.5676,10.4663C10.9542,10.4663 13.606,9.0952 14.3808,5.1302C14.4037,5.0127 14.4233,4.8989 14.4409,4.7872" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
    <path android:fillColor="#ffffffff" android:pathData="M6.0231,4.8067C6.0618,4.5616 6.2197,4.3611 6.4321,4.2597C6.5286,4.2135 6.6365,4.1879 6.7497,4.1879L11.3563,4.1879C11.902,4.1879 12.4108,4.2235 12.8761,4.2981C13.009,4.3194 13.1384,4.344 13.264,4.3718C13.3897,4.3996 13.5116,4.4307 13.6299,4.4651C13.6888,4.4822 13.747,4.5004 13.804,4.5192C14.0324,4.5948 14.2452,4.6839 14.4409,4.7873C14.6716,3.3214 14.4392,2.324 13.6438,1.4207C12.7679,0.4261 11.1853,0.0001 9.1608,0.0001L3.284,0.0001C2.8701,0.0001 2.5179,0.3 2.4534,0.7073L0.0063,16.1708C-0.0421,16.476 0.1946,16.7519 0.5047,16.7519L4.1328,16.7519L5.0441,10.993L6.0231,4.8067Z" android:strokeColor="#00000000" android:strokeWidth="1.0" android:fillType="evenOdd" />
</vector>
