<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="ActivityTransparentStyle" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>
    <style name="AppFullScreenTheme" parent="@style/Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowActionBar">false</item>
    </style>
    <style name="AppTheme.Transparent" parent="@style/AppTheme.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>
    <style name="Base.Theme.SplashScreen" parent="@style/Base.v27.Theme.SplashScreen" />
    <style name="Base.Theme.SplashScreen.Light" parent="@style/Base.v27.Theme.SplashScreen.Light" />
    <style name="PYPLAppTheme" parent="@style/Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowActionBar">false</item>
        <item name="bottomSheetDialogTheme">@style/PYPLBottomSheetDialogTheme</item>
    </style>
    <style name="Base.v27.Theme.SplashScreen" parent="@style/Base.v21.Theme.SplashScreen">
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <style name="Base.v27.Theme.SplashScreen.Light" parent="@style/Base.v21.Theme.SplashScreen.Light">
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
</resources>
