
# ClassIn TS解密模板
import binascii
from Crypto.Cipher import AES

def decrypt_classin_ts(encrypted_data, key_hex, iv_hex):
    """解密ClassIn TS分段"""
    key = binascii.unhexlify(key_hex)
    iv = binascii.unhexlify(iv_hex)
    
    cipher = AES.new(key, AES.MODE_CBC, iv)
    decrypted = cipher.decrypt(encrypted_data)
    
    # 验证TS文件头
    if len(decrypted) > 0 and decrypted[0] == 0x47:
        return decrypted
    else:
        return None

# 使用示例
# encrypted_ts = open('encrypted.ts', 'rb').read()
# key_hex = "4912a924461c98dcb08d5bbd4a0cc541"  # 替换为正确的密钥
# iv_hex = "********************************"
# 
# decrypted = decrypt_classin_ts(encrypted_ts, key_hex, iv_hex)
# if decrypted:
#     with open('decrypted.ts', 'wb') as f:
#         f.write(decrypted)
#     print("解密成功！")
# else:
#     print("解密失败！")
