if (!window.MathJax) {
  // amsmath 包是一个 LaTeX 必须掌握的宏集
  // 而\boldsymbol的渲染效果是加粗的斜体字符。 mathbf{a}为正体
  // mhchem 化学公式宏包
  // AMSsymbols各种命令符号
  // 这个 extpfeil 扩展添加了更多用于生成可扩展箭头的宏，包括 \xtwoheadrightarrow ， \xtwoheadleftarrow ， \xmapsto ， \xlongequal ， \xtofrom ，以及非标准 \Newextarrow 创建自己的可扩展箭头

  window.MathJax = {
    tex: {
      inlineMath: [["$", "$"], ["\\(", "\\)"]],   //行内公式选择符
      displayMath: [["$$", "$$"], ["\\[", "\\]"]],   //段内公式选择符
      packages: ['base', 'ams', 'mhchem', 'textmacros', 'color', 'extpfeil', 'amscd'],
    },
    options: {
      enableMenu: false, // 隐藏右键菜单
      skipHtmlTags: ["script", "noscript", "style", "textarea", "pre", "code", "a", 'img', 'video', 'svg', 'table', 'tr', 'td'],   //避开某些标签
      ignoreHtmlClass: "comment-content",
      processHtmlClass: 'tex2jax_process',
    },
    loader: {
      load: ['[tex]/mhchem', '[tex]/textmacros', '[tex]/color', '[tex]/extpfeil', '[tex]/amscd']
    },
    startup: { typeset: false }
  }
}

// 2023.07.29 14:23分 ios 客户端针对 woff 文件审核不通过调整成https访问地址
var eeoConfigMobile = {
  spareCn: ["a0s-cdn.eeo.cn", "t0s-cdn.eeo.cn", "b0s-cdn.eeo.cn", "wseqcb011.eeo.cn"], // lms
  spareEn: ["a1s-cdn.eeo.cn", "t1s-cdn.eeo.cn", "b1s-cdn.eeo.cn", "wseqef011.eeo.cn", "wseqah011.eeo.cn", "wseqng011.eeo.cn"], // lms
};

var host = "";// 默认国内域名
var staticHost = "static.eeo.cn"; // 默认国内静态域名
if (window.lmsWidget && window.lmsWidget.clientInfo) {
  var clientInfo = window.lmsWidget.clientInfo() || {};
  host = clientInfo.hosts && clientInfo.hosts.static || "";
}

if (host && eeoConfigMobile.spareEn.indexOf(host) != -1) {
  staticHost = "static.classin.com"; //海外静态静态域名
}

/* var script = document.createElement('script');
script.src = "https://" + staticHost + "/assets/polyfill/polyfill.min.js";
script.async = true;
document.head.appendChild(script); */

var scriptSvg = document.createElement('script');
scriptSvg.src = "https://" + staticHost + "/assets/lib/mathjax/es5/tex-chtml.js";
scriptSvg.async = true;
document.head.appendChild(scriptSvg);