#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ClassIn M3U8流媒体加密分析专用工具
专门分析HLS流媒体的加密机制和解密方法
"""

import os
import re
import struct
import binascii
from urllib.parse import urljoin
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

class ClassInM3U8Analyzer:
    """ClassIn M3U8流媒体加密分析器"""
    
    def __init__(self):
        print("ClassIn M3U8流媒体加密分析工具")
        print("=" * 50)
        
        # ClassIn可能使用的加密密钥
        self.potential_keys = [
            b'classin_hls_2023',      # 16字节
            b'eeo_stream_key_v19',    # 16字节  
            b'cn.eeo.classin.hls',    # 16字节
            b'hls_encrypt_key_01',    # 16字节
            b'eeo_video_stream',      # 14字节，需要填充
            b'classin_video',         # 11字节，需要填充
        ]
        
        # 标准化密钥长度为16字节（AES-128）
        self.normalized_keys = []
        for key in self.potential_keys:
            if len(key) < 16:
                normalized_key = key + b'\x00' * (16 - len(key))
            elif len(key) > 16:
                normalized_key = key[:16]
            else:
                normalized_key = key
            self.normalized_keys.append(normalized_key)
    
    def analyze_m3u8_encryption(self, m3u8_content):
        """分析M3U8文件的加密配置"""
        print("\n=== M3U8加密分析 ===")
        
        encryption_info = {
            'is_encrypted': False,
            'method': None,
            'key_uri': None,
            'iv': None,
            'custom_encryption': False
        }
        
        # 1. 检查标准HLS加密
        key_pattern = r'#EXT-X-KEY:(.+)'
        key_matches = re.findall(key_pattern, m3u8_content)
        
        if key_matches:
            print("✓ 发现标准HLS加密配置")
            encryption_info['is_encrypted'] = True
            
            for key_line in key_matches:
                print(f"  加密配置: {key_line}")
                
                # 解析METHOD
                method_match = re.search(r'METHOD=([^,\s]+)', key_line)
                if method_match:
                    encryption_info['method'] = method_match.group(1)
                    print(f"  加密方法: {encryption_info['method']}")
                
                # 解析URI
                uri_match = re.search(r'URI="([^"]+)"', key_line)
                if uri_match:
                    encryption_info['key_uri'] = uri_match.group(1)
                    print(f"  密钥URI: {encryption_info['key_uri']}")
                
                # 解析IV
                iv_match = re.search(r'IV=0x([A-Fa-f0-9]+)', key_line)
                if iv_match:
                    encryption_info['iv'] = iv_match.group(1)
                    print(f"  初始向量: {encryption_info['iv']}")
        
        # 2. 检查自定义加密标识
        custom_patterns = [
            (r'#EEO-ENCRYPT', 'EEO自定义加密'),
            (r'#CLASSIN-KEY', 'ClassIn密钥标识'),
            (r'#CUSTOM-ENCRYPT', '自定义加密'),
            (r'\.crypt', '加密文件扩展名'),
            (r'encrypted=true', '加密标志'),
            (r'#EEO-KEY:', 'EEO密钥标签'),
        ]
        
        for pattern, description in custom_patterns:
            if re.search(pattern, m3u8_content, re.IGNORECASE):
                print(f"✓ 发现自定义加密: {description}")
                encryption_info['custom_encryption'] = True
                encryption_info['is_encrypted'] = True
        
        # 3. 分析分段文件
        segments = self.extract_segments(m3u8_content)
        print(f"\n发现 {len(segments)} 个视频分段:")
        
        encrypted_segments = 0
        for i, segment in enumerate(segments[:5]):  # 只显示前5个
            print(f"  分段 {i+1}: {segment}")
            if '.crypt' in segment or 'encrypted' in segment.lower():
                encrypted_segments += 1
        
        if len(segments) > 5:
            print(f"  ... 还有 {len(segments) - 5} 个分段")
        
        if encrypted_segments > 0:
            print(f"✓ 发现 {encrypted_segments} 个可能的加密分段")
            encryption_info['is_encrypted'] = True
        
        return encryption_info
    
    def extract_segments(self, m3u8_content):
        """提取M3U8中的分段文件列表"""
        segments = []
        lines = m3u8_content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            # 跳过注释行和空行
            if line and not line.startswith('#'):
                segments.append(line)
        
        return segments
    
    def demonstrate_decryption_methods(self):
        """演示不同的解密方法"""
        print("\n=== 解密方法演示 ===")
        
        # 模拟加密的TS分段数据
        sample_data = b"This is a sample TS segment data for testing encryption/decryption methods."
        
        print("1. AES-128 CBC解密演示:")
        for i, key in enumerate(self.normalized_keys[:3]):
            print(f"  尝试密钥 {i+1}: {key.hex()}")
            
            # 使用零IV进行演示
            iv = b'\x00' * 16
            
            try:
                # 模拟解密过程
                cipher = AES.new(key, AES.MODE_CBC, iv)
                
                # 为了演示，我们先加密再解密
                padded_data = sample_data + b'\x00' * (16 - len(sample_data) % 16)
                encrypted = cipher.encrypt(padded_data)
                
                # 解密
                decrypt_cipher = AES.new(key, AES.MODE_CBC, iv)
                decrypted = decrypt_cipher.decrypt(encrypted)
                
                print(f"    解密成功: {decrypted[:50]}...")
                
            except Exception as e:
                print(f"    解密失败: {e}")
        
        print("\n2. 自定义解密方法:")
        print("  - XOR解密 (密钥: 0x10)")
        xor_key = 0x10
        xor_result = bytes(b ^ xor_key for b in sample_data[:20])
        print(f"    XOR结果: {xor_result}")
        
        print("  - 组合解密 (AES + XOR)")
        print("    先AES解密，再XOR处理")
    
    def generate_analysis_report(self, m3u8_content):
        """生成完整的分析报告"""
        print("\n" + "=" * 60)
        print("ClassIn M3U8流媒体加密分析报告")
        print("=" * 60)
        
        # 基本信息
        lines = m3u8_content.strip().split('\n')
        print(f"M3U8文件行数: {len(lines)}")
        
        # 版本信息
        version_match = re.search(r'#EXT-X-VERSION:(\d+)', m3u8_content)
        if version_match:
            print(f"HLS版本: {version_match.group(1)}")
        
        # 目标时长
        duration_match = re.search(r'#EXT-X-TARGETDURATION:(\d+)', m3u8_content)
        if duration_match:
            print(f"目标分段时长: {duration_match.group(1)}秒")
        
        # 加密分析
        encryption_info = self.analyze_m3u8_encryption(m3u8_content)
        
        # 安全评估
        print(f"\n=== 安全评估 ===")
        if encryption_info['is_encrypted']:
            print("✓ 流媒体已加密")
            
            if encryption_info['method'] == 'AES-128':
                print("✓ 使用AES-128加密（标准HLS加密）")
                print("  - 安全级别: 中等")
                print("  - 破解难度: 需要获取密钥")
            elif encryption_info['custom_encryption']:
                print("✓ 使用自定义加密")
                print("  - 安全级别: 取决于实现")
                print("  - 破解难度: 需要逆向分析")
            
            if encryption_info['key_uri']:
                print(f"  - 密钥获取方式: HTTP请求 ({encryption_info['key_uri']})")
                print("  - 风险: 密钥传输可能被拦截")
            
        else:
            print("⚠ 流媒体未加密")
            print("  - 安全级别: 低")
            print("  - 风险: 内容可直接访问")
        
        # 建议
        print(f"\n=== 分析建议 ===")
        if encryption_info['is_encrypted']:
            print("1. 尝试拦截密钥请求")
            print("2. 分析客户端解密逻辑")
            print("3. 查找硬编码密钥")
            print("4. 使用动态调试工具Hook解密函数")
        else:
            print("1. 直接下载分段文件")
            print("2. 使用ffmpeg合并分段")
        
        return encryption_info

def main():
    """主函数 - 演示M3U8分析功能"""
    analyzer = ClassInM3U8Analyzer()
    
    # 示例1: 标准HLS加密的M3U8
    print("\n【示例1: 标准HLS加密】")
    sample_encrypted_m3u8 = """#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXT-X-KEY:METHOD=AES-128,URI="https://example.com/key.bin",IV=0x12345678901234567890123456789012
#EXTINF:10.0,
segment001.ts
#EXTINF:10.0,
segment002.ts
#EXTINF:10.0,
segment003.ts
#EXT-X-ENDLIST"""
    
    analyzer.generate_analysis_report(sample_encrypted_m3u8)
    
    # 示例2: 自定义加密的M3U8
    print("\n\n【示例2: 自定义加密】")
    sample_custom_m3u8 = """#EXTM3U
#EXT-X-VERSION:3
#EEO-ENCRYPT:METHOD=CUSTOM,KEY=embedded
#EXT-X-TARGETDURATION:10
#EXTINF:10.0,
segment001.ts.crypt
#EXTINF:10.0,
segment002.ts.crypt
#EXT-X-ENDLIST"""
    
    analyzer.generate_analysis_report(sample_custom_m3u8)
    
    # 解密方法演示
    analyzer.demonstrate_decryption_methods()
    
    print("\n" + "=" * 60)
    print("分析完成！")
    print("\n实际使用时，请将真实的M3U8内容传入analyze_m3u8_encryption()方法")

if __name__ == "__main__":
    main()
