<?xml version="1.0" encoding="utf-8"?>
<selector
  xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="false" android:state_checked="false" android:color="?colorOnSurface" android:alpha="@dimen/m3_comp_switch_disabled_unselected_handle_opacity" />
    <item android:state_enabled="false" android:state_checked="true" android:color="?colorSurface" android:alpha="@dimen/m3_comp_switch_disabled_selected_handle_opacity" />
    <item android:state_checked="true" android:state_pressed="true" android:color="?colorPrimaryContainer" />
    <item android:state_checked="true" android:color="?colorOnPrimary" />
    <item android:state_pressed="true" android:color="?colorOnSurfaceVariant" />
    <item android:color="?colorOutline" />
</selector>
